<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 測試頁面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 API 連接測試</h1>
    
    <div class="test-section">
        <h2>📋 當前配置</h2>
        <div id="config-info">載入中...</div>
    </div>

    <div class="test-section">
        <h2>🌐 API 測試</h2>
        <button onclick="testGetAssets()">測試 getAssets API</button>
        <button onclick="testDirectRequest()">測試直接請求</button>
        <button onclick="testProxyRequest()">測試代理請求</button>
        <div id="test-results"></div>
    </div>

    <script>
        // 顯示當前配置
        async function showConfig() {
            try {
                const response = await fetch('/config.json');
                const config = await response.json();
                document.getElementById('config-info').innerHTML = `
                    <pre>${JSON.stringify(config.api.gateway, null, 2)}</pre>
                    <p><strong>環境:</strong> ${import.meta.env ? 'Vite Dev' : 'Production'}</p>
                    <p><strong>當前域:</strong> ${window.location.origin}</p>
                `;
            } catch (error) {
                document.getElementById('config-info').innerHTML = `
                    <div class="error">載入配置失敗: ${error.message}</div>
                `;
            }
        }

        // 測試 getAssets API
        async function testGetAssets() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<p>🔄 測試中...</p>';

            try {
                const testData = {
                    tenant_id: 'R20230704-0001',
                    service_id: 'dbc2cd12-3d42-1bbe-6728-0b03b2c19440',
                    user_id: '00000000-0000-0000-1002-000000000001'
                };

                const response = await fetch('/svc/ams.svc/attachments/getAssets', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.text();
                
                resultsDiv.innerHTML = `
                    <div class="${response.ok ? 'success' : 'error'}">
                        <h3>✅ getAssets 測試結果</h3>
                        <p><strong>狀態:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>URL:</strong> ${response.url}</p>
                        <pre>${result}</pre>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ getAssets 測試失敗</h3>
                        <p><strong>錯誤:</strong> ${error.message}</p>
                        <p><strong>類型:</strong> ${error.name}</p>
                    </div>
                `;
            }
        }

        // 測試直接請求到 ngrok
        async function testDirectRequest() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<p>🔄 測試直接請求...</p>';

            try {
                const testData = {
                    tenant_id: 'R20230704-0001',
                    service_id: 'dbc2cd12-3d42-1bbe-6728-0b03b2c19440',
                    user_id: '00000000-0000-0000-1002-000000000001'
                };

                const response = await fetch('https://ai.aile.cloud/v2/svc/ams.svc/attachments/getAssets', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'ngrok-skip-browser-warning': 'true'
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.text();
                
                resultsDiv.innerHTML = `
                    <div class="${response.ok ? 'success' : 'error'}">
                        <h3>🌐 直接請求測試結果</h3>
                        <p><strong>狀態:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>URL:</strong> ${response.url}</p>
                        <pre>${result}</pre>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ 直接請求測試失敗 (可能是 CORS 問題)</h3>
                        <p><strong>錯誤:</strong> ${error.message}</p>
                        <p><strong>類型:</strong> ${error.name}</p>
                    </div>
                `;
            }
        }

        // 測試代理請求
        async function testProxyRequest() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<p>🔄 測試代理請求...</p>';

            try {
                const testData = {
                    tenant_id: 'R20230704-0001',
                    service_id: 'dbc2cd12-3d42-1bbe-6728-0b03b2c19440',
                    user_id: '00000000-0000-0000-1002-000000000001'
                };

                const response = await fetch('/svc/ams.svc/attachments/getAssets', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.text();
                
                resultsDiv.innerHTML = `
                    <div class="${response.ok ? 'success' : 'error'}">
                        <h3>🔄 代理請求測試結果</h3>
                        <p><strong>狀態:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>URL:</strong> ${response.url}</p>
                        <pre>${result}</pre>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ 代理請求測試失敗</h3>
                        <p><strong>錯誤:</strong> ${error.message}</p>
                        <p><strong>類型:</strong> ${error.name}</p>
                    </div>
                `;
            }
        }

        // 頁面載入時顯示配置
        showConfig();
    </script>
</body>
</html>
