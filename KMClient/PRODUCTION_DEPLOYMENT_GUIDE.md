# 🚀 生產環境部署指南

## 📋 已修復的配置問題

### ✅ API baseUrl 配置修正
- **問題**: 配置文件中的 `api.gateway.baseUrl` 為空字符串
- **修復**: 更新為生產環境 URL `https://ai.aile.cloud/v2`
- **影響文件**: 
  - `public/config.json`
  - `dist/config.json`

### ✅ 環境設定更新
- **環境**: `development` → `production`
- **熱重載**: 已禁用 (`enabled: false`)
- **日誌級別**: `info` → `warn`
- **控制台日誌**: 已禁用 (`enableConsole: false`)

### ✅ 頭像服務配置確認
- **獨立 baseUrl**: `https://newaile.prod.aile.cloud` ✅
- **配置隔離**: 不受主 API 配置影響 ✅

## 🔧 配置文件對比

### 修復前 (錯誤配置)
```json
{
  "app": {
    "environment": "development"
  },
  "api": {
    "gateway": {
      "baseUrl": ""  // ❌ 空字符串導致錯誤
    }
  },
  "features": {
    "hotReload": {
      "enabled": true  // ❌ 生產環境不需要
    }
  },
  "logging": {
    "level": "info",
    "enableConsole": true  // ❌ 生產環境過多日誌
  }
}
```

### 修復後 (正確配置)
```json
{
  "app": {
    "environment": "production"  // ✅
  },
  "api": {
    "gateway": {
      "baseUrl": "https://ai.aile.cloud/v2"  // ✅
    }
  },
  "features": {
    "hotReload": {
      "enabled": false  // ✅
    }
  },
  "logging": {
    "level": "warn",
    "enableConsole": false  // ✅
  },
  "avatar": {
    "baseUrl": "https://newaile.prod.aile.cloud"  // ✅ 獨立配置
  }
}
```

## 🚀 部署步驟

### 1. 構建應用程式
```bash
npm run build
```

### 2. 驗證配置
檢查 `dist/config.json` 確保：
- ✅ `api.gateway.baseUrl`: `"https://ai.aile.cloud/v2"`
- ✅ `app.environment`: `"production"`
- ✅ `features.hotReload.enabled`: `false`
- ✅ `avatar.baseUrl`: `"https://newaile.prod.aile.cloud"`

### 3. Firebase 部署
```bash
firebase deploy
```

## 🔍 API 請求驗證

### 正確的 API 請求格式
```
POST https://ai.aile.cloud/v2/svc/ams.svc/attachments/getAssets
```

### 錯誤的請求格式 (已修復)
```
❌ POST https://aile-ai-product.firebaseapp.com/svc/ams.svc/attachments/getAssets
```

## 🧪 測試驗證

### 1. 開發者工具檢查
1. 打開瀏覽器開發者工具
2. 切換到 Network 標籤
3. 重新載入頁面
4. 檢查 API 請求是否指向 `https://ai.aile.cloud/v2`

### 2. 功能測試
- ✅ 附件管理功能正常運作
- ✅ 頭像設置功能正常運作
- ✅ 聊天功能正常運作
- ✅ 系統指令設置正常運作

## 🔧 故障排除

### 如果仍然出現錯誤的 URL
1. 清除瀏覽器緩存
2. 確認 Firebase 部署成功
3. 檢查 `dist/config.json` 是否正確更新
4. 重新構建並部署：
   ```bash
   npm run build
   firebase deploy
   ```

### 如果 API 請求失敗
1. 檢查網路連接
2. 確認 `https://ai.aile.cloud/v2` 服務正常
3. 檢查瀏覽器控制台錯誤訊息
4. 驗證 URL 參數格式正確

## 📊 配置架構

```
KMClient 應用程式
├── 主 API 配置
│   ├── baseUrl: https://ai.aile.cloud/v2
│   ├── 服務: tenants, ams, omnichannel, line, km
│   └── 端點: 動態構建 /svc/{service}/{path}{endpoint}
└── 頭像 API 配置 (獨立)
    ├── baseUrl: https://newaile.prod.aile.cloud
    ├── endpoint: /job/api/platform/servicenumber/update
    └── 加密: HMAC-SHA256
```

## ✅ 部署檢查清單

- [x] 更新 API baseUrl 為生產環境
- [x] 設定環境為 production
- [x] 禁用開發功能 (熱重載、詳細日誌)
- [x] 確認頭像服務獨立配置
- [x] 執行構建並驗證 dist/config.json
- [x] Firebase 部署
- [x] 功能測試驗證

部署完成後，所有 API 請求都會正確指向生產環境！🎉
