<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多文件上傳調試工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .upload-area:hover {
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            background-color: #e3f2fd;
            border-color: #1976d2;
        }
        .file-input {
            display: none;
        }
        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .file-info {
            background: #f0f8ff;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }
        .error {
            background: #ffe8e8;
            border-left-color: #f44336;
            color: #d32f2f;
        }
        .success {
            background: #e8f5e8;
            border-left-color: #4caf50;
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 多文件上傳調試工具</h1>
        
        <div class="section">
            <div class="section-title">📁 文件選擇</div>
            <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                <p>📤 點擊選擇文件或拖拽文件到此處</p>
                <p>支持多文件選擇</p>
            </div>
            <input type="file" id="fileInput" class="file-input" multiple>
            
            <button onclick="simulateUpload()" id="uploadBtn" disabled>模擬上傳</button>
            <button onclick="clearLogs()">清除日誌</button>
            <button onclick="testFormData()">測試 FormData</button>
        </div>

        <div class="section">
            <div class="section-title">📋 選中的文件</div>
            <div id="fileList">尚未選擇文件</div>
        </div>

        <div class="section">
            <div class="section-title">🔍 content_type 分析</div>
            <div id="contentTypeAnalysis">等待文件選擇...</div>
        </div>

        <div class="section">
            <div class="section-title">📊 調試日誌</div>
            <div id="logArea" class="log-area">等待操作...</div>
        </div>
    </div>

    <script>
        let selectedFiles = [];

        // 日誌函數
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logArea.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 清除日誌
        function clearLogs() {
            document.getElementById('logArea').textContent = '';
        }

        // 文件選擇處理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            selectedFiles = Array.from(e.target.files);
            log(`用戶選擇了 ${selectedFiles.length} 個文件`);
            
            updateFileList();
            updateContentTypeAnalysis();
            document.getElementById('uploadBtn').disabled = selectedFiles.length === 0;
        });

        // 更新文件列表顯示
        function updateFileList() {
            const fileListDiv = document.getElementById('fileList');
            
            if (selectedFiles.length === 0) {
                fileListDiv.innerHTML = '尚未選擇文件';
                return;
            }

            let html = '';
            selectedFiles.forEach((file, index) => {
                html += `
                    <div class="file-info">
                        <strong>${index + 1}. ${file.name}</strong><br>
                        類型: ${file.type || '(未知)'}<br>
                        大小: ${(file.size / 1024).toFixed(2)} KB<br>
                        最後修改: ${new Date(file.lastModified).toLocaleString()}
                    </div>
                `;
            });
            
            fileListDiv.innerHTML = html;
        }

        // 更新 content_type 分析
        function updateContentTypeAnalysis() {
            const analysisDiv = document.getElementById('contentTypeAnalysis');
            
            if (selectedFiles.length === 0) {
                analysisDiv.innerHTML = '等待文件選擇...';
                return;
            }

            // 生成 content_type 字符串
            const contentTypes = selectedFiles.map(file => file.type || 'application/octet-stream');
            const contentTypeString = contentTypes.join(',');

            log(`生成 content_type: ${contentTypeString}`);

            let html = `
                <div class="file-info">
                    <strong>文件數量:</strong> ${selectedFiles.length}<br>
                    <strong>content_type 字符串:</strong><br>
                    <code>${contentTypeString}</code>
                </div>
                <div class="file-info">
                    <strong>MIME 類型詳情:</strong><br>
            `;

            contentTypes.forEach((type, index) => {
                html += `${index + 1}. ${selectedFiles[index].name} → ${type}<br>`;
            });

            html += '</div>';
            analysisDiv.innerHTML = html;
        }

        // 測試 FormData 構建
        function testFormData() {
            if (selectedFiles.length === 0) {
                log('請先選擇文件', 'error');
                return;
            }

            log('開始測試 FormData 構建...');

            try {
                const formData = new FormData();
                const contentTypes = selectedFiles.map(file => file.type || 'application/octet-stream');
                const contentTypeString = contentTypes.join(',');

                // 添加文件
                selectedFiles.forEach((file, index) => {
                    formData.append('file', file);
                    log(`添加文件 ${index + 1}: ${file.name} (${file.type})`);
                });

                // 添加其他參數
                formData.append('tenant_id', 'R20230704-0001');
                formData.append('service_id', 'dbc2cd12-3d42-1bbe-6728-0b03b2c19440');
                formData.append('user_id', '00000000-0000-0000-1002-000000000001');
                formData.append('content_type', contentTypeString);

                log('FormData 構建成功');
                log('FormData 內容:');
                
                for (const [key, value] of formData.entries()) {
                    if (value instanceof File) {
                        log(`  ${key}: File(${value.name}, ${value.type}, ${value.size} bytes)`);
                    } else {
                        log(`  ${key}: ${value}`);
                    }
                }

                log('FormData 測試完成', 'success');
            } catch (error) {
                log(`FormData 測試失敗: ${error.message}`, 'error');
            }
        }

        // 模擬上傳
        function simulateUpload() {
            if (selectedFiles.length === 0) {
                log('請先選擇文件', 'error');
                return;
            }

            log('開始模擬上傳請求...');

            try {
                // 驗證文件
                selectedFiles.forEach((file, index) => {
                    if (!(file instanceof File)) {
                        throw new Error(`第 ${index + 1} 個文件不是有效的 File 對象`);
                    }
                    if (!file.name) {
                        throw new Error(`第 ${index + 1} 個文件沒有名稱`);
                    }
                });

                const contentTypes = selectedFiles.map(file => file.type || 'application/octet-stream');
                const contentTypeString = contentTypes.join(',');

                // 模擬請求對象
                const uploadRequest = {
                    file: selectedFiles,
                    tenant_id: 'R20230704-0001',
                    service_id: 'dbc2cd12-3d42-1bbe-6728-0b03b2c19440',
                    user_id: '00000000-0000-0000-1002-000000000001',
                    content_type: contentTypeString
                };

                log('上傳請求對象:');
                log(JSON.stringify({
                    fileCount: uploadRequest.file.length,
                    fileNames: uploadRequest.file.map(f => f.name),
                    tenant_id: uploadRequest.tenant_id,
                    service_id: uploadRequest.service_id,
                    user_id: uploadRequest.user_id,
                    content_type: uploadRequest.content_type
                }, null, 2));

                log('模擬上傳完成 - 請求格式正確', 'success');
                
                // 顯示會發送到後端的實際數據
                log('實際發送的 multipart/form-data 將包含:');
                selectedFiles.forEach((file, index) => {
                    log(`  file: ${file.name} (${file.size} bytes, ${file.type})`);
                });
                log(`  tenant_id: R20230704-0001`);
                log(`  service_id: dbc2cd12-3d42-1bbe-6728-0b03b2c19440`);
                log(`  user_id: 00000000-0000-0000-1002-000000000001`);
                log(`  content_type: ${contentTypeString}`);

            } catch (error) {
                log(`模擬上傳失敗: ${error.message}`, 'error');
            }
        }

        // 拖拽支持
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = Array.from(e.dataTransfer.files);
            selectedFiles = files;
            
            log(`通過拖拽選擇了 ${files.length} 個文件`);
            updateFileList();
            updateContentTypeAnalysis();
            document.getElementById('uploadBtn').disabled = files.length === 0;
        });

        // 初始化
        log('多文件上傳調試工具已載入');
        log('請選擇文件開始測試...');
    </script>
</body>
</html>
