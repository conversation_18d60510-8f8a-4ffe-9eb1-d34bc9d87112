"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.apiProxy = void 0;
const functions = __importStar(require("firebase-functions"));
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const http_proxy_middleware_1 = require("http-proxy-middleware");
// 初始化 Express 應用
const app = (0, express_1.default)();
// 啟用 CORS - 允許來自 Firebase Hosting 的請求
app.use((0, cors_1.default)({
    origin: [
        'https://aile-ai-product.web.app',
        'https://aile-ai-product.firebaseapp.com',
        'http://localhost:3001' // 開發環境
    ],
    credentials: true
}));
// API 代理配置
const apiProxyOptions = {
    target: 'https://ai.aile.cloud/v2',
    changeOrigin: true,
    secure: true,
    pathRewrite: {
        '^/api': '', // 移除 /api 前綴
    },
    onProxyReq: (proxyReq, req, res) => {
        console.log('🌐 Firebase Functions - Proxying request:', req.method, req.originalUrl);
        console.log('🎯 Target URL:', `https://ai.aile.cloud/v2${req.originalUrl.replace('/api', '')}`);
        // 確保正確的 Content-Type
        if (req.body && typeof req.body === 'object') {
            proxyReq.setHeader('Content-Type', 'application/json');
        }
    },
    onProxyRes: (proxyRes, req, res) => {
        console.log('✅ Firebase Functions - Received response:', proxyRes.statusCode, req.originalUrl);
    },
    onError: (err, req, res) => {
        console.error('🚫 Firebase Functions - Proxy error:', err.message);
        console.error('📍 Request URL:', req.originalUrl);
        res.status(500).json({
            error: 'Proxy Error',
            message: 'Failed to proxy request to target server',
            details: err.message
        });
    }
};
// 創建代理中間件
const proxyMiddleware = (0, http_proxy_middleware_1.createProxyMiddleware)(apiProxyOptions);
// 健康檢查端點
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'KM Client API Proxy'
    });
});
// API 代理路由 - 處理所有 /api/* 請求
app.use('/api', proxyMiddleware);
// 錯誤處理中間件
app.use((err, req, res, next) => {
    console.error('🚫 Firebase Functions - Unhandled error:', err);
    res.status(500).json({
        error: 'Internal Server Error',
        message: 'An unexpected error occurred',
        timestamp: new Date().toISOString()
    });
});
// 導出 Firebase Function
exports.apiProxy = functions.https.onRequest(app);
//# sourceMappingURL=index.js.map