{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^5.0.0", "express": "^4.18.2", "http-proxy-middleware": "^2.0.6", "cors": "^2.8.5"}, "devDependencies": {"typescript": "^4.9.0", "@types/express": "^4.17.17", "@types/cors": "^2.8.13"}, "private": true}