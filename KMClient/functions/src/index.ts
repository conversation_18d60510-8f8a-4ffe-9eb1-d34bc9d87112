import * as functions from 'firebase-functions';
import express from 'express';
import cors from 'cors';
import { createProxyMiddleware } from 'http-proxy-middleware';

// 初始化 Express 應用
const app = express();

// 啟用 CORS - 允許來自 Firebase Hosting 的請求
app.use(cors({
  origin: [
    'https://aile-ai-product.web.app',
    'https://aile-ai-product.firebaseapp.com',
    'http://localhost:3001' // 開發環境
  ],
  credentials: true
}));

// API 代理配置
const apiProxyOptions = {
  target: 'https://ai.aile.cloud/v2',
  changeOrigin: true,
  secure: true,
  pathRewrite: {
    '^/api': '', // 移除 /api 前綴
  },
  onProxyReq: (proxyReq: any, req: express.Request, res: express.Response) => {
    console.log('🌐 Firebase Functions - Proxying request:', req.method, req.originalUrl);
    console.log('🎯 Target URL:', `https://ai.aile.cloud/v2${req.originalUrl.replace('/api', '')}`);
    
    // 確保正確的 Content-Type
    if (req.body && typeof req.body === 'object') {
      proxyReq.setHeader('Content-Type', 'application/json');
    }
  },
  onProxyRes: (proxyRes: any, req: express.Request, res: express.Response) => {
    console.log('✅ Firebase Functions - Received response:', proxyRes.statusCode, req.originalUrl);
  },
  onError: (err: any, req: express.Request, res: express.Response) => {
    console.error('🚫 Firebase Functions - Proxy error:', err.message);
    console.error('📍 Request URL:', req.originalUrl);
    res.status(500).json({
      error: 'Proxy Error',
      message: 'Failed to proxy request to target server',
      details: err.message
    });
  }
};

// 創建代理中間件
const proxyMiddleware = createProxyMiddleware(apiProxyOptions);

// 健康檢查端點
app.get('/health', (req: express.Request, res: express.Response) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'KM Client API Proxy'
  });
});

// API 代理路由 - 處理所有 /api/* 請求
app.use('/api', proxyMiddleware);

// 錯誤處理中間件
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('🚫 Firebase Functions - Unhandled error:', err);
  res.status(500).json({
    error: 'Internal Server Error',
    message: 'An unexpected error occurred',
    timestamp: new Date().toISOString()
  });
});

// 導出 Firebase Function
export const apiProxy = functions.https.onRequest(app);
