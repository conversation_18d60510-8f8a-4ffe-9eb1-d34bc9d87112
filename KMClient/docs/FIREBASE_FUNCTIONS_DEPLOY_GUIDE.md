# Firebase Functions 手動部署指南

由於代理環境下 Firebase CLI 存在技術限制，請使用以下方法手動部署 Functions：

## 🎯 方法1：使用 Firebase Console（推薦）

### 步驟1：訪問 Firebase Console
1. 打開瀏覽器，訪問：https://console.firebase.google.com/
2. 選擇項目：`aile-419002`
3. 在左側菜單中點擊 "Functions"

### 步驟2：創建新函數
1. 點擊 "創建函數" 或 "Get started"
2. 選擇 "第2代" (Gen 2) 函數
3. 函數名稱：`apiProxy`
4. 地區：選擇 `asia-east1` 或 `us-central1`
5. 觸發器類型：HTTP
6. 允許未經身份驗證的調用：✅ 勾選

### 步驟3：上傳代碼
1. 在代碼編輯器中，將以下文件內容複製貼上：

**package.json:**
```json
{
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "start": "node index.js"
  },
  "engines": {
    "node": "18"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.0.0",
    "firebase-functions": "^5.0.0",
    "express": "^4.18.2",
    "http-proxy-middleware": "^2.0.6",
    "cors": "^2.8.5"
  }
}
```

**index.js:** (使用項目中的 `functions/index.js` 文件內容)

### 步驟4：部署
1. 點擊 "部署" 按鈕
2. 等待部署完成（通常需要2-5分鐘）
3. 記錄函數的 URL，格式類似：
   `https://asia-east1-aile-419002.cloudfunctions.net/apiProxy`

## 🎯 方法2：使用 Google Cloud Console

### 步驟1：訪問 Google Cloud Console
1. 打開：https://console.cloud.google.com/
2. 選擇項目：`aile-419002`
3. 導航到 "Cloud Functions"

### 步驟2：創建函數
1. 點擊 "創建函數"
2. 基本設置：
   - 函數名稱：`apiProxy`
   - 地區：`asia-east1`
   - 觸發器類型：HTTP
   - 允許未經身份驗證的調用：✅
3. 運行時設置：
   - 運行時：Node.js 18
   - 入口點：`apiProxy`

### 步驟3：上傳代碼
使用與方法1相同的 package.json 和 index.js 內容

## 🔧 部署後配置

### 1. 更新 Firebase Hosting 配置
確保 `firebase.json` 中的重寫規則指向正確的函數 URL：

```json
{
  "hosting": {
    "rewrites": [
      {
        "source": "/api/**",
        "function": "apiProxy"
      }
    ]
  }
}
```

### 2. 重新部署 Hosting
```bash
npm run build
firebase deploy --only hosting
```

## ✅ 驗證部署

### 1. 測試函數健康檢查
訪問：`https://your-function-url/health`
應該返回：
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "service": "KM Client API Proxy"
}
```

### 2. 測試完整應用
訪問：`https://aile-ai-product.web.app/?TenantID=R20230704-0001&ServiceID=dbc2cd12-3d42-1bbe-6728-0b03b2c19440&UserID=00000000-0000-0000-1002-000000000001`

檢查瀏覽器開發者工具中的網路請求，確認 API 調用正常工作。

## 🚨 注意事項

1. **函數冷啟動**：首次調用可能需要幾秒鐘
2. **CORS 配置**：函數已配置允許來自 Firebase Hosting 的請求
3. **錯誤監控**：可在 Firebase Console 的 Functions 日誌中查看錯誤
4. **成本考慮**：Functions 按調用次數和執行時間計費

## 🔄 更新函數

如需更新函數代碼：
1. 在 Firebase Console 中編輯函數
2. 更新代碼後點擊 "部署"
3. 或者重新創建函數（刪除舊的，創建新的）
