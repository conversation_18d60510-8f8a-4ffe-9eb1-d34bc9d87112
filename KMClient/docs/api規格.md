# KM Client API 規格文檔

## 聊天 API

### 統一聊天接口 (Omnichannel Chat)

**端點**: `POST /svc/brainHub.svc/omnichannel/chat`

**功能描述**: 支持文字、圖片和語音消息的統一聊天接口。

#### 請求格式

**Content-Type**: `application/json`

**請求參數**:

| 參數名 | 類型 | 必填 | 描述 |
|--------|------|------|------|
| `tenant_id` | string | 是 | 租戶 ID |
| `question` | string | 是 | 用戶問題或 Base64 編碼的媒體數據 |
| `message_type` | string | 否 | 消息類型：'text'、'b64_image'、'b64_voice' |
| `source` | string[] | 是 | 知識來源，通常為 ['*'] |
| `version` | string[] | 是 | 版本，通常為 ['*'] |
| `tags` | string[] | 是 | 標籤，通常為 ['*'] |
| `service_id` | string | 是 | 服務 ID |
| `user_id` | string | 是 | 用戶 ID |
| `session_id` | string | 是 | 會話 ID |
| `chat_mode` | string | 否 | 聊天模式：'general'、'knowledge_base'、'attachment' |
| `channel` | string | 否 | 渠道，默認為 'web' |

#### message_type 字段規格

- **數據類型**: `string`
- **可選值**:
  - `'text'`: 純文字消息（默認值）
  - `'b64_image'`: Base64 編碼的圖片（Data URI 格式）
  - `'b64_voice'`: Base64 編碼的語音（Data URI 格式）
- **用途**: 區分不同類型的消息內容

#### question 字段格式說明

根據 `message_type` 的不同，`question` 字段的格式也不同：

**文字消息** (`message_type: 'text'`):
- 格式：純文字字符串
- 範例：`"你好，請問有什麼可以幫助我的嗎？"`

**圖片消息** (`message_type: 'b64_image'`):
- 格式：完整的 Data URI 格式
- 結構：`data:{mimeType};base64,{base64Data}`
- 支援格式：PNG、JPG、JPEG、GIF、WebP
- 範例：`"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="`

**語音消息** (`message_type: 'b64_voice'`):
- 格式：完整的 Data URI 格式
- 結構：`data:{mimeType};base64,{base64Data}`
- 支援格式：WAV、WebM、MP3、OGG
- 範例：`"data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT"`

#### 消息類型示例

**文字消息**:
```json
{
  "tenant_id": "R20230704-0001",
  "question": "你好，請問有什麼可以幫助我的嗎？",
  "message_type": "text",
  "source": ["*"],
  "version": ["*"],
  "tags": ["*"],
  "service_id": "dbc2cd12-3d42-1bbe-6728-0b03b2c19440",
  "user_id": "00000000-0000-0000-1002-000000000001",
  "session_id": "session-123",
  "chat_mode": "general",
  "channel": "web"
}
```

**圖片消息**:
```json
{
  "tenant_id": "R20230704-0001",
  "question": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
  "message_type": "b64_image",
  "source": ["*"],
  "version": ["*"],
  "tags": ["*"],
  "service_id": "dbc2cd12-3d42-1bbe-6728-0b03b2c19440",
  "user_id": "00000000-0000-0000-1002-000000000001",
  "session_id": "session-123",
  "chat_mode": "general",
  "channel": "web"
}
```

**語音消息**:
```json
{
  "tenant_id": "R20230704-0001",
  "question": "data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT",
  "message_type": "b64_voice",
  "source": ["*"],
  "version": ["*"],
  "tags": ["*"],
  "service_id": "dbc2cd12-3d42-1bbe-6728-0b03b2c19440",
  "user_id": "00000000-0000-0000-1002-000000000001",
  "session_id": "session-123",
  "chat_mode": "general",
  "channel": "web"
}
```

#### 響應格式

```json
{
  "answer": "AI 回應內容",
  "session_id": "session-123",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 文件上傳 API

### 多文件上傳功能

**端點**: `POST /svc/ams.svc/upload`

**功能描述**: 支持單個或多個文件同時上傳，並在請求中包含每個文件的 MIME 類型信息。

#### 請求格式

**Content-Type**: `multipart/form-data`

**請求參數**:

| 參數名 | 類型 | 必填 | 描述 |
|--------|------|------|------|
| `file` | File[] | 是 | 要上傳的文件數組 |
| `tenant_id` | string | 是 | 租戶 ID |
| `service_id` | string | 是 | 服務 ID |
| `user_id` | string | 是 | 用戶 ID |
| `content_type` | string | 是 | 文件 MIME 類型字符串 |

#### content_type 字段規格

- **數據類型**: `string`
- **用途**: 表示每個上傳文件的 MIME 類型
- **格式**: 使用逗號分隔的字符串
- **順序要求**: 各個 MIME 類型的順序必須與上傳文件的順序完全一致

#### 示例

**單文件上傳**:
```
file: [document.pdf]
content_type: "application/pdf"
```

**多文件上傳**:
```
file: [file1.pdf, file2.md, file3.jpg]
content_type: "application/pdf,text/markdown,image/jpeg"
```

**對應關係**:
- 第1個文件 `file1.pdf` → 第1個 MIME 類型 `application/pdf`
- 第2個文件 `file2.md` → 第2個 MIME 類型 `text/markdown`
- 第3個文件 `file3.jpg` → 第3個 MIME 類型 `image/jpeg`

#### 響應格式

**成功響應**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "file_names": ["file1.pdf", "file2.md", "file3.jpg"]
  }
}
```

**錯誤響應**:
```json
{
  "code": 1,
  "message": "Upload failed: [error details]"
}
```

#### 前端實現

**TypeScript 類型定義**:
```typescript
export interface UploadFileRequest {
  file: File[];
  tenant_id: string;
  service_id: string;
  user_id: string;
  content_type: string;
}
```

**多文件上傳邏輯**:
```typescript
const handleMultipleUpload = async (files: File[]) => {
  // 生成 content_type 字符串，按文件順序用逗號分隔
  const contentTypes = files.map(file => file.type || 'application/octet-stream');
  const contentTypeString = contentTypes.join(',');

  await uploadFiles({
    file: files,
    tenant_id: userInfo.tenant_id,
    service_id: userInfo.service_id,
    user_id: userInfo.user_id,
    content_type: contentTypeString,
  });
};
```

## 其他附件 API

### YouTube 影片附件

**端點**: `POST /svc/ams.svc/attachments/setYoutubeLink`

**請求格式**:
```typescript
{
  tenant_id: string;
  service_id: string;
  user_id: string;
  YoutubeContents: Array<{
    youtube_link: string;
    remark: string;
  }>;
}
```

### 純文本附件

**端點**: `POST /svc/ams.svc/attachments/setPlainText`

**請求格式**:
```typescript
{
  tenant_id: string;
  service_id: string;
  user_id: string;
  content: string;
  remark?: string;
}
```

### 網站附件

**端點**: `POST /svc/ams.svc/attachments/setWebsiteLink`

**請求格式**:
```typescript
{
  tenant_id: string;
  service_id: string;
  user_id: string;
  website_links: string[];
}
```

## 實現狀態

### ✅ 已完成功能

1. **多文件上傳支持**
   - ✅ 支持同時選擇和上傳多個文件
   - ✅ 自動生成 `content_type` 字段
   - ✅ MIME 類型順序與文件順序保持一致
   - ✅ 單文件和多文件上傳兼容

2. **類型定義完整**
   - ✅ `UploadFileRequest` 接口定義
   - ✅ `content_type` 字段規格
   - ✅ TypeScript 類型安全

3. **前端組件實現**
   - ✅ `FileUploadTab.tsx` 支持多文件上傳
   - ✅ 文件選擇變化處理
   - ✅ 上傳狀態管理
   - ✅ 錯誤處理和日誌記錄

4. **工具函數**
   - ✅ `generateContentTypeString()` - 生成 content_type 字符串
   - ✅ `validateContentType()` - 驗證 content_type 格式
   - ✅ 測試工具函數

### 🧪 測試驗證

- ✅ 單文件上傳測試
- ✅ 多文件上傳測試
- ✅ 邊界情況測試（無 MIME 類型、數量不匹配等）
- ✅ content_type 字段格式驗證
- ✅ API 請求格式驗證

### 📁 相關文件

- `src/types/attachment.ts` - 類型定義
- `src/components/AttachmentPanel/FileUploadTab.tsx` - 前端組件
- `src/services/attachmentService.ts` - API 服務
- `src/utils/fileUploadUtils.ts` - 工具函數
- `test-content-type.html` - 測試頁面

---

**最後更新**: 2025-01-24
**版本**: 1.2.0 - 新增多文件上傳支持
