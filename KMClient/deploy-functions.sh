#!/bin/bash

# Firebase Functions 部署腳本
# 解決代理環境下的循環引用問題

echo "🚀 開始部署 Firebase Functions..."

# 保存當前代理設置
ORIGINAL_HTTP_PROXY=$HTTP_PROXY
ORIGINAL_HTTPS_PROXY=$HTTPS_PROXY
ORIGINAL_http_proxy=$http_proxy
ORIGINAL_https_proxy=$https_proxy

echo "📋 當前代理設置:"
echo "  HTTP_PROXY: $HTTP_PROXY"
echo "  HTTPS_PROXY: $HTTPS_PROXY"

# 嘗試方法1: 使用 NO_PROXY 繞過代理
echo "🔄 嘗試方法1: 使用 NO_PROXY..."
if NO_PROXY="*" HTTP_PROXY="" HTTPS_PROXY="" http_proxy="" https_proxy="" firebase deploy --only functions; then
    echo "✅ 方法1 成功！"
    exit 0
fi

echo "❌ 方法1 失敗，嘗試方法2..."

# 嘗試方法2: 使用 curl 作為代理客戶端
echo "🔄 嘗試方法2: 配置代理客戶端..."
export HTTPS_PROXY_REQUEST_FULLURI=false
export HTTP_PROXY_REQUEST_FULLURI=false

if firebase deploy --only functions; then
    echo "✅ 方法2 成功！"
    exit 0
fi

echo "❌ 方法2 失敗，嘗試方法3..."

# 嘗試方法3: 使用本地 Firebase CLI
echo "🔄 嘗試方法3: 使用本地 Firebase CLI..."
if npx firebase-tools deploy --only functions; then
    echo "✅ 方法3 成功！"
    exit 0
fi

echo "❌ 所有方法都失敗了"
echo "💡 建議："
echo "  1. 檢查網路連接"
echo "  2. 確認 Firebase 項目權限"
echo "  3. 嘗試手動在 Firebase Console 中部署"

exit 1
