# ========= Build stage =========
FROM node:20-alpine AS builder
WORKDIR /app
COPY . .
# Use clean, reproducible install
RUN npm ci && npm run build

# ========= Runtime stage (Nginx) =========
FROM nginx:1.27-alpine
# Copy built assets
COPY --from=builder /app/dist /usr/share/nginx/html

# Replace default site and add SPA + config.json no-cache rules
RUN rm -f /etc/nginx/conf.d/default.conf \
  && printf 'server {\n\
    listen 80;\n\
    server_name _;\n\
    root /usr/share/nginx/html;\n\
    index index.html;\n\
\n\
    # SPA routing: serve index.html for all non-file paths\n\
    location / {\n\
      try_files $uri $uri/ /index.html;\n\
    }\n\
\n\
    # Ensure config.json is never cached so hot-reload works reliably\n\
    location = /config.json {\n\
      add_header Cache-Control "no-cache, no-store, must-revalidate" always;\n\
      add_header Pragma "no-cache" always;\n\
      add_header Expires "0" always;\n\
      try_files $uri =404;\n\
    }\n\
\n\
    # Reverse proxy for API to avoid CORS\n\
    location /api/svc/ {\n\
      proxy_pass https://ai.aile.cloud/v2/svc/;\n\
      proxy_set_header Host $host;\n\
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n\
      proxy_set_header X-Forwarded-Proto $scheme;\n\
      proxy_set_header X-Real-IP $remote_addr;\n\
      proxy_ssl_verify off;\n\
    }\n\
  }\n' > /etc/nginx/conf.d/km-client.conf

EXPOSE 80

