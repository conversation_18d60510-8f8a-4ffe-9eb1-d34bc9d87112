<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多文件上傳測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
        }
        .file-info {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .result {
            background: #e8f5e8;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            border-left: 4px solid #4caf50;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>多文件上傳測試</h1>
    <p>此頁面用於測試多文件上傳功能中的 content_type 字段生成邏輯。</p>
    
    <div class="upload-area">
        <input type="file" id="fileInput" multiple accept="*/*">
        <p>選擇多個文件來測試 content_type 字段生成</p>
    </div>
    
    <button onclick="simulateUpload()">模擬上傳</button>
    <button onclick="clearResults()">清除結果</button>
    
    <div id="results"></div>

    <script>
        function simulateUpload() {
            const fileInput = document.getElementById('fileInput');
            const files = Array.from(fileInput.files);
            const resultsDiv = document.getElementById('results');
            
            if (files.length === 0) {
                alert('請先選擇文件');
                return;
            }
            
            // 生成 content_type 字符串
            const contentTypes = files.map(file => file.type || 'application/octet-stream');
            const contentTypeString = contentTypes.join(',');
            
            // 顯示結果
            const resultHTML = `
                <div class="result">
                    <h3>📤 多文件上傳模擬結果</h3>
                    <p><strong>文件數量:</strong> ${files.length}</p>
                    <p><strong>文件列表:</strong></p>
                    <ul>
                        ${files.map((file, index) => `
                            <li>
                                <strong>${index + 1}. ${file.name}</strong><br>
                                大小: ${(file.size / 1024).toFixed(2)} KB<br>
                                MIME 類型: ${file.type || 'application/octet-stream'}
                            </li>
                        `).join('')}
                    </ul>
                    <p><strong>生成的 content_type 字段:</strong></p>
                    <div class="file-info">
                        <code>${contentTypeString}</code>
                    </div>
                    <p><strong>API 請求模擬:</strong></p>
                    <div class="file-info">
                        <pre>{
  "file": [${files.map(f => `"${f.name}"`).join(', ')}],
  "tenant_id": "R20230704-0001",
  "service_id": "dbc2cd12-3d42-1bbe-6728-0b03b2c19440",
  "user_id": "00000000-0000-0000-1002-000000000001",
  "content_type": "${contentTypeString}"
}</pre>
                    </div>
                </div>
            `;
            
            resultsDiv.innerHTML = resultHTML;
            
            // 在控制台輸出詳細信息
            console.log('📤 多文件上傳測試:', {
                fileCount: files.length,
                fileNames: files.map(f => f.name),
                contentTypes: contentTypes,
                contentTypeString: contentTypeString,
                apiRequest: {
                    file: files,
                    tenant_id: "R20230704-0001",
                    service_id: "dbc2cd12-3d42-1bbe-6728-0b03b2c19440",
                    user_id: "00000000-0000-0000-1002-000000000001",
                    content_type: contentTypeString
                }
            });
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('fileInput').value = '';
        }
        
        // 文件選擇變化時的處理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            console.log('📁 用戶選擇了文件:', {
                totalFiles: files.length,
                fileNames: files.map(f => f.name),
                fileTypes: files.map(f => f.type)
            });
        });
    </script>
</body>
</html>
