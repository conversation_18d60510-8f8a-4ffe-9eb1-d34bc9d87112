# 🧠 KM Client - 知識管理客戶端

基於 amsBrainClient 專案創建的全新知識管理客戶端，專注於系統指令設置和智慧聊天功能。

## ✨ 主要特色

### 🎯 核心功能
- **左右分欄佈局** - 經典的雙欄設計：左側附件管理，右側智慧聊天
- **系統指令設置** - 獨立的系統指令配置頁面，支援全局指令和服務特定指令
- **URL 參數傳遞** - 支援透過 URL 查詢參數傳遞租戶編碼（必要）和其他可選參數
- **配置文件管理** - 使用 JSON 配置文件管理 API 設置，支援熱重載
- **現代化 UI/UX** - 提亮的色彩主題，保持科技感的同時提升可讀性

### 🚀 技術特色
- **React 19** + **TypeScript** - 現代化前端技術棧
- **Ant Design 5** - 企業級 UI 組件庫
- **Tailwind CSS** - 實用優先的 CSS 框架
- **Zustand** - 輕量級狀態管理
- **Vite** - 快速的構建工具
- **熱重載** - 配置文件變更自動生效

## 🛠️ 安裝與運行

### 環境要求
- Node.js 18+
- npm 或 yarn

### 安裝依賴
```bash
cd KMClient
npm install
```

### 開發模式
```bash
npm run dev
```
應用程式將在 http://localhost:3001 啟動

### 生產構建
```bash
npm run build
```

## 📋 URL 參數

應用程式需要以下 URL 參數才能正常運行：

### 必要參數
- `TenantID` - 租戶編碼（必須）

### 可選參數
- `ServiceID` - 服務編碼（可選）
- `UserID` - 用戶ID（可選）

### 示例 URL
```
http://localhost:3001/?TenantID=your_tenant&ServiceID=your_service&UserID=your_user
```

**最簡 URL（僅必要參數）：**
```
http://localhost:3001/?TenantID=your_tenant
```

## ⚙️ 配置管理

### 配置文件位置
- 主配置文件：`public/config.json`

### 配置結構
```json
{
  "app": {
    "name": "KM Client",
    "version": "1.0.0",
    "environment": "development"
  },
  "api": {
    "gateway": {
      "baseUrl": "YOUR_API_GATEWAY_URL",
      "timeout": 30000
    },
    "services": {
      "tenants": {
        "service": "tenants.svc",
        "path": "tenants"
      }
    }
  },
  "features": {
    "systemInstructions": {
      "enabled": true,
      "directAccess": true
    },
    "hotReload": {
      "enabled": true,
      "interval": 5000
    }
  }
}

注意：請將 `YOUR_API_GATEWAY_URL` 替換為實際的 API Gateway 地址，例如：
- 開發環境：`http://localhost:8083`
- 測試環境：`http://test-api.example.com`
- 生產環境：`https://api.example.com`
```

### 熱重載
配置文件支援熱重載，修改 `public/config.json` 後會自動生效，無需重啟應用程式。

## 🎨 UI/UX 特色

### 現代化設計
- **提亮主題** - 中等深度的灰色背景，增強可讀性
- **科技感色彩** - 降低強度的霓虹色彩，保持科技感
- **雙欄佈局** - 經典的左右分欄設計
- **白色元素** - 增加白色和淺色元素，提升視覺舒適度

### 動畫效果
- **頁面過渡** - 流暢的頁面切換動畫
- **元素動畫** - 淡入、滑入、縮放等動畫效果
- **懸停效果** - 豐富的滑鼠懸停互動效果
- **載入動畫** - 科技感的載入指示器

### 響應式設計
- **多設備支援** - 桌面、平板、手機完美適配
- **性能優化** - 移動端動畫優化，節省電池
- **觸控優化** - 觸控設備的交互優化

## 📱 頁面結構

### 主頁 (`/`)
- **左側面板**：附件管理（檔案、網站、影片、文本）
- **右側面板**：智慧聊天對話
- **頂部導航**：側邊欄切換、主題切換
- **響應式設計**：支援桌面、平板、手機

### 系統指令設置 (`/system-instructions`)
- 全局系統指令配置
- 服務特定指令管理
- 即時保存功能
- 參數驗證
- **僅通過直接 URL 訪問**

## 🔧 開發指南

### 目錄結構
```
src/
├── components/          # React 組件
│   ├── Common/         # 通用組件
│   ├── Home/           # 主頁組件
│   └── SystemInstructions/ # 系統指令組件
├── hooks/              # 自定義 Hooks
├── services/           # API 服務
├── types/              # TypeScript 類型定義
├── utils/              # 工具函數
├── styles/             # 樣式文件
└── router/             # 路由配置
```

### 代碼規範
- 使用 TypeScript 進行類型檢查
- 遵循 React Hooks 最佳實踐
- 組件採用函數式組件
- 使用 ESLint 進行代碼檢查

## 🚀 部署

### 環境變量
創建 `.env` 文件（可選，用於覆蓋默認配置）：
```env
# API 基礎 URL（可選，會覆蓋 config.json 中的設置）
VITE_API_BASE_URL=http://localhost:8083

# 應用環境
VITE_APP_ENV=production
```

**配置優先級**：
1. `config.json` 文件中的配置（推薦）
2. 環境變量 `VITE_API_BASE_URL`（作為後備）
3. 默認值 `http://localhost:8083`

### 構建部署
```bash
npm run build
```

### 一鍵部署（Docker / Compose）
- 參考部署手冊：`./deploy.md`
- 快速開始：
  1. 建置映像：`docker build -t yourrepo/km-client:1.0.0 .`
  2. 本機驗證：`docker run --rm -p 8080:80 yourrepo/km-client:1.0.0`
  3. 生產部署（Compose）：編輯 `docker-compose.yml` 與宿主機 `/etc/km-client/config.json` 後，執行 `docker compose up -d`
  4. 熱重載：修改 `config.json` 後數秒內自動生效（需確保 `/config.json` 不被快取）

構建產物在 `dist` 目錄中，可直接部署到靜態文件服務器。

## 🎯 主要改進

1. **用戶體驗提升** - 直接進入工作界面，無需額外導航
2. **視覺舒適度** - 提亮的色彩方案，減少眼部疲勞
3. **功能集中** - 左側管理附件，右側進行對話，工作流程更順暢
4. **響應式優化** - 在任何設備上都有良好的使用體驗
5. **簡化參數** - 只需要 TenantID 即可使用，降低使用門檻

## 🚀 核心功能實現

### 📎 附件管理系統
- **檔案上傳** - 支援拖拽上傳，多種格式檔案
- **網站管理** - 添加網站連結，自動爬取內容
- **YouTube 管理** - 管理 YouTube 影片連結
- **純文本管理** - 直接添加和管理文本內容
- **即時同步** - 操作後自動刷新，即時顯示結果

### � 智慧聊天系統
- **知識庫對話** - 基於附件內容進行 AI 對話
- **附件聊天** - 直接上傳檔案進行對話分析
- **消息解析** - 支援複雜消息格式和快速回復
- **會話管理** - 支援多會話創建和管理
- **實時狀態** - 載入狀態、錯誤處理、進度提示

### 🔧 技術架構
- **狀態管理** - 使用 Zustand 進行統一狀態管理
- **API 服務** - 統一的 API 調用層，支援重試和錯誤處理
- **類型安全** - 完整的 TypeScript 類型定義
- **組件化** - 模組化組件設計，易於維護和擴展

## �📄 授權

MIT License

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request！

---

**KMClient 核心功能開發完成！** 🎉 現在您擁有一個功能完整、架構清晰、用戶體驗優秀的知識管理客戶端，完全符合您的要求。
