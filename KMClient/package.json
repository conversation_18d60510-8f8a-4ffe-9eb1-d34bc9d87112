{"name": "km-client", "version": "1.0.0", "description": "KM Client - 知識管理客戶端，專注於系統指令設置和智慧聊天功能", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "test": "vitest", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug"}, "keywords": ["react", "typescript", "antd", "tailwindcss", "knowledge-management", "ai-chat", "system-instructions"], "author": "<PERSON>", "license": "MIT", "dependencies": {"@ant-design/icons": "^6.0.0", "@tailwindcss/postcss": "^4.1.11", "@types/crypto-js": "^4.2.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.6.0", "antd": "^5.26.4", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "framer-motion": "^12.23.7", "gsap": "^3.13.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.3", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "typescript": "^5.8.3", "vite": "^7.0.3", "zustand": "^5.0.6"}, "devDependencies": {"@playwright/test": "^1.53.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^24.3.0", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitest/coverage-v8": "^3.2.4", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "jsdom": "^26.1.0", "tailwindcss": "^3.4.17", "vitest": "^3.2.4"}}