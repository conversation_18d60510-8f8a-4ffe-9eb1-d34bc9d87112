#!/usr/bin/env node

/**
 * 配置熱重載測試腳本
 * 用於驗證配置文件更改後是否能正確熱重載
 */

const fs = require('fs');
const path = require('path');

const CONFIG_PATH = path.join(__dirname, 'public/config.json');
const BACKUP_PATH = path.join(__dirname, 'public/config.json.backup');

// 讀取當前配置
function readConfig() {
  return JSON.parse(fs.readFileSync(CONFIG_PATH, 'utf8'));
}

// 寫入配置
function writeConfig(config) {
  fs.writeFileSync(CONFIG_PATH, JSON.stringify(config, null, 2));
}

// 備份當前配置
function backupConfig() {
  fs.copyFileSync(CONFIG_PATH, BACKUP_PATH);
  console.log('✅ 配置文件已備份');
}

// 恢復配置
function restoreConfig() {
  if (fs.existsSync(BACKUP_PATH)) {
    fs.copyFileSync(BACKUP_PATH, CONFIG_PATH);
    fs.unlinkSync(BACKUP_PATH);
    console.log('✅ 配置文件已恢復');
  }
}

// 測試配置更改
function testConfigChange() {
  console.log('🧪 開始配置熱重載測試...');
  
  // 備份原配置
  backupConfig();
  
  try {
    const config = readConfig();
    const originalBaseUrl = config.api.gateway.baseUrl;
    
    console.log(`📍 原始 baseUrl: ${originalBaseUrl}`);
    
    // 修改 baseUrl 進行測試
    const testBaseUrl = 'http://test-api.example.com:8083';
    config.api.gateway.baseUrl = testBaseUrl;
    config.app.name = '配置熱重載測試';
    
    writeConfig(config);
    console.log(`🔄 已更改 baseUrl 為: ${testBaseUrl}`);
    console.log('🔄 已更改 app.name 為: 配置熱重載測試');
    console.log('');
    console.log('📋 請在瀏覽器中檢查：');
    console.log('   1. 頁面標題是否變為 "配置熱重載測試"');
    console.log('   2. 控制台是否顯示配置更新日誌');
    console.log('   3. API 請求是否使用新的 baseUrl');
    console.log('');
    console.log('⏰ 10 秒後自動恢復原配置...');
    
    // 10 秒後恢復
    setTimeout(() => {
      restoreConfig();
      console.log('✅ 測試完成，配置已恢復');
      process.exit(0);
    }, 10000);
    
  } catch (error) {
    console.error('❌ 測試失敗:', error);
    restoreConfig();
    process.exit(1);
  }
}

// 處理中斷信號
process.on('SIGINT', () => {
  console.log('\n⚠️  測試被中斷，正在恢復配置...');
  restoreConfig();
  process.exit(0);
});

// 開始測試
testConfigChange();
