/// <reference types="vitest" />
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  test: {
    // 使用 jsdom 以支援 React 元件測試
    environment: 'jsdom',
    setupFiles: ['./vitest.setup.ts'],
  },
  server: {
    port: 3001, // 使用不同的端口避免衝突
    strictPort: true, // 確保使用固定端口，避免測試等待錯誤端口
    open: false, // 減少 CI/測試環境干擾
    proxy: {
      // 攔截所有 API 請求並代理到目標服務器
      '/svc': {
        // target: 'https://ai.aile.cloud/v2',
        target: 'https://b6c6f7d99a31.ngrok-free.app',
        changeOrigin: true,
        secure: true,
        configure: (proxy) => {
          proxy.on('error', (err) => {
            console.log('🚫 Proxy error:', err.message);
          });
          proxy.on('proxyReq', (proxyReq, req) => {
            console.log('🌐 Sending Request to Target:', req.method, req.url);
            // 添加必要的標頭
            proxyReq.setHeader('ngrok-skip-browser-warning', 'true');
          });
          proxy.on('proxyRes', (proxyRes, req) => {
            console.log('✅ Received Response from Target:', proxyRes.statusCode, req.url);
          });
        }
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
});
