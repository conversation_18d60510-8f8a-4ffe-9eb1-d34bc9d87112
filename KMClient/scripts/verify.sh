#!/usr/bin/env bash
set -euo pipefail

IMAGE_TAG="yourrepo/km-client:verify"
PORT="8080"
CFG_HOST="/etc/km-client/config.json"
CFG_CONT="/usr/share/nginx/html/config.json"

# Colors
info() { echo -e "\033[1;34m[INFO]\033[0m $*"; }
ok()   { echo -e "\033[1;32m[ OK ]\033[0m $*"; }
err()  { echo -e "\033[1;31m[FAIL]\033[0m $*"; }

main() {
  info "Building docker image..."
  docker build -t "$IMAGE_TAG" .

  info "Preparing temp config.json (for hot-reload test)"
  TMPDIR=$(mktemp -d)
  cp public/config.json "$TMPDIR/config.json"

  info "Starting container on :$PORT ..."
  CID=$(docker run -d -p "$PORT:80" -v "$TMPDIR/config.json":"$CFG_CONT":ro "$IMAGE_TAG")
  trap 'docker rm -f $CID >/dev/null 2>&1 || true; rm -rf "$TMPDIR"' EXIT

  info "Waiting for server..."
  for i in {1..30}; do
    if curl -sSf "http://localhost:$PORT/" >/dev/null; then ok "Server is up"; break; fi
    sleep 1
    if [[ $i -eq 30 ]]; then err "Server not responding"; exit 1; fi
  done

  info "Checking no-cache headers for /config.json ..."
  HDRS=$(curl -sSI "http://localhost:$PORT/config.json")
  echo "$HDRS" | grep -qi "Cache-Control:.*no-cache" && ok "Cache-Control no-cache present" || { err "Cache-Control no-cache missing"; exit 1; }

  info "Testing hot-reload: change app.name and expect UI to update within interval (manual visual check)"
  # Modify app.name then wait so human can verify in browser tab title
  jq '.app.name = "HotReloadTest"' "$TMPDIR/config.json" > "$TMPDIR/config.json.tmp" && mv "$TMPDIR/config.json.tmp" "$TMPDIR/config.json"
  ok "Updated $TMPDIR/config.json -> app.name=HotReloadTest"
  info "Open http://localhost:$PORT/?TenantID=demo and verify tab title changes within ~5-10s"

  ok "Verify script completed. Press Ctrl+C to stop, or container will be cleaned up automatically on exit."
  # Keep running for manual check
  read -rp "Press Enter to cleanup and exit..."
}

main "$@"

