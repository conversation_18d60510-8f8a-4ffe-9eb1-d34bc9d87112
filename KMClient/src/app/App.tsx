import React, { useEffect, useRef } from 'react';
import { App as AntA<PERSON>, Result, Button, ConfigProvider } from 'antd';
import zhTW from 'antd/locale/zh_TW';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { parseURLParams, validateURLParams, storeParams } from '@/utils/urlParams';
import { useAppInitialization, useAppStore } from '@/hooks/useAppStore';
import { useConfig } from '@/hooks/useConfig';
import { UserInfo } from '@/types/attachment';
import AppRouter from '@/app/router';

// 參數錯誤頁面組件
const ParameterErrorPage: React.FC<{ missingParams: string[] }> = ({ missingParams }) => (
  <div className="min-h-screen bg-gradient-to-br from-dark-900 to-dark-800 flex items-center justify-center p-4">
    <div className="max-w-md w-full">
      <Result
        status="error"
        icon={<ExclamationCircleOutlined className="text-red-500" />}
        title={<span className="text-white">缺少必要參數</span>}
        subTitle={
          <div className="text-gray-300">
            <p>應用程式需要以下必要參數才能正常運行：</p>
            <ul className="mt-2 text-left">
              {missingParams.map(param => (
                <li key={param} className="text-red-400">• {param}</li>
              ))}
            </ul>
            <p className="mt-4 text-sm">
              請確保 URL 包含正確的查詢參數，例如：<br />
              <code className="bg-dark-700 px-2 py-1 rounded text-neon-blue">
                ?TenantID=your_tenant&ServiceID=your_service
              </code>
            </p>
          </div>
        }
        extra={
          <Button
            type="primary"
            onClick={() => window.location.reload()}
            className="bg-neon-blue border-neon-blue hover:bg-neon-blue/80"
          >
            重新載入
          </Button>
        }
      />
    </div>
  </div>
);

// 主應用程式組件
const App: React.FC = () => {
  const {
    isParamsValid,
    paramErrors,
    isInitialized,
    isLoading,
    setURLParams,
    setParamValidation,
    setInitialized,
    setLoading,
    restoreFromStorage,
  } = useAppInitialization();

  const {
    config,
    isLoading: configLoading,
    error: configError,
  } = useConfig();

  const { setUserInfo, refreshAttachments } = useAppStore();

  // 追蹤是否已經進行過初始附件刷新
  const hasInitialRefresh = useRef(false);

  // 初始化應用程式
  useEffect(() => {
    const initializeApp = async () => {
      setLoading(true);

      try {
        // 恢復存儲的狀態
        restoreFromStorage();

        // 解析當前 URL 參數
        const params = parseURLParams();
        console.log('🔍 解析的 URL 參數:', params);

        const validation = validateURLParams(params);
        console.log('✅ 參數驗證結果:', validation);

        // 更新狀態
        setURLParams(params);
        setParamValidation(validation.isValid, validation.missingParams);

        // 如果參數有效，存儲它們並設置用戶信息
        if (validation.isValid) {
          storeParams(params);

          // 設置用戶信息
          const userInfo: UserInfo = {
            tenant_id: params.tenantId || '',
            service_id: params.serviceId || '',
            user_id: params.userId || '',
          };
          setUserInfo(userInfo);

          // 注意：不在這裡調用 refreshAttachments()
          // 將在配置載入完成後的 useEffect 中調用
        }

        setInitialized(true);
      } catch (error) {
        console.error('App initialization failed:', error);
        setParamValidation(false, ['應用程式初始化失敗']);
      } finally {
        setLoading(false);
      }
    };

    initializeApp();
  }, []);

  // 配置載入完成後刷新附件數據（僅在初始化時）
  useEffect(() => {
    // 只有當配置載入完成、參數有效、且已初始化時才刷新附件
    // 使用 ref 來追蹤是否已經進行過初始刷新，避免熱重載時重複調用
    if (!configLoading && config && isParamsValid && isInitialized && !hasInitialRefresh.current) {
      console.log('🔄 配置載入完成，開始初始刷新附件數據...');
      console.log('📋 使用的 baseURL:', config.api.gateway.baseUrl);

      hasInitialRefresh.current = true;

      // 延遲一小段時間確保 apiService 已經更新配置
      setTimeout(() => {
        refreshAttachments();
      }, 100);
    }
  }, [configLoading, config, isParamsValid, isInitialized, refreshAttachments]);


      // 根據配置動態更新頁面標題（支援熱重載）
      useEffect(() => {
        const appName = config?.app?.name || 'KM Client';
        try {
          document.title = appName;
        } catch (e) {
          console.warn('Failed to update document.title:', e);
        }
      }, [config?.app?.name]);

  // 載入中狀態
  if (!isInitialized || isLoading || configLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-dark-900 to-dark-800 flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner mb-4"></div>
          <div className="text-neon-blue text-lg">
            {configLoading ? '載入配置中...' :
             !isInitialized ? '初始化應用程式...' : '驗證參數中...'}
          </div>
        </div>
      </div>
    );
  }

  // 配置錯誤狀態
  if (configError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-dark-900 to-dark-800 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <Result
            status="error"
            icon={<ExclamationCircleOutlined className="text-red-500" />}
            title={<span className="text-white">配置載入失敗</span>}
            subTitle={
              <div className="text-gray-300">
                <p>無法載入應用程式配置文件</p>
                <p className="mt-2 text-sm text-red-400">{configError}</p>
              </div>
            }
            extra={
              <Button
                type="primary"
                onClick={() => window.location.reload()}
                className="bg-neon-blue border-neon-blue hover:bg-neon-blue/80"
              >
                重新載入
              </Button>
            }
          />
        </div>
      </div>
    );
  }

  // 參數錯誤狀態
  if (!isParamsValid) {
    return <ParameterErrorPage missingParams={paramErrors} />;
  }

  // 正常應用程式
  return (
    <ConfigProvider locale={zhTW}>
      <AntApp>
        <AppRouter />
      </AntApp>
    </ConfigProvider>
  );
};

export default App;

