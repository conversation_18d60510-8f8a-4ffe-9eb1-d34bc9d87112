/**
 * 路由配置
 */

import React from 'react';
import { createBrowserRouter, RouterProvider, Navigate } from 'react-router-dom';
import HomePage from '@/pages/Home/HomePage';
import SystemInstructionsPage from '@/pages/SystemInstructions/SystemInstructionsPage';
import ThemeDemo from '@/pages/ThemeDemo/ThemeDemo';
import MotionDemo from '@/pages/MotionDemo/MotionDemo';

// 路由配置
const router = createBrowserRouter([
  {
    path: '/',
    element: <HomePage />,
  },
  {
    path: '/system-instructions',
    element: <SystemInstructionsPage />,
  },
  {
    path: '/sys-instruction', // 兼容舊路徑
    element: <Navigate to="/system-instructions" replace />,
  },
  {
    path: '/theme-demo',
    element: <ThemeDemo />,
  },
  {
    path: '/motion-demo',
    element: <MotionDemo />,
  },
  {
    path: '*',
    element: <Navigate to="/" replace />,
  },
]);

// 路由提供者組件
export const AppRouter: React.FC = () => {
  return <RouterProvider router={router} />;
};

export default AppRouter;

