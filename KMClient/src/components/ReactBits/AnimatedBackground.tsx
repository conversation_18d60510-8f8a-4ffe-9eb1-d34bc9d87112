/**
 * AnimatedBackground 組件 - 基於 React Bits 風格的動畫背景
 * 提供粒子效果、波浪動畫等背景效果
 */

import React, { useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import clsx from 'clsx';

type BackgroundType = 'particles' | 'waves' | 'gradient' | 'dots' | 'grid';

interface AnimatedBackgroundProps {
  type?: BackgroundType;
  className?: string;
  style?: React.CSSProperties;
  color?: string;
  opacity?: number;
  speed?: number;
  density?: number;
  children?: React.ReactNode;
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({
  type = 'particles',
  className = '',
  style = {},
  color = 'var(--color-neon-blue)',
  opacity = 0.1,
  speed = 1,
  density = 50,
  children,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 粒子系統
  useEffect(() => {
    if (type !== 'particles') return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 設置畫布大小
    const resizeCanvas = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // 粒子類
    class Particle {
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;

      constructor() {
        // 確保 canvas 不為 null
        if (!canvas) throw new Error('Canvas is not available');
        this.x = Math.random() * canvas.width;
        this.y = Math.random() * canvas.height;
        this.vx = (Math.random() - 0.5) * speed;
        this.vy = (Math.random() - 0.5) * speed;
        this.size = Math.random() * 2 + 1;
        this.opacity = Math.random() * opacity;
      }

      update() {
        this.x += this.vx;
        this.y += this.vy;

        // 確保 canvas 不為 null
        if (!canvas) return;

        // 邊界檢測
        if (this.x < 0 || this.x > canvas.width) this.vx *= -1;
        if (this.y < 0 || this.y > canvas.height) this.vy *= -1;

        // 保持在畫布內
        this.x = Math.max(0, Math.min(canvas.width, this.x));
        this.y = Math.max(0, Math.min(canvas.height, this.y));
      }

      draw() {
        // 確保 ctx 不為 null
        if (!ctx) return;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(59, 130, 246, ${this.opacity})`;
        ctx.fill();
      }
    }

    // 創建粒子
    const particles: Particle[] = [];
    for (let i = 0; i < density; i++) {
      particles.push(new Particle());
    }

    // 動畫循環
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particles.forEach(particle => {
        particle.update();
        particle.draw();
      });

      // 連接附近的粒子
      particles.forEach((particle, i) => {
        particles.slice(i + 1).forEach(otherParticle => {
          const dx = particle.x - otherParticle.x;
          const dy = particle.y - otherParticle.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 100) {
            ctx.beginPath();
            ctx.moveTo(particle.x, particle.y);
            ctx.lineTo(otherParticle.x, otherParticle.y);
            ctx.strokeStyle = `rgba(59, 130, 246, ${opacity * (1 - distance / 100)})`;
            ctx.lineWidth = 0.5;
            ctx.stroke();
          }
        });
      });

      requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
    };
  }, [type, speed, density, opacity]);

  // 渲染不同類型的背景
  const renderBackground = () => {
    switch (type) {
      case 'particles':
        return (
          <canvas
            ref={canvasRef}
            className="absolute inset-0 w-full h-full"
            style={{ opacity }}
          />
        );

      case 'waves':
        return (
          <div className="absolute inset-0 overflow-hidden">
            <motion.div
              className="absolute inset-0"
              style={{
                background: `linear-gradient(45deg, transparent, ${color}, transparent)`,
                opacity,
              }}
              animate={{
                x: ['-100%', '100%'],
              }}
              transition={{
                duration: 10 / speed,
                repeat: Infinity,
                ease: 'linear',
              }}
            />
          </div>
        );

      case 'gradient':
        return (
          <motion.div
            className="absolute inset-0"
            style={{
              background: `radial-gradient(circle at center, ${color} 0%, transparent 70%)`,
              opacity,
            }}
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 360],
            }}
            transition={{
              duration: 20 / speed,
              repeat: Infinity,
              ease: 'linear',
            }}
          />
        );

      case 'dots':
        return (
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `radial-gradient(circle, ${color} 1px, transparent 1px)`,
              backgroundSize: '20px 20px',
              opacity,
            }}
          />
        );

      case 'grid':
        return (
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(${color} 1px, transparent 1px),
                linear-gradient(90deg, ${color} 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px',
              opacity,
            }}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div
      className={clsx('relative', className)}
      style={style}
    >
      {/* 背景層 */}
      <div className="absolute inset-0 pointer-events-none">
        {renderBackground()}
      </div>

      {/* 內容層 */}
      {children && (
        <div className="relative z-10">
          {children}
        </div>
      )}
    </div>
  );
};

export default AnimatedBackground;
