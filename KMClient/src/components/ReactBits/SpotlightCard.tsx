/**
 * SpotlightCard 組件 - 基於 React Bits 風格的光效卡片
 * 提供鼠標懸停光效和平滑動畫
 */

import React, { useRef, useState } from 'react';
import { motion, Variants } from 'framer-motion';
import clsx from 'clsx';
import { useMotionPreferences } from '@/ui/animations/framer-motion';

interface SpotlightCardProps {
  children: React.ReactNode;
  className?: string;
  spotlightColor?: string;
  spotlightSize?: number;
  borderRadius?: number;
  backgroundColor?: string;
  padding?: string;
  onClick?: () => void;
  disabled?: boolean;
  style?: React.CSSProperties;
}

const SpotlightCard: React.FC<SpotlightCardProps> = ({
  children,
  className = '',
  spotlightColor = 'rgba(167, 139, 250, 0.3)', // 默認使用紫色，在深色模式下更明顯
  spotlightSize = 200,
  borderRadius = 8,
  backgroundColor = 'var(--color-surface-primary)',
  padding = '16px',
  onClick,
  disabled = false,
  style = {},
}) => {
  const { prefersReducedMotion, animationSpeed } = useMotionPreferences();
  const cardRef = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);

  // 處理鼠標移動
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current || disabled) return;

    const rect = cardRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    setMousePosition({ x, y });
  };

  // 處理鼠標進入
  const handleMouseEnter = () => {
    if (!disabled) {
      setIsHovered(true);
    }
  };

  // 處理鼠標離開
  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  // 動畫變體
  const cardVariants: Variants = {
    initial: {
      scale: 1,
      boxShadow: 'var(--shadow-sm)',
    },
    hover: {
      scale: prefersReducedMotion ? 1 : 1.02,
      boxShadow: prefersReducedMotion ? 'var(--shadow-sm)' : 'var(--shadow-lg)',
      transition: {
        duration: prefersReducedMotion ? 0 : 0.2 / animationSpeed,
        ease: 'easeOut',
      },
    },
  };

  return (
    <motion.div
      ref={cardRef}
      className={clsx(
        'relative overflow-hidden transition-all duration-300 cursor-pointer',
        {
          'cursor-not-allowed opacity-60': disabled,
          'cursor-pointer': !disabled && onClick,
        },
        className
      )}
      style={{
        borderRadius: `${borderRadius}px`,
        backgroundColor,
        padding,
        border: '1px solid var(--color-border-primary)',
        ...style,
      }}
      variants={cardVariants}
      initial="initial"
      animate={isHovered && !disabled ? 'hover' : 'initial'}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={disabled ? undefined : onClick}
    >
      {/* 光效層 */}
      {isHovered && !disabled && (
        <div
          className="absolute inset-0 pointer-events-none"
          style={{
            background: `radial-gradient(${spotlightSize}px circle at ${mousePosition.x}px ${mousePosition.y}px, ${spotlightColor}, transparent 70%)`,
            borderRadius: `${borderRadius}px`,
            opacity: 0.8,
            transition: 'opacity 0.3s ease',
          }}
        />
      )}

      {/* 邊框光效 */}
      {isHovered && !disabled && (
        <div
          className="absolute inset-0 pointer-events-none"
          style={{
            borderRadius: `${borderRadius}px`,
            background: `linear-gradient(90deg, transparent, ${spotlightColor.replace('0.3', '0.5')}, transparent)`,
            mask: `linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)`,
            maskComposite: 'xor',
            padding: '1px',
          }}
        />
      )}

      {/* 內容層 */}
      <div className="relative z-10">
        {children}
      </div>

      {/* 微妙的內陰影效果 */}
      <div
        className="absolute inset-0 pointer-events-none"
        style={{
          borderRadius: `${borderRadius}px`,
          boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.1)',
        }}
      />
    </motion.div>
  );
};

export default SpotlightCard;
