/**
 * AnimatedText 組件 - 基於 React Bits 風格的文字動畫
 * 支持打字機效果、漸入動畫等
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import clsx from 'clsx';

type AnimationType = 'typewriter' | 'fadeIn' | 'slideUp' | 'bounce' | 'glow';

interface AnimatedTextProps {
  text: string;
  className?: string;
  style?: React.CSSProperties;
  animation?: AnimationType;
  duration?: number;
  delay?: number;
  repeat?: boolean;
  onComplete?: () => void;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';
}

const AnimatedText: React.FC<AnimatedTextProps> = ({
  text,
  className = '',
  style = {},
  animation = 'fadeIn',
  duration = 1,
  delay = 0,
  repeat = false,
  onComplete,
  as: Component = 'div',
}) => {
  const [displayText, setDisplayText] = useState('');
  const [isAnimating, setIsAnimating] = useState(false);

  // 打字機效果
  useEffect(() => {
    if (animation === 'typewriter') {
      setIsAnimating(true);
      setDisplayText('');
      
      const timeout = setTimeout(() => {
        let currentIndex = 0;
        const typeInterval = setInterval(() => {
          if (currentIndex <= text.length) {
            setDisplayText(text.slice(0, currentIndex));
            currentIndex++;
          } else {
            clearInterval(typeInterval);
            setIsAnimating(false);
            onComplete?.();
            
            if (repeat) {
              setTimeout(() => {
                setDisplayText('');
                currentIndex = 0;
              }, 1000);
            }
          }
        }, duration * 1000 / text.length);

        return () => clearInterval(typeInterval);
      }, delay * 1000);

      return () => clearTimeout(timeout);
    } else {
      setDisplayText(text);
    }
  }, [text, animation, duration, delay, repeat, onComplete]);

  // 動畫變體
  const getAnimationVariants = () => {
    switch (animation) {
      case 'fadeIn':
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          transition: { duration, delay },
        };
      
      case 'slideUp':
        return {
          initial: { opacity: 0, y: 20 },
          animate: { opacity: 1, y: 0 },
          transition: { duration, delay, ease: 'easeOut' },
        };
      
      case 'bounce':
        return {
          initial: { opacity: 0, scale: 0.5 },
          animate: { 
            opacity: 1, 
            scale: 1,
            transition: {
              duration,
              delay,
              type: 'spring',
              stiffness: 500,
              damping: 15,
            },
          },
        };
      
      case 'glow':
        return {
          initial: { 
            opacity: 0,
            textShadow: '0 0 0px var(--color-neon-blue)',
          },
          animate: { 
            opacity: 1,
            textShadow: [
              '0 0 0px var(--color-neon-blue)',
              '0 0 10px var(--color-neon-blue)',
              '0 0 20px var(--color-neon-blue)',
              '0 0 10px var(--color-neon-blue)',
            ],
          },
          transition: { 
            duration, 
            delay,
            textShadow: {
              duration: 2,
              repeat: repeat ? Infinity : 0,
              repeatType: 'reverse' as const,
            },
          },
        };
      
      default:
        return {
          initial: { opacity: 1 },
          animate: { opacity: 1 },
        };
    }
  };

  const animationProps = animation !== 'typewriter' ? getAnimationVariants() : {};

  // 打字機效果的特殊處理
  if (animation === 'typewriter') {
    return (
      <Component
        className={clsx(
          'relative',
          className
        )}
        style={style}
      >
        {displayText}
        {isAnimating && (
          <motion.span
            className="inline-block ml-1"
            animate={{ opacity: [1, 0] }}
            transition={{ duration: 0.5, repeat: Infinity, repeatType: 'reverse' }}
            style={{ color: 'var(--color-neon-blue)' }}
          >
            |
          </motion.span>
        )}
      </Component>
    );
  }

  // 其他動畫效果
  return (
    <motion.div
      className={clsx(className)}
      style={style}
      {...animationProps}
      onAnimationComplete={onComplete}
    >
      <Component>{displayText}</Component>
    </motion.div>
  );
};

// 預設動畫組件
export const TypewriterText: React.FC<Omit<AnimatedTextProps, 'animation'>> = (props) => (
  <AnimatedText {...props} animation="typewriter" />
);

export const FadeInText: React.FC<Omit<AnimatedTextProps, 'animation'>> = (props) => (
  <AnimatedText {...props} animation="fadeIn" />
);

export const SlideUpText: React.FC<Omit<AnimatedTextProps, 'animation'>> = (props) => (
  <AnimatedText {...props} animation="slideUp" />
);

export const BounceText: React.FC<Omit<AnimatedTextProps, 'animation'>> = (props) => (
  <AnimatedText {...props} animation="bounce" />
);

export const GlowText: React.FC<Omit<AnimatedTextProps, 'animation'>> = (props) => (
  <AnimatedText {...props} animation="glow" />
);

export default AnimatedText;
