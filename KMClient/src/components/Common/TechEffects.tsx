/**
 * 科技感效果組件庫
 */

import React from 'react';

// 數據流動畫組件
export const DataFlow: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <div className={`data-flow ${className}`}>
    {children}
  </div>
);

// 霓虹邊框組件
export const NeonBorder: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <div className={`neon-border ${className}`}>
    {children}
  </div>
);

// 發光效果組件
export const GlowEffect: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <div className={`glow-effect ${className}`}>
    {children}
  </div>
);

// 漸變文字組件
export const GradientText: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <span className={`gradient-text ${className}`}>
    {children}
  </span>
);

// 發光文字組件
export const GlowText: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <span className={`text-glow ${className}`}>
    {children}
  </span>
);

// 科技感背景組件
export const TechBackground: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <div className={`tech-bg ${className}`}>
    {children}
  </div>
);

// 卡片懸停效果組件
export const HoverCard: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <div className={`card-hover ${className}`}>
    {children}
  </div>
);

// 脈衝按鈕組件
export const PulseButton: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <div className={`btn-pulse ${className}`}>
    {children}
  </div>
);

// 動畫容器組件
interface AnimatedContainerProps {
  children: React.ReactNode;
  animation?: 'fadeIn' | 'slideIn' | 'slideInRight' | 'scaleIn' | 'rotateIn' | 'bounceIn';
  delay?: 100 | 200 | 300 | 400 | 500;
  className?: string;
}

export const AnimatedContainer: React.FC<AnimatedContainerProps> = ({ 
  children, 
  animation = 'fadeIn',
  delay,
  className = '' 
}) => {
  const animationClass = animation.replace(/([A-Z])/g, '-$1').toLowerCase();
  const delayClass = delay ? `delay-${delay}` : '';
  
  return (
    <div className={`${animationClass} ${delayClass} ${className}`}>
      {children}
    </div>
  );
};

// 載入動畫組件
export const TechLoader: React.FC<{ size?: 'small' | 'medium' | 'large'; className?: string }> = ({ 
  size = 'medium',
  className = '' 
}) => {
  const sizeClasses = {
    small: 'w-6 h-6',
    medium: 'w-10 h-10',
    large: 'w-16 h-16'
  };

  return (
    <div className={`loading-enhanced ${sizeClasses[size]} ${className}`}>
      <div className="loading-spinner"></div>
    </div>
  );
};

// 粒子效果組件（CSS 實現）
export const ParticleEffect: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`relative overflow-hidden ${className}`}>
    <div className="absolute inset-0 opacity-30">
      {[...Array(20)].map((_, i) => (
        <div
          key={i}
          className="absolute w-1 h-1 bg-neon-blue rounded-full animate-float"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 3}s`,
            animationDuration: `${3 + Math.random() * 2}s`
          }}
        />
      ))}
    </div>
  </div>
);

// 全息效果組件
export const HologramEffect: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <div className={`relative ${className}`}>
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-neon-blue/10 to-transparent opacity-50 animate-pulse-slow"></div>
    <div className="relative z-10">
      {children}
    </div>
  </div>
);

// 掃描線效果組件
export const ScanlineEffect: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`relative overflow-hidden ${className}`}>
    <div className="absolute inset-0 opacity-20">
      <div className="absolute w-full h-px bg-neon-blue animate-pulse top-1/4"></div>
      <div className="absolute w-full h-px bg-neon-blue animate-pulse top-2/4 animation-delay-1000"></div>
      <div className="absolute w-full h-px bg-neon-blue animate-pulse top-3/4 animation-delay-2000"></div>
    </div>
  </div>
);

// 組合效果組件
export const TechCard: React.FC<{ 
  children: React.ReactNode; 
  withGlow?: boolean;
  withNeonBorder?: boolean;
  withDataFlow?: boolean;
  withHover?: boolean;
  animation?: 'fadeIn' | 'slideIn' | 'scaleIn';
  delay?: number;
  className?: string;
}> = ({ 
  children, 
  withGlow = false,
  withNeonBorder = false,
  withDataFlow = false,
  withHover = false,
  animation,
  delay,
  className = '' 
}) => {
  let containerClass = className;
  
  if (withGlow) containerClass += ' glow-effect';
  if (withNeonBorder) containerClass += ' neon-border';
  if (withDataFlow) containerClass += ' data-flow';
  if (withHover) containerClass += ' card-hover';
  if (animation) containerClass += ` ${animation.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
  if (delay) containerClass += ` delay-${delay}`;

  return (
    <div className={containerClass}>
      {children}
    </div>
  );
};
