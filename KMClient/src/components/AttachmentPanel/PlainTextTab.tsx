/**
 * 純文本管理標籤頁組件
 */

import React, { useState } from 'react';
import { Button, List, Space, Popconfirm, Typography, Empty, Modal, Form, Input } from 'antd';
import {
  PlusOutlined
} from '@ant-design/icons';
import {
  MdDelete,
  MdDescription,
  MdVisibility
} from 'react-icons/md';
import { useAppStore } from '@/hooks/useAppStore';
import { useAttachmentWithRefresh } from '@/hooks/useAttachmentWithRefresh';
import { AttachmentType, PlainTextAttachment } from '@/types/attachment';
import { SpotlightCard } from '@/components/ReactBits';

const { Text } = Typography;

interface PlainTextFormData {
  name: string;
  content: string;
  remark: string;
}

const PlainTextTab: React.FC = () => {
  const { attachments, userInfo, attachmentsLoading } = useAppStore();
  const { setPlainText, deletePlainText } = useAttachmentWithRefresh();
  const [modalVisible, setModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [viewingText, setViewingText] = useState<PlainTextAttachment | null>(null);
  const [form] = Form.useForm<PlainTextFormData>();

  // 獲取純文本附件
  const plainTextAttachments = attachments.filter(att => att.type === AttachmentType.PLAIN_TEXT) as PlainTextAttachment[];

  // 處理添加純文本
  const handleAddPlainText = () => {
    setModalVisible(true);
  };

  // 處理表單提交
  const handleSubmit = async (values: PlainTextFormData) => {
    if (!userInfo.tenant_id) {
      console.error('缺少用戶信息');
      return;
    }

    try {
      // 構建純文本請求，只在 user_id 存在時才包含
      const plainTextRequest: any = {
        tenant_id: userInfo.tenant_id,
        service_id: userInfo.service_id,
        content: values.content, // 修正：直接傳遞內容
        remark: values.remark, // 修正：直接傳遞備註
      };

      // 只在 user_id 存在時才添加
      if (userInfo.user_id) {
        plainTextRequest.user_id = userInfo.user_id;
      }

      await setPlainText(plainTextRequest);

      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('添加純文本失敗:', error);
    }
  };

  // 處理刪除純文本
  const handleDelete = async (content: string) => {
    if (!userInfo.tenant_id) {
      console.error('缺少用戶信息');
      return;
    }

    try {
      // 構建刪除請求，只在 user_id 存在時才包含
      const deleteRequest: any = {
        tenant_id: userInfo.tenant_id,
        service_id: userInfo.service_id,
        content: content, // 修正：直接傳遞內容
      };

      // 只在 user_id 存在時才添加
      if (userInfo.user_id) {
        deleteRequest.user_id = userInfo.user_id;
      }

      await deletePlainText(deleteRequest);
    } catch (error) {
      console.error('刪除純文本失敗:', error);
    }
  };

  // 處理查看內容
  const handleViewContent = (item: PlainTextAttachment) => {
    setViewingText(item);
    setViewModalVisible(true);
  };

  // 處理取消
  const handleCancel = () => {
    setModalVisible(false);
    form.resetFields();
  };

  // 處理查看模態框關閉
  const handleViewCancel = () => {
    setViewModalVisible(false);
    setViewingText(null);
  };

  // 安全獲取字符串值
  const safeGetString = (value: any): string => {
    if (!value) return '';
    if (typeof value === 'string') return value;
    if (typeof value === 'object') {
      return value.plain_text || value.content || value.text || String(value);
    }
    return String(value);
  };

  // 安全獲取內容長度
  const safeGetContentLength = (content: any): number => {
    const stringContent = safeGetString(content);
    return stringContent.length;
  };

  // 截取內容預覽
  const getContentPreview = (content: any, maxLength: number = 100): string => {
    const stringContent = safeGetString(content);
    if (stringContent.length <= maxLength) {
      return stringContent;
    }
    return stringContent.substring(0, maxLength) + '...';
  };

  return (
    <div className="space-y-4">
      {/* 添加按鈕 */}
      <Button 
        type="primary" 
        icon={<PlusOutlined />} 
        onClick={handleAddPlainText}
        block
        data-testid="add-button"
        style={{ background: 'var(--color-neon-blue)' }}
        disabled={!userInfo.tenant_id}
      >
        添加純文本
      </Button>

      {/* 純文本列表 */}
      {plainTextAttachments.length === 0 ? (
        <Empty 
          description="暫無文本附件" 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          style={{ color: 'var(--color-dark-500)' }}
        />
      ) : (
        <List
          loading={attachmentsLoading}
          dataSource={plainTextAttachments}
          renderItem={(item) => (
            <SpotlightCard
              className="mb-2"
              backgroundColor="var(--color-surface-primary)"
              spotlightColor="rgba(167, 139, 250, 0.2)"
              padding="16px"
              borderRadius={8}
            >
              <List.Item
                className="!p-0 !border-0"
                actions={[
                  <Button
                    key="view"
                    type="text"
                    icon={<MdVisibility />}
                    onClick={() => handleViewContent(item)}
                    style={{ color: 'var(--color-neon-blue)' }}
                  />,
                  <Popconfirm
                    key="delete"
                    title="確定要刪除這個文本嗎？"
                    onConfirm={() => handleDelete(item.content)} // 修正：傳遞 content 而不是 name
                    okText="確定"
                    cancelText="取消"
                  >
                    <Button
                      type="text"
                      icon={<MdDelete />}
                      danger
                    />
                  </Popconfirm>,
                ]}
              >
              <List.Item.Meta
                avatar={<MdDescription style={{ color: 'var(--color-neon-blue)', fontSize: '20px' }} />}
                title={
                  <Text strong style={{ color: 'var(--color-text-primary)' }}>
                    {item.name}
                  </Text>
                }
                description={
                  <Space direction="vertical" size="small">
                    <Text className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                      內容預覽: {getContentPreview(item.content)}
                    </Text>
                    <Text className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                      字數: {safeGetContentLength(item.content)} 字
                    </Text>
                    {item.remark && (
                      <Text className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                        備註: {safeGetString(item.remark)}
                      </Text>
                    )}
                    <Text className="text-xs" style={{ color: 'var(--color-text-tertiary)' }}>
                      添加時間: {new Date(item.created_at).toLocaleString()}
                    </Text>
                  </Space>
                }
              />
            </List.Item>
            </SpotlightCard>
          )}
        />
      )}

      {/* 添加純文本模態框 */}
      <Modal
        title="添加純文本"
        open={modalVisible}
        onCancel={handleCancel}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          autoComplete="off"
        >
          <Form.Item
            label="文本名稱"
            name="name"
            rules={[
              { required: true, message: '請輸入文本名稱' },
              { max: 100, message: '名稱不能超過100個字符' }
            ]}
          >
            <Input
              placeholder="請輸入文本的名稱"
              prefix={<MdDescription />}
            />
          </Form.Item>

          <Form.Item
            label="文本內容"
            name="content"
            rules={[
              { required: true, message: '請輸入文本內容' },
              { max: 10000, message: '內容不能超過10000個字符' }
            ]}
          >
            <Input.TextArea
              placeholder="請輸入文本內容"
              rows={8}
              showCount
              maxLength={10000}
            />
          </Form.Item>

          <Form.Item
            label="備註說明"
            name="remark"
            rules={[
              { required: true, message: '請輸入備註說明' }
            ]}
          >
            <Input.TextArea
              placeholder="請輸入文本的描述或用途說明"
              rows={3}
            />
          </Form.Item>

          <Form.Item className="mb-0">
            <Space className="w-full justify-end">
              <Button onClick={handleCancel}>
                取消
              </Button>
              <Button 
                type="primary" 
                htmlType="submit"
                style={{ background: 'var(--color-neon-blue)' }}
              >
                添加
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 查看內容模態框 */}
      <Modal
        title={`查看文本內容 - ${viewingText?.name}`}
        open={viewModalVisible}
        onCancel={handleViewCancel}
        footer={[
          <Button key="close" onClick={handleViewCancel}>
            關閉
          </Button>
        ]}
        width={700}
      >
        {viewingText && (
          <div className="space-y-4">
            <div>
              <Text strong>內容：</Text>
              <div 
                className="mt-2 p-4 bg-light-100 rounded-lg border max-h-96 overflow-y-auto"
                style={{ whiteSpace: 'pre-wrap' }}
              >
                {viewingText.content}
              </div>
            </div>
            
            {viewingText.remark && (
              <div>
                <Text strong>備註：</Text>
                <div className="mt-1">
                  <Text style={{ color: 'var(--color-text-secondary)' }}>{viewingText.remark}</Text>
                </div>
              </div>
            )}

            <div>
              <Text strong>統計：</Text>
              <div className="mt-1">
                <Text style={{ color: 'var(--color-text-secondary)' }}>
                  字數: {viewingText.content.length} |
                  添加時間: {new Date(viewingText.created_at).toLocaleString()}
                </Text>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default PlainTextTab;
