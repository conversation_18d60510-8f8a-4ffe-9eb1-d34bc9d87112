// React 在 JSX 轉換中自動導入，無需手動導入
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock掉動畫相關，避免需要 MotionProvider
vi.mock('@/components/FramerMotion', () => ({
  ListAnimation: ({ children }: any) => <div>{children}</div>,
  useMotionPreferences: () => ({ prefersReducedMotion: true, animationSpeed: 1 }),
}));

// Mock hooks
vi.mock('@/hooks/useAttachmentWithRefresh', () => ({
  useAttachmentWithRefresh: () => ({ setWebsite: vi.fn(), deleteWebsite: vi.fn() })
}));

const baseUserInfo = { tenant_id: 't1', service_id: 's1', user_id: 'u1' };

function makeItem(overrides: Partial<any> = {}) {
  return {
    id: Math.random().toString(36).slice(2),
    type: 'website',
    name: '測試站',
    url: 'https://example.com',
    created_at: new Date().toISOString(),
    user_info: baseUserInfo,
    remark: 'remark',
    ...overrides,
  };
}

// helper: render with mocked store
async function renderWithStore(items: any[]) {
  vi.doMock('@/hooks/useAppStore', () => ({
    useAppStore: () => ({
      attachments: items,
      userInfo: baseUserInfo,
      attachmentsLoading: false,
    })
  }));
  // 需要在 mock 後以 ESM 動態導入元件
  const Comp = (await import('../WebsiteTab')).default;
  return render(<Comp />);
}

describe('WebsiteTab - 狀態顯示', () => {
  beforeEach(() => {
    vi.resetModules();
  });

  it('無 status 時不顯示「狀態」文字', async () => {
    await renderWithStore([makeItem({ status: undefined })]);
    const statusText = screen.queryByText(/狀態/i);
    expect(statusText).toBeNull();
  });

  it('completed 顯示已完成', async () => {
    await renderWithStore([makeItem({ status: 'completed' })]);
    expect(screen.getByText(/已完成/)).toBeInTheDocument();
  });

  it('processing 顯示處理中', async () => {
    await renderWithStore([makeItem({ status: 'processing' })]);
    expect(screen.getByText(/處理中/)).toBeInTheDocument();
  });

  it('failed 顯示失敗', async () => {
    await renderWithStore([makeItem({ status: 'failed' })]);
    expect(screen.getByText(/失敗/)).toBeInTheDocument();
  });
});

