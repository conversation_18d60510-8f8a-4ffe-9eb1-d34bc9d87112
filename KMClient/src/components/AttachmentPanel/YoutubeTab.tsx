/**
 * YouTube 管理標籤頁組件
 */

import React, { useState } from 'react';
import { Button, List, Space, Popconfirm, Typography, Empty, Modal, Form, Input } from 'antd';
import {
  PlusOutlined
} from '@ant-design/icons';
import {
  MdDelete,
  MdSmartDisplay,
  MdOpenInNew
} from 'react-icons/md';
import { useAppStore } from '@/hooks/useAppStore';
import { useAttachmentWithRefresh } from '@/hooks/useAttachmentWithRefresh';
import { AttachmentType, YoutubeAttachment } from '@/types/attachment';
import { SpotlightCard } from '@/components/ReactBits';

const { Text } = Typography;

interface YoutubeFormData {
  youtube_link: string;
  remark: string;
}

const YoutubeTab: React.FC = () => {
  const { attachments, userInfo, attachmentsLoading } = useAppStore();
  const { setYoutube, deleteYoutube } = useAttachmentWithRefresh();
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm<YoutubeFormData>();

  // 獲取 YouTube 附件
  const youtubeAttachments = attachments.filter(att => att.type === AttachmentType.YOUTUBE) as YoutubeAttachment[];

  // 處理添加 YouTube
  const handleAddYoutube = () => {
    setModalVisible(true);
  };

  // 處理表單提交
  const handleSubmit = async (values: YoutubeFormData) => {
    if (!userInfo.tenant_id) {
      console.error('缺少用戶信息');
      return;
    }

    try {
      // 構建 YouTube 請求，只在 user_id 存在時才包含
      const youtubeRequest: any = {
        tenant_id: userInfo.tenant_id,
        service_id: userInfo.service_id,
        YoutubeContents: [{
          youtube_link: values.youtube_link,
          remark: values.remark,
        }], // 修正：根據 API 錯誤信息調整格式
      };

      // 只在 user_id 存在時才添加
      if (userInfo.user_id) {
        youtubeRequest.user_id = userInfo.user_id;
      }

      await setYoutube(youtubeRequest);

      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('添加 YouTube 失敗:', error);
    }
  };

  // 處理刪除 YouTube
  const handleDelete = async (youtubeLink: string) => {
    if (!userInfo.tenant_id) {
      console.error('缺少用戶信息');
      return;
    }

    try {
      // 構建刪除請求，只在 user_id 存在時才包含
      const deleteRequest: any = {
        tenant_id: userInfo.tenant_id,
        service_id: userInfo.service_id,
        youtube_links: [youtubeLink], // 修正：直接傳遞字符串數組
      };

      // 只在 user_id 存在時才添加
      if (userInfo.user_id) {
        deleteRequest.user_id = userInfo.user_id;
      }

      await deleteYoutube(deleteRequest);
    } catch (error) {
      console.error('刪除 YouTube 失敗:', error);
    }
  };

  // 處理取消
  const handleCancel = () => {
    setModalVisible(false);
    form.resetFields();
  };

  // 驗證 YouTube URL 格式
  const validateYoutubeUrl = (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error('請輸入 YouTube 連結'));
    }
    
    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+/;
    if (!youtubeRegex.test(value)) {
      return Promise.reject(new Error('請輸入有效的 YouTube 連結'));
    }
    
    return Promise.resolve();
  };

  // 提取 YouTube 影片 ID
  const extractVideoId = (url: string): string | null => {
    if (!url || typeof url !== 'string') {
      return null;
    }
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const match = url.match(regex);
    return match ? match[1] : null;
  };

  // 生成縮圖 URL
  const getThumbnailUrl = (url: string): string => {
    if (!url || typeof url !== 'string') {
      return '';
    }
    const videoId = extractVideoId(url);
    return videoId ? `https://img.youtube.com/vi/${videoId}/mqdefault.jpg` : '';
  };

  return (
    <div className="space-y-4">
      {/* 添加按鈕 */}
      <Button 
        type="primary" 
        icon={<PlusOutlined />} 
        onClick={handleAddYoutube}
        block
        data-testid="add-button"
        style={{ background: 'var(--color-neon-blue)' }}
        disabled={!userInfo.tenant_id}
      >
        添加 YouTube 影片
      </Button>

      {/* YouTube 列表 */}
      {youtubeAttachments.length === 0 ? (
        <Empty 
          description="暫無影片附件" 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          style={{ color: 'var(--color-dark-500)' }}
        />
      ) : (
        <List
          loading={attachmentsLoading}
          dataSource={youtubeAttachments}
          renderItem={(item) => (
            <SpotlightCard
              className="mb-2"
              backgroundColor="var(--color-surface-primary)"
              spotlightColor="rgba(167, 139, 250, 0.2)"
              padding="16px"
              borderRadius={8}
            >
              <List.Item
                className="!p-0 !border-0"
                actions={[
                  <Button
                    key="visit"
                    type="text"
                    icon={<MdOpenInNew />}
                    onClick={() => window.open(item.youtube_link, '_blank')}
                    style={{ color: 'var(--color-neon-blue)' }}
                  />,
                  <Popconfirm
                    key="delete"
                    title="確定要刪除這個影片嗎？"
                    onConfirm={() => handleDelete(item.youtube_link)}
                    okText="確定"
                    cancelText="取消"
                  >
                    <Button
                      type="text"
                      icon={<MdDelete />}
                      danger
                    />
                  </Popconfirm>,
                ]}
              >
              <List.Item.Meta
                avatar={
                  <div className="flex items-center">
                    {getThumbnailUrl(item.youtube_link) ? (
                      <img 
                        src={getThumbnailUrl(item.youtube_link)} 
                        alt="YouTube thumbnail"
                        className="w-16 h-12 object-cover rounded"
                        onError={(e) => {
                          (e.target as HTMLImageElement).style.display = 'none';
                          const nextElement = (e.target as HTMLImageElement).nextElementSibling as HTMLElement;
                          if (nextElement) {
                            nextElement.style.display = 'block';
                          }
                        }}
                      />
                    ) : null}
                    <MdSmartDisplay
                      style={{
                        color: 'var(--color-neon-blue)',
                        fontSize: '20px',
                        display: getThumbnailUrl(item.youtube_link) ? 'none' : 'block'
                      }}
                    />
                  </div>
                }
                title={
                  <Text strong style={{ color: 'var(--color-text-primary)' }}>
                    {item.name}
                  </Text>
                }
                description={
                  <Space direction="vertical" size="small">
                    <Text
                      className="text-sm break-all"
                      style={{ color: 'var(--color-neon-blue)' }}
                    >
                      {item.youtube_link}
                    </Text>
                    {item.remark && (
                      <Text className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                        備註: {item.remark}
                      </Text>
                    )}
                    <Text className="text-xs" style={{ color: 'var(--color-text-tertiary)' }}>
                      添加時間: {new Date(item.created_at).toLocaleString()}
                    </Text>
                  </Space>
                }
              />
            </List.Item>
            </SpotlightCard>
          )}
        />
      )}

      {/* 添加 YouTube 模態框 */}
      <Modal
        title="添加 YouTube 影片"
        open={modalVisible}
        onCancel={handleCancel}
        footer={null}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          autoComplete="off"
        >
          <Form.Item
            label="YouTube 連結"
            name="youtube_link"
            rules={[
              { validator: validateYoutubeUrl }
            ]}
          >
            <Input
              placeholder="https://www.youtube.com/watch?v=..."
              prefix={<MdSmartDisplay />}
            />
          </Form.Item>

          <Form.Item
            label="備註說明"
            name="remark"
            rules={[
              { required: true, message: '請輸入備註說明' }
            ]}
          >
            <Input.TextArea
              placeholder="請輸入影片的描述或用途說明"
              rows={3}
            />
          </Form.Item>

          <Form.Item className="mb-0">
            <Space className="w-full justify-end">
              <Button onClick={handleCancel}>
                取消
              </Button>
              <Button 
                type="primary" 
                htmlType="submit"
                style={{ background: 'var(--color-neon-blue)' }}
              >
                添加
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default YoutubeTab;
