/**
 * 圖片上傳按鈕組件
 */

import React, { useRef } from 'react';
import { Button, Tooltip } from 'antd';
import { CameraOutlined, LoadingOutlined } from '@ant-design/icons';
import { MediaUploadStatus } from '@/types/media';

interface ImageUploadButtonProps {
  onFileSelect: (file: File) => void;
  loading?: boolean;
  disabled?: boolean;
  uploadStatus?: MediaUploadStatus;
  className?: string;
}

const ImageUploadButton: React.FC<ImageUploadButtonProps> = ({
  onFileSelect,
  loading = false,
  disabled = false,
  uploadStatus = MediaUploadStatus.IDLE,
  className = ''
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  /**
   * 處理文件選擇
   */
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 驗證是否為圖片文件
      if (!file.type.startsWith('image/')) {
        console.warn('選擇的文件不是圖片格式');
        return;
      }
      
      onFileSelect(file);
    }
    
    // 清空 input 值，允許重複選擇同一文件
    event.target.value = '';
  };

  /**
   * 觸發文件選擇
   */
  const handleClick = () => {
    if (fileInputRef.current && !disabled && !loading) {
      fileInputRef.current.click();
    }
  };

  /**
   * 獲取按鈕狀態
   */
  const getButtonState = () => {
    if (loading || uploadStatus === MediaUploadStatus.PROCESSING) {
      return {
        icon: <LoadingOutlined spin />,
        tooltip: '處理中...',
        type: 'default' as const
      };
    }
    
    if (uploadStatus === MediaUploadStatus.UPLOADING) {
      return {
        icon: <LoadingOutlined spin />,
        tooltip: '上傳中...',
        type: 'primary' as const
      };
    }
    
    if (uploadStatus === MediaUploadStatus.SUCCESS) {
      return {
        icon: <CameraOutlined />,
        tooltip: '圖片上傳成功',
        type: 'primary' as const
      };
    }
    
    if (uploadStatus === MediaUploadStatus.ERROR) {
      return {
        icon: <CameraOutlined />,
        tooltip: '圖片上傳失敗，點擊重試',
        type: 'default' as const
      };
    }
    
    return {
      icon: <CameraOutlined />,
      tooltip: '選擇圖片',
      type: 'default' as const
    };
  };

  const buttonState = getButtonState();

  return (
    <>
      <Tooltip title={buttonState.tooltip} placement="top">
        <Button
          type={buttonState.type}
          icon={buttonState.icon}
          onClick={handleClick}
          disabled={disabled}
          loading={loading}
          className={`media-input-button ${className}`}
          style={{
            borderRadius: '6px',
            height: '36px',
            width: '36px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: uploadStatus === MediaUploadStatus.ERROR ? '1px solid #ff4d4f' : undefined,
            backgroundColor: uploadStatus === MediaUploadStatus.SUCCESS ? '#f6ffed' : undefined,
            borderColor: uploadStatus === MediaUploadStatus.SUCCESS ? '#b7eb8f' : undefined
          }}
        />
      </Tooltip>
      
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        style={{ display: 'none' }}
        multiple={false}
      />
    </>
  );
};

export default ImageUploadButton;
