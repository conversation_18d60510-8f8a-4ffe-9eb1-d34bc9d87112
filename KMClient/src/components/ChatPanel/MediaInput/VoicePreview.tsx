/**
 * 語音預覽組件
 */

import React, { useState, useRef, useEffect } from 'react';
import { Card, Button, Space, Typography, Slider } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  SendOutlined,
  CloseOutlined,
  SoundOutlined
} from '@ant-design/icons';
import { formatDuration, formatFileSize } from '@/utils/mediaUtils';
import AudioWaveform from './AudioWaveform';

const { Text } = Typography;

interface VoicePreviewProps {
  audioBlob: Blob;
  audioUrl: string;
  duration: number;
  onSend: () => void;
  onCancel: () => void;
  loading?: boolean;
  className?: string;
}

const VoicePreview: React.FC<VoicePreviewProps> = ({
  audioBlob,
  audioUrl,
  duration,
  onSend,
  onCancel,
  loading = false,
  className = ''
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [audioDuration, setAudioDuration] = useState(duration);
  const audioRef = useRef<HTMLAudioElement>(null);

  // 防重複點擊處理
  const handleSend = () => {
    if (loading) {
      console.log('⚠️ 正在發送中，忽略重複點擊');
      return;
    }
    onSend();
  };

  /**
   * 檢查音頻數據完整性
   */
  const checkAudioData = () => {
    console.log('🔍 檢查音頻數據:', {
      audioUrl: audioUrl,
      audioBlob: audioBlob,
      blobSize: audioBlob?.size,
      blobType: audioBlob?.type,
      duration: duration,
      urlValid: audioUrl && audioUrl.startsWith('blob:')
    });

    if (!audioUrl || !audioBlob) {
      console.error('❌ 音頻數據缺失');
      return false;
    }

    if (audioBlob.size === 0) {
      console.error('❌ 音頻數據為空');
      return false;
    }

    if (!audioUrl.startsWith('blob:')) {
      console.error('❌ 音頻 URL 格式錯誤');
      return false;
    }

    console.log('✅ 音頻數據檢查通過');
    return true;
  };

  /**
   * 測試音頻播放能力
   */
  const testAudioPlayback = async () => {
    const audio = audioRef.current;
    if (!audio) return false;

    try {
      console.log('🧪 測試音頻播放能力...');

      // 檢查音頻元素狀態
      console.log('🎵 音頻元素詳細狀態:', {
        src: audio.src,
        readyState: audio.readyState,
        networkState: audio.networkState,
        duration: audio.duration,
        volume: audio.volume,
        muted: audio.muted,
        paused: audio.paused,
        ended: audio.ended,
        error: audio.error,
        currentTime: audio.currentTime
      });

      // 檢查瀏覽器音頻支援
      const canPlayWebm = audio.canPlayType('audio/webm');
      const canPlayMp4 = audio.canPlayType('audio/mp4');
      const canPlayOgg = audio.canPlayType('audio/ogg');
      const canPlayWav = audio.canPlayType('audio/wav');

      console.log('🎵 瀏覽器音頻格式支援:', {
        webm: canPlayWebm,
        mp4: canPlayMp4,
        ogg: canPlayOgg,
        wav: canPlayWav
      });

      // 嘗試創建一個簡單的測試音頻（測試音頻上下文）
      try {
        const testAudioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
        console.log('✅ 音頻上下文創建成功');
        testAudioContext.close();
      } catch (contextError) {
        console.error('❌ 音頻上下文創建失敗:', contextError);
      }

      return true;
    } catch (error) {
      console.error('❌ 音頻播放能力測試失敗:', error);
      return false;
    }
  };

  /**
   * 初始化音頻
   */
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) {
      console.warn('⚠️ 音頻元素不存在');
      return;
    }

    console.log('🎵 初始化音頻元素:', {
      src: audioUrl,
      duration: duration,
      volume: audio.volume,
      muted: audio.muted
    });

    // 檢查音頻數據完整性
    checkAudioData();

    // 測試音頻播放能力
    testAudioPlayback();

    const handleLoadedMetadata = () => {
      console.log('📊 音頻元數據加載完成:', {
        duration: audio.duration,
        readyState: audio.readyState
      });
      setAudioDuration(audio.duration);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleEnded = () => {
      console.log('🏁 音頻播放結束');
      setIsPlaying(false);
      setCurrentTime(0);
    };

    const handleError = (e: Event) => {
      const target = e.target as HTMLAudioElement;
      console.error('❌ 音頻播放錯誤:', {
        error: target.error,
        networkState: target.networkState,
        readyState: target.readyState,
        src: target.src
      });
      setIsPlaying(false);
    };

    const handleCanPlay = () => {
      console.log('✅ 音頻可以播放');
    };

    const handleLoadStart = () => {
      console.log('🔄 音頻開始加載');
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('loadstart', handleLoadStart);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('loadstart', handleLoadStart);
    };
  }, [audioUrl, duration]);

  /**
   * 播放/暫停切換
   */
  const togglePlayPause = async () => {
    const audio = audioRef.current;
    if (!audio) {
      console.error('❌ 音頻元素不存在');
      return;
    }

    console.log('🎵 當前播放狀態:', isPlaying);
    console.log('🎵 音頻源:', audio.src);
    console.log('🎵 音頻就緒狀態:', audio.readyState);
    console.log('🎵 音頻音量:', audio.volume);
    console.log('🎵 音頻靜音狀態:', audio.muted);

    try {
      if (isPlaying) {
        audio.pause();
        setIsPlaying(false);
        console.log('⏸️ 音頻已暫停');
      } else {
        // 強制重新設置音頻屬性
        console.log('🔄 重新設置音頻屬性...');
        audio.volume = 1.0;
        audio.muted = false;

        // 如果音頻源不正確，重新設置
        if (audio.src !== audioUrl) {
          console.log('🔄 重新設置音頻源:', audioUrl);
          audio.src = audioUrl;
          audio.load(); // 強制重新加載
        }

        // 確保音頻已加載
        if (audio.readyState < 2) {
          console.log('⏳ 等待音頻加載...');
          await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
              reject(new Error('音頻加載超時'));
            }, 10000); // 增加超時時間

            const onCanPlay = () => {
              clearTimeout(timeout);
              audio.removeEventListener('canplay', onCanPlay);
              audio.removeEventListener('error', onError);
              console.log('✅ 音頻加載完成，可以播放');
              resolve(void 0);
            };

            const onError = (e: Event) => {
              clearTimeout(timeout);
              audio.removeEventListener('canplay', onCanPlay);
              audio.removeEventListener('error', onError);
              console.error('❌ 音頻加載失敗:', e);
              reject(e);
            };

            audio.addEventListener('canplay', onCanPlay);
            audio.addEventListener('error', onError);
          });
        }

        // 嘗試播放前的最終檢查
        console.log('🎵 播放前最終狀態:', {
          src: audio.src,
          volume: audio.volume,
          muted: audio.muted,
          readyState: audio.readyState,
          duration: audio.duration,
          currentTime: audio.currentTime
        });

        // 嘗試播放
        console.log('▶️ 開始播放音頻...');
        await audio.play();
        setIsPlaying(true);
        console.log('✅ 音頻播放成功');
      }
    } catch (error: any) {
      console.error('❌ 音頻播放失敗:', error);
      setIsPlaying(false);

      // 處理不同類型的播放錯誤
      if (error.name === 'NotAllowedError') {
        console.error('❌ 瀏覽器阻止了音頻播放（需要用戶交互）');
        // 可以在這裡顯示用戶提示
      } else if (error.name === 'NotSupportedError') {
        console.error('❌ 音頻格式不被支援');
      } else if (error.name === 'AbortError') {
        console.error('❌ 音頻播放被中止');
      } else {
        console.error('❌ 未知的音頻播放錯誤:', error.message);
      }
    }
  };

  /**
   * 拖動進度條
   */
  const handleSeek = (value: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = (value / 100) * audioDuration;
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  /**
   * 獲取播放進度百分比
   */
  const getProgressPercent = () => {
    if (audioDuration === 0) return 0;
    return (currentTime / audioDuration) * 100;
  };

  /**
   * 設置音量
   */
  const handleVolumeChange = (value: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    const volume = value / 100;
    audio.volume = volume;
    console.log('🔊 音量設置為:', volume);
  };

  return (
    <Card
      className={`voice-preview-card ${className}`}
      style={{
        width: '100%',
        maxWidth: '400px',
        borderRadius: '8px'
      }}
      bodyStyle={{ padding: '16px' }}
    >
      {/* 隱藏的音頻元素 */}
      <audio
        ref={audioRef}
        src={audioUrl}
        preload="metadata"
        muted={false}
        playsInline
        crossOrigin="anonymous"
        onLoadStart={() => {
          console.log('🎵 音頻開始加載');
          // 設置音量
          if (audioRef.current) {
            audioRef.current.volume = 1.0;
          }
        }}
        onCanPlay={() => {
          console.log('🎵 音頻可以播放');
          // 再次確保音量設置
          if (audioRef.current) {
            audioRef.current.volume = 1.0;
            console.log('🔊 音量設置為:', audioRef.current.volume);
          }
        }}
        onLoadedData={() => console.log('🎵 音頻數據加載完成')}
        onError={(e) => console.error('❌ 音頻加載錯誤:', e)}
      />

      {/* 語音信息 */}
      <div className="mb-4">
        <div className="flex items-center mb-2">
          <SoundOutlined className="text-blue-500 mr-2" />
          <Text strong>語音消息</Text>
        </div>
        <Text type="secondary" className="text-xs">
          {formatDuration(audioDuration)} • {formatFileSize(audioBlob.size)}
        </Text>
      </div>

      {/* 播放控制 */}
      <div className="mb-4">
        <div className="flex items-center space-x-3 mb-2">
          <Button
            type="text"
            icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={togglePlayPause}
            size="large"
            className="flex items-center justify-center"
            style={{
              fontSize: '24px',
              color: '#1890ff',
              padding: 0,
              height: 'auto',
              width: 'auto'
            }}
          />
          
          <div className="flex-1">
            <div className="text-xs text-gray-500 mb-1">
              {formatDuration(currentTime)} / {formatDuration(audioDuration)}
            </div>
            <Slider
              value={getProgressPercent()}
              onChange={handleSeek}
              tooltip={{ formatter: null }}
              trackStyle={{ backgroundColor: '#1890ff' }}
              handleStyle={{ borderColor: '#1890ff' }}
              className="mb-0"
            />
          </div>
        </div>

        {/* 音量控制 */}
        <div className="flex items-center space-x-2 mb-3">
          <SoundOutlined className="text-gray-500" />
          <Slider
            min={0}
            max={100}
            defaultValue={100}
            onChange={handleVolumeChange}
            tooltip={{ formatter: (value) => `${value}%` }}
            trackStyle={{ backgroundColor: '#1890ff' }}
            handleStyle={{ borderColor: '#1890ff' }}
            className="flex-1"
            style={{ width: '120px' }}
          />
        </div>

        {/* 音頻波形可視化 */}
        <div className="flex justify-center">
          <AudioWaveform
            staticWaveform={true}
            playProgress={getProgressPercent()}
            width={320}
            height={40}
            barCount={20}
            color="#1890ff"
            backgroundColor="#f5f5f5"
          />
        </div>
      </div>

      {/* 操作按鈕 */}
      <Space className="w-full justify-end">
        <Button
          icon={<CloseOutlined />}
          onClick={onCancel}
          disabled={loading}
          size="small"
        >
          取消
        </Button>
        <Button
          type="primary"
          icon={<SendOutlined />}
          onClick={handleSend}
          loading={loading}
          disabled={loading}
          size="small"
          className="disabled:bg-gray-400"
        >
          {loading ? '發送中...' : '發送語音'}
        </Button>
      </Space>
    </Card>
  );
};

export default VoicePreview;
