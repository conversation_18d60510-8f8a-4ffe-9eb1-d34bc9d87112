/**
 * 音頻波形可視化組件
 */

import React, { useEffect, useRef, useState } from 'react';

interface AudioWaveformProps {
  /** 音頻分析器節點 */
  analyser?: AnalyserNode;
  /** 是否正在錄音 */
  isRecording?: boolean;
  /** 音量級別 (0-100) */
  volume?: number;
  /** 波形寬度 */
  width?: number;
  /** 波形高度 */
  height?: number;
  /** 波形條數量 */
  barCount?: number;
  /** 波形顏色 */
  color?: string;
  /** 背景顏色 */
  backgroundColor?: string;
  /** 是否顯示靜態波形（用於預覽） */
  staticWaveform?: boolean;
  /** 靜態波形數據 */
  staticData?: number[];
  /** 播放進度 (0-100) */
  playProgress?: number;
  /** 樣式類名 */
  className?: string;
}

const AudioWaveform: React.FC<AudioWaveformProps> = ({
  analyser,
  isRecording = false,
  volume = 0,
  width = 200,
  height = 40,
  barCount = 20,
  color = '#1890ff',
  backgroundColor = '#f0f0f0',
  staticWaveform = false,
  staticData,
  playProgress = 0,
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number | null>(null);
  const [_frequencyData, setFrequencyData] = useState<Uint8Array | null>(null);

  /**
   * 繪製波形
   */
  const drawWaveform = (data: Uint8Array | number[], progress = 0) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清空畫布
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, width, height);

    const barWidth = width / barCount;
    const barSpacing = barWidth * 0.2;
    const actualBarWidth = barWidth - barSpacing;

    // 繪製波形條
    for (let i = 0; i < barCount; i++) {
      let barHeight: number;
      
      if (staticWaveform && staticData) {
        // 靜態波形模式
        barHeight = staticData[i] || 0;
        // 根據播放進度調整透明度
        const progressRatio = progress / 100;
        const barProgress = i / barCount;
        const opacity = barProgress <= progressRatio ? 1 : 0.3;
        ctx.globalAlpha = opacity;
      } else {
        // 實時波形模式
        const dataIndex = Math.floor((i / barCount) * data.length);
        const value = data[dataIndex] || 0;
        barHeight = (value / 255) * height;
        
        // 添加一些隨機性使波形更自然
        if (isRecording && volume > 0) {
          barHeight += Math.random() * 5;
        }
        
        ctx.globalAlpha = 1;
      }

      // 限制最小和最大高度
      barHeight = Math.max(2, Math.min(barHeight, height - 2));

      const x = i * barWidth + barSpacing / 2;
      const y = (height - barHeight) / 2;

      // 繪製波形條
      ctx.fillStyle = color;
      ctx.fillRect(x, y, actualBarWidth, barHeight);
    }

    ctx.globalAlpha = 1;
  };

  /**
   * 實時更新波形
   */
  const updateWaveform = () => {
    if (!analyser || !isRecording) return;

    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    analyser.getByteFrequencyData(dataArray);

    setFrequencyData(dataArray);
    drawWaveform(dataArray);

    animationFrameRef.current = requestAnimationFrame(updateWaveform);
  };

  /**
   * 生成靜態波形數據
   */
  const generateStaticWaveform = () => {
    if (staticData) {
      return staticData;
    }

    // 生成模擬的波形數據
    return Array.from({ length: barCount }, (_, i) => {
      const progress = i / barCount;
      const baseHeight = Math.sin(progress * Math.PI * 4) * 0.5 + 0.5;
      const randomFactor = Math.random() * 0.3 + 0.7;
      return (baseHeight * randomFactor * height * 0.8) + (height * 0.1);
    });
  };

  // 實時波形更新
  useEffect(() => {
    if (isRecording && analyser) {
      updateWaveform();
    } else if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isRecording, analyser]);

  // 靜態波形繪製
  useEffect(() => {
    if (staticWaveform) {
      const data = generateStaticWaveform();
      drawWaveform(data, playProgress);
    }
  }, [staticWaveform, staticData, playProgress, width, height, barCount]);

  // 初始化畫布
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // 設置畫布尺寸
    canvas.width = width;
    canvas.height = height;

    // 初始繪製
    if (staticWaveform) {
      const data = generateStaticWaveform();
      drawWaveform(data, playProgress);
    } else if (!isRecording) {
      // 繪製靜態背景
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = backgroundColor;
        ctx.fillRect(0, 0, width, height);
        
        // 繪製靜態波形線
        const centerY = height / 2;
        ctx.strokeStyle = color;
        ctx.globalAlpha = 0.3;
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(0, centerY);
        ctx.lineTo(width, centerY);
        ctx.stroke();
        ctx.globalAlpha = 1;
      }
    }
  }, [width, height, backgroundColor, color]);

  return (
    <div className={`audio-waveform ${className}`}>
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        style={{
          width: `${width}px`,
          height: `${height}px`,
          borderRadius: '4px',
          border: `1px solid ${backgroundColor}`,
          background: backgroundColor
        }}
      />
      
      {/* 錄音狀態指示 */}
      {isRecording && (
        <div className="flex items-center justify-center mt-2">
          <div className="flex items-center space-x-2">
            <div 
              className="w-2 h-2 bg-red-500 rounded-full animate-pulse"
              style={{
                animationDuration: '1s'
              }}
            />
            <span className="text-xs text-red-500 font-medium">
              錄音中
            </span>
          </div>
        </div>
      )}
      
      {/* 音量指示器 */}
      {isRecording && volume > 0 && (
        <div className="mt-1">
          <div className="w-full bg-gray-200 rounded-full h-1">
            <div 
              className="bg-gradient-to-r from-green-400 to-red-500 h-1 rounded-full transition-all duration-100"
              style={{ width: `${volume}%` }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default AudioWaveform;
