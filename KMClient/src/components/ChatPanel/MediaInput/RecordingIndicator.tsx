/**
 * 錄音狀態指示器組件
 */

import React from 'react';
import { Card, Button, Space, Typography } from 'antd';
import { StopOutlined, PauseOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { VoiceRecordStatus } from '@/types/media';
import { formatDuration } from '@/utils/mediaUtils';
import AudioWaveform from './AudioWaveform';

const { Text } = Typography;

interface RecordingIndicatorProps {
  /** 錄音狀態 */
  status: VoiceRecordStatus;
  /** 錄音時長（秒） */
  duration: number;
  /** 音量級別 (0-100) */
  volume: number;
  /** 最大錄音時長（秒） */
  maxDuration: number;
  /** 音頻分析器節點 */
  analyser?: AnalyserNode;
  /** 停止錄音回調 */
  onStop: () => void;
  /** 暫停錄音回調 */
  onPause: () => void;
  /** 恢復錄音回調 */
  onResume: () => void;
  /** 樣式類名 */
  className?: string;
}

const RecordingIndicator: React.FC<RecordingIndicatorProps> = ({
  status,
  duration,
  volume,
  maxDuration,
  analyser,
  onStop,
  onPause,
  onResume,
  className = ''
}) => {
  const isRecording = status === VoiceRecordStatus.RECORDING;
  const isPaused = status === VoiceRecordStatus.PAUSED;
  const isProcessing = status === VoiceRecordStatus.PROCESSING;

  // 計算進度百分比
  const progressPercent = (duration / maxDuration) * 100;

  // 獲取狀態文字和顏色
  const getStatusInfo = () => {
    switch (status) {
      case VoiceRecordStatus.RECORDING:
        return {
          text: '錄音中',
          color: '#ff4d4f',
          bgColor: '#fff2f0',
          borderColor: '#ffccc7'
        };
      case VoiceRecordStatus.PAUSED:
        return {
          text: '已暫停',
          color: '#faad14',
          bgColor: '#fffbe6',
          borderColor: '#ffe58f'
        };
      case VoiceRecordStatus.PROCESSING:
        return {
          text: '處理中',
          color: '#1890ff',
          bgColor: '#e6f7ff',
          borderColor: '#91d5ff'
        };
      default:
        return {
          text: '準備中',
          color: '#52c41a',
          bgColor: '#f6ffed',
          borderColor: '#b7eb8f'
        };
    }
  };

  const statusInfo = getStatusInfo();

  if (status === VoiceRecordStatus.IDLE || status === VoiceRecordStatus.STOPPED) {
    return null;
  }

  return (
    <Card
      className={`recording-indicator ${className}`}
      style={{
        width: '100%',
        maxWidth: '400px',
        borderRadius: '12px',
        backgroundColor: statusInfo.bgColor,
        borderColor: statusInfo.borderColor,
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
      }}
      bodyStyle={{ padding: '16px' }}
    >
      {/* 狀態標題 */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <div 
            className="w-3 h-3 rounded-full"
            style={{
              backgroundColor: statusInfo.color,
              animation: isRecording ? 'pulse 1s infinite' : 'none'
            }}
          />
          <Text 
            strong 
            style={{ color: statusInfo.color, fontSize: '14px' }}
          >
            {statusInfo.text}
          </Text>
        </div>
        
        <Text 
          type="secondary" 
          style={{ fontSize: '12px' }}
        >
          {formatDuration(duration)} / {formatDuration(maxDuration)}
        </Text>
      </div>

      {/* 音頻波形顯示 */}
      <div className="mb-3">
        <AudioWaveform
          analyser={analyser}
          isRecording={isRecording}
          volume={volume}
          width={350}
          height={50}
          barCount={25}
          color={statusInfo.color}
          backgroundColor="rgba(255, 255, 255, 0.8)"
        />
      </div>

      {/* 進度條 */}
      <div className="mb-3">
        <div className="w-full bg-white bg-opacity-50 rounded-full h-2 overflow-hidden">
          <div 
            className="h-full rounded-full transition-all duration-300"
            style={{
              width: `${progressPercent}%`,
              background: `linear-gradient(90deg, ${statusInfo.color}, ${statusInfo.color}dd)`
            }}
          />
        </div>
        <div className="flex justify-between mt-1">
          <Text type="secondary" style={{ fontSize: '10px' }}>
            0:00
          </Text>
          <Text type="secondary" style={{ fontSize: '10px' }}>
            {formatDuration(maxDuration)}
          </Text>
        </div>
      </div>

      {/* 控制按鈕 */}
      <Space className="w-full justify-center">
        {isPaused ? (
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={onResume}
            size="small"
            style={{
              backgroundColor: statusInfo.color,
              borderColor: statusInfo.color
            }}
          >
            繼續錄音
          </Button>
        ) : (
          <Button
            icon={<PauseOutlined />}
            onClick={onPause}
            disabled={isProcessing}
            size="small"
          >
            暫停
          </Button>
        )}
        
        <Button
          type="primary"
          danger
          icon={<StopOutlined />}
          onClick={onStop}
          disabled={isProcessing}
          size="small"
        >
          {isProcessing ? '處理中...' : '停止錄音'}
        </Button>
      </Space>

      {/* 提示文字 */}
      <div className="mt-2 text-center">
        <Text type="secondary" style={{ fontSize: '11px' }}>
          {isRecording && '正在錄製您的語音消息...'}
          {isPaused && '錄音已暫停，點擊繼續錄音'}
          {isProcessing && '正在處理錄音文件，請稍候...'}
        </Text>
      </div>

      {/* CSS 動畫樣式 */}
      <style>{`
        @keyframes pulse {
          0% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.7;
            transform: scale(1.1);
          }
          100% {
            opacity: 1;
            transform: scale(1);
          }
        }
      `}</style>
    </Card>
  );
};

export default RecordingIndicator;
