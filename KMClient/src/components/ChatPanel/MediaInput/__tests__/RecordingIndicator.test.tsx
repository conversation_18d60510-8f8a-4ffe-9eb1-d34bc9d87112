/**
 * RecordingIndicator 組件測試
 */

import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import RecordingIndicator from '../RecordingIndicator';
import { VoiceRecordStatus } from '@/types/media';

// Mock formatDuration 函數
vi.mock('@/utils/mediaUtils', () => ({
  formatDuration: (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }
}));

describe('RecordingIndicator', () => {
  const mockOnStop = vi.fn();
  const mockOnPause = vi.fn();
  const mockOnResume = vi.fn();

  const defaultProps = {
    status: VoiceRecordStatus.RECORDING,
    duration: 15.5,
    volume: 75,
    maxDuration: 60,
    onStop: mockOnStop,
    onPause: mockOnPause,
    onResume: mockOnResume
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('錄音狀態顯示', () => {
    it('應該在錄音狀態下正確顯示', () => {
      render(<RecordingIndicator {...defaultProps} />);
      
      expect(screen.getByText('錄音中')).toBeInTheDocument();
      expect(screen.getByText('0:15 / 1:00')).toBeInTheDocument();
      expect(screen.getByText('正在錄製您的語音消息...')).toBeInTheDocument();
    });

    it('應該在暫停狀態下正確顯示', () => {
      render(
        <RecordingIndicator 
          {...defaultProps} 
          status={VoiceRecordStatus.PAUSED}
        />
      );
      
      expect(screen.getByText('已暫停')).toBeInTheDocument();
      expect(screen.getByText('錄音已暫停，點擊繼續錄音')).toBeInTheDocument();
      expect(screen.getByText('繼續錄音')).toBeInTheDocument();
    });

    it('應該在處理狀態下正確顯示', () => {
      render(
        <RecordingIndicator 
          {...defaultProps} 
          status={VoiceRecordStatus.PROCESSING}
        />
      );
      
      expect(screen.getByText('處理中')).toBeInTheDocument();
      expect(screen.getByText('正在處理錄音文件，請稍候...')).toBeInTheDocument();
      expect(screen.getByText('處理中...')).toBeInTheDocument();
    });

    it('應該在閒置或停止狀態下不顯示', () => {
      const { container: idleContainer } = render(
        <RecordingIndicator 
          {...defaultProps} 
          status={VoiceRecordStatus.IDLE}
        />
      );
      expect(idleContainer.firstChild).toBeNull();

      const { container: stoppedContainer } = render(
        <RecordingIndicator 
          {...defaultProps} 
          status={VoiceRecordStatus.STOPPED}
        />
      );
      expect(stoppedContainer.firstChild).toBeNull();
    });
  });

  describe('控制按鈕功能', () => {
    it('應該在錄音狀態下顯示暫停和停止按鈕', () => {
      render(<RecordingIndicator {...defaultProps} />);
      
      expect(screen.getByText('暫停')).toBeInTheDocument();
      expect(screen.getByText('停止錄音')).toBeInTheDocument();
    });

    it('應該正確處理暫停按鈕點擊', () => {
      render(<RecordingIndicator {...defaultProps} />);
      
      const pauseButton = screen.getByText('暫停');
      fireEvent.click(pauseButton);
      
      expect(mockOnPause).toHaveBeenCalledTimes(1);
    });

    it('應該正確處理停止按鈕點擊', () => {
      render(<RecordingIndicator {...defaultProps} />);
      
      const stopButton = screen.getByText('停止錄音');
      fireEvent.click(stopButton);
      
      expect(mockOnStop).toHaveBeenCalledTimes(1);
    });

    it('應該在暫停狀態下顯示繼續錄音按鈕', () => {
      render(
        <RecordingIndicator 
          {...defaultProps} 
          status={VoiceRecordStatus.PAUSED}
        />
      );
      
      const resumeButton = screen.getByText('繼續錄音');
      fireEvent.click(resumeButton);
      
      expect(mockOnResume).toHaveBeenCalledTimes(1);
    });

    it('應該在處理狀態下禁用控制按鈕', () => {
      render(
        <RecordingIndicator 
          {...defaultProps} 
          status={VoiceRecordStatus.PROCESSING}
        />
      );
      
      const pauseButton = screen.getByText('暫停');
      const stopButton = screen.getByText('處理中...');
      
      expect(pauseButton).toBeDisabled();
      expect(stopButton).toBeDisabled();
    });
  });

  describe('進度顯示', () => {
    it('應該正確計算和顯示進度百分比', () => {
      render(
        <RecordingIndicator 
          {...defaultProps} 
          duration={30}
          maxDuration={60}
        />
      );
      
      // 檢查時間顯示
      expect(screen.getByText('0:30 / 1:00')).toBeInTheDocument();
      
      // 進度條應該顯示 50% (30/60)
      const progressBar = document.querySelector('[style*="width: 50%"]');
      expect(progressBar).toBeInTheDocument();
    });

    it('應該顯示正確的時間格式', () => {
      render(
        <RecordingIndicator 
          {...defaultProps} 
          duration={125.7}
          maxDuration={180}
        />
      );
      
      expect(screen.getByText('2:05 / 3:00')).toBeInTheDocument();
    });
  });

  describe('音頻波形顯示', () => {
    it('應該渲染 AudioWaveform 組件', () => {
      render(<RecordingIndicator {...defaultProps} />);
      
      // 檢查是否有 canvas 元素（AudioWaveform 的一部分）
      const canvas = document.querySelector('canvas');
      expect(canvas).toBeInTheDocument();
    });

    it('應該傳遞正確的 props 給 AudioWaveform', () => {
      const mockAnalyser = {} as AnalyserNode;
      
      render(
        <RecordingIndicator 
          {...defaultProps} 
          analyser={mockAnalyser}
        />
      );
      
      // AudioWaveform 應該接收到正確的 props
      const canvas = document.querySelector('canvas');
      expect(canvas).toHaveAttribute('width', '350');
      expect(canvas).toHaveAttribute('height', '50');
    });
  });

  describe('狀態樣式', () => {
    it('應該根據錄音狀態應用正確的顏色', () => {
      const { rerender } = render(<RecordingIndicator {...defaultProps} />);
      
      // 錄音狀態 - 紅色
      expect(screen.getByText('錄音中')).toHaveStyle({ color: '#ff4d4f' });
      
      // 暫停狀態 - 黃色
      rerender(
        <RecordingIndicator 
          {...defaultProps} 
          status={VoiceRecordStatus.PAUSED}
        />
      );
      expect(screen.getByText('已暫停')).toHaveStyle({ color: '#faad14' });
      
      // 處理狀態 - 藍色
      rerender(
        <RecordingIndicator 
          {...defaultProps} 
          status={VoiceRecordStatus.PROCESSING}
        />
      );
      expect(screen.getByText('處理中')).toHaveStyle({ color: '#1890ff' });
    });

    it('應該在錄音狀態下顯示脈衝動畫', () => {
      render(<RecordingIndicator {...defaultProps} />);
      
      const pulseIndicator = document.querySelector('[style*="animation"]');
      expect(pulseIndicator).toBeInTheDocument();
    });
  });

  describe('自定義樣式', () => {
    it('應該應用自定義 className', () => {
      const { container } = render(
        <RecordingIndicator 
          {...defaultProps} 
          className="custom-recording-indicator"
        />
      );
      
      expect(container.firstChild).toHaveClass('custom-recording-indicator');
    });
  });
});
