/**
 * 媒體輸入組件測試
 */

// import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ImageUploadButton from '../ImageUploadButton';
import VoiceRecordButton from '../VoiceRecordButton';
import { MediaUploadStatus, VoiceRecordStatus } from '@/types/media';

// Mock Ant Design message
vi.mock('antd', async () => {
  const actual = await vi.importActual('antd');
  return {
    ...actual,
    message: {
      error: vi.fn(),
      success: vi.fn(),
      info: vi.fn(),
      warning: vi.fn()
    }
  };
});

// Mock 瀏覽器 API
Object.defineProperty(window, 'MediaRecorder', {
  writable: true,
  value: vi.fn().mockImplementation(() => ({
    start: vi.fn(),
    stop: vi.fn(),
    pause: vi.fn(),
    resume: vi.fn(),
    state: 'inactive',
    ondataavailable: null,
    onstop: null,
    onerror: null
  }))
});

Object.defineProperty(navigator, 'mediaDevices', {
  writable: true,
  value: {
    getUserMedia: vi.fn().mockResolvedValue({
      getTracks: () => [{ stop: vi.fn() }]
    })
  }
});

describe('ImageUploadButton', () => {
  const mockOnFileSelect = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('應該正確渲染圖片上傳按鈕', () => {
    render(
      <ImageUploadButton
        onFileSelect={mockOnFileSelect}
        uploadStatus={MediaUploadStatus.IDLE}
      />
    );

    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });

  it('應該在點擊時觸發文件選擇', () => {
    render(
      <ImageUploadButton
        onFileSelect={mockOnFileSelect}
        uploadStatus={MediaUploadStatus.IDLE}
      />
    );

    const button = screen.getByRole('button');
    fireEvent.click(button);

    // 檢查是否有隱藏的文件輸入
    const fileInput = document.querySelector('input[type="file"]');
    expect(fileInput).toBeInTheDocument();
  });

  it('應該在加載時顯示加載狀態', () => {
    render(
      <ImageUploadButton
        onFileSelect={mockOnFileSelect}
        loading={true}
        uploadStatus={MediaUploadStatus.UPLOADING}
      />
    );

    const button = screen.getByRole('button');
    expect(button).toHaveClass('ant-btn-loading');
  });

  it('應該在禁用時不可點擊', () => {
    render(
      <ImageUploadButton
        onFileSelect={mockOnFileSelect}
        disabled={true}
        uploadStatus={MediaUploadStatus.IDLE}
      />
    );

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });
});

describe('VoiceRecordButton', () => {
  const mockProps = {
    status: VoiceRecordStatus.IDLE,
    duration: 0,
    volume: 0,
    maxDuration: 60,
    onStartRecord: vi.fn(),
    onStopRecord: vi.fn(),
    onPauseRecord: vi.fn(),
    onResumeRecord: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('應該正確渲染語音錄製按鈕', () => {
    render(<VoiceRecordButton {...mockProps} />);

    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });

  it('應該在閒置狀態顯示開始錄音圖標', () => {
    render(<VoiceRecordButton {...mockProps} />);

    // 檢查是否有音頻圖標
    const audioIcon = document.querySelector('.anticon-audio');
    expect(audioIcon).toBeInTheDocument();
  });

  it('應該在錄音狀態顯示停止圖標', () => {
    render(
      <VoiceRecordButton
        {...mockProps}
        status={VoiceRecordStatus.RECORDING}
        duration={5.5}
      />
    );

    // 檢查是否有停止圖標
    const stopIcon = document.querySelector('.anticon-stop');
    expect(stopIcon).toBeInTheDocument();
  });

  it('應該在點擊時觸發開始錄音', () => {
    render(<VoiceRecordButton {...mockProps} />);

    const button = screen.getByRole('button');
    fireEvent.click(button);

    expect(mockProps.onStartRecord).toHaveBeenCalledTimes(1);
  });

  it('應該在錄音時點擊觸發停止錄音', () => {
    render(
      <VoiceRecordButton
        {...mockProps}
        status={VoiceRecordStatus.RECORDING}
      />
    );

    const button = screen.getByRole('button');
    fireEvent.click(button);

    expect(mockProps.onStopRecord).toHaveBeenCalledTimes(1);
  });

  it('應該在禁用時不可點擊', () => {
    render(
      <VoiceRecordButton
        {...mockProps}
        disabled={true}
      />
    );

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });
});

describe('媒體輸入整合測試', () => {
  it('應該支援圖片文件選擇', async () => {
    const mockOnFileSelect = vi.fn();

    render(
      <ImageUploadButton
        onFileSelect={mockOnFileSelect}
        uploadStatus={MediaUploadStatus.IDLE}
      />
    );

    // 模擬文件選擇
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

    Object.defineProperty(fileInput, 'files', {
      value: [file],
      writable: false
    });

    fireEvent.change(fileInput);

    await waitFor(() => {
      expect(mockOnFileSelect).toHaveBeenCalledWith(file);
    });
  });

  it('應該拒絕非圖片文件', async () => {
    const mockOnFileSelect = vi.fn();

    render(
      <ImageUploadButton
        onFileSelect={mockOnFileSelect}
        uploadStatus={MediaUploadStatus.IDLE}
      />
    );

    // 模擬選擇非圖片文件
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });

    Object.defineProperty(fileInput, 'files', {
      value: [file],
      writable: false
    });

    fireEvent.change(fileInput);

    // 應該不會調用 onFileSelect
    expect(mockOnFileSelect).not.toHaveBeenCalled();
  });
});

describe('Data URI 格式測試', () => {
  it('應該生成正確的 Data URI 格式', () => {
    // 測試 createDataURI 函數
    const base64Data = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
    const mimeType = 'image/png';

    // 動態導入工具函數進行測試
    import('@/utils/mediaUtils').then(({ createDataURI }) => {
      const dataURI = createDataURI(base64Data, mimeType);
      expect(dataURI).toBe(`data:${mimeType};base64,${base64Data}`);
      expect(dataURI.startsWith('data:')).toBe(true);
      expect(dataURI.includes(';base64,')).toBe(true);
    });
  });

  it('應該正確提取 Base64 數據', () => {
    const dataURI = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
    const expectedBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

    import('@/utils/mediaUtils').then(({ extractBase64FromDataURI }) => {
      const extractedBase64 = extractBase64FromDataURI(dataURI);
      expect(extractedBase64).toBe(expectedBase64);
    });
  });

  it('應該正確提取 MIME 類型', () => {
    const dataURI = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
    const expectedMimeType = 'image/png';

    import('@/utils/mediaUtils').then(({ extractMimeTypeFromDataURI }) => {
      const extractedMimeType = extractMimeTypeFromDataURI(dataURI);
      expect(extractedMimeType).toBe(expectedMimeType);
    });
  });

  it('應該驗證 Data URI 格式', () => {
    const validDataURI = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
    const invalidDataURI = 'invalid-data-uri';

    import('@/utils/mediaUtils').then(({ isValidDataURI }) => {
      expect(isValidDataURI(validDataURI)).toBe(true);
      expect(isValidDataURI(invalidDataURI)).toBe(false);
    });
  });
});
