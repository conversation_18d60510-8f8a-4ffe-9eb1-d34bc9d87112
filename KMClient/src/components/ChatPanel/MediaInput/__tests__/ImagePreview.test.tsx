/**
 * ImagePreview 組件測試 - 專注於防重複點擊功能
 */

import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ImagePreview from '../ImagePreview';

// Mock formatFileSize 函數
vi.mock('@/utils/mediaUtils', () => ({
  formatFileSize: (size: number) => `${(size / 1024).toFixed(2)} KB`
}));

describe('ImagePreview - 防重複點擊測試', () => {
  const mockFile = new File(['test'], 'test-image.png', { type: 'image/png' });
  const mockPreviewUrl = 'blob:http://localhost:3000/test-image';
  const mockOnSend = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('發送按鈕狀態管理', () => {
    it('應該在非加載狀態下正常響應點擊', () => {
      render(
        <ImagePreview
          file={mockFile}
          previewUrl={mockPreviewUrl}
          onSend={mockOnSend}
          onCancel={mockOnCancel}
          loading={false}
        />
      );

      const sendButton = screen.getByRole('button', { name: /發送圖片/i });
      
      // 按鈕應該是可點擊的
      expect(sendButton).not.toBeDisabled();
      expect(sendButton).toHaveTextContent('發送圖片');
      
      // 點擊按鈕
      fireEvent.click(sendButton);
      
      // 應該調用 onSend
      expect(mockOnSend).toHaveBeenCalledTimes(1);
    });

    it('應該在加載狀態下禁用按鈕並顯示加載文字', () => {
      render(
        <ImagePreview
          file={mockFile}
          previewUrl={mockPreviewUrl}
          onSend={mockOnSend}
          onCancel={mockOnCancel}
          loading={true}
        />
      );

      const sendButton = screen.getByRole('button', { name: /發送中/i });
      
      // 按鈕應該被禁用
      expect(sendButton).toBeDisabled();
      expect(sendButton).toHaveTextContent('發送中...');
      expect(sendButton).toHaveClass('disabled:bg-gray-400');
    });

    it('應該防止在加載狀態下的重複點擊', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      render(
        <ImagePreview
          file={mockFile}
          previewUrl={mockPreviewUrl}
          onSend={mockOnSend}
          onCancel={mockOnCancel}
          loading={true}
        />
      );

      const sendButton = screen.getByRole('button', { name: /發送中/i });
      
      // 嘗試點擊禁用的按鈕（雖然被禁用，但我們測試內部邏輯）
      fireEvent.click(sendButton);
      
      // onSend 不應該被調用
      expect(mockOnSend).not.toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });

    it('應該在取消按鈕上正確處理加載狀態', () => {
      render(
        <ImagePreview
          file={mockFile}
          previewUrl={mockPreviewUrl}
          onSend={mockOnSend}
          onCancel={mockOnCancel}
          loading={true}
        />
      );

      const cancelButton = screen.getByRole('button', { name: /取消/i });
      
      // 取消按鈕在加載時也應該被禁用
      expect(cancelButton).toBeDisabled();
    });
  });

  describe('鍵盤事件處理', () => {
    it('應該在非加載狀態下響應 Enter 鍵', () => {
      render(
        <ImagePreview
          file={mockFile}
          previewUrl={mockPreviewUrl}
          onSend={mockOnSend}
          onCancel={mockOnCancel}
          loading={false}
        />
      );

      // 按下 Enter 鍵
      fireEvent.keyDown(document, { key: 'Enter' });
      
      // 應該調用 onSend
      expect(mockOnSend).toHaveBeenCalledTimes(1);
    });

    it('應該在加載狀態下忽略 Enter 鍵', () => {
      render(
        <ImagePreview
          file={mockFile}
          previewUrl={mockPreviewUrl}
          onSend={mockOnSend}
          onCancel={mockOnCancel}
          loading={true}
        />
      );

      // 按下 Enter 鍵
      fireEvent.keyDown(document, { key: 'Enter' });
      
      // onSend 不應該被調用
      expect(mockOnSend).not.toHaveBeenCalled();
    });

    it('應該始終響應 Escape 鍵', () => {
      render(
        <ImagePreview
          file={mockFile}
          previewUrl={mockPreviewUrl}
          onSend={mockOnSend}
          onCancel={mockOnCancel}
          loading={true}
        />
      );

      // 按下 Escape 鍵
      fireEvent.keyDown(document, { key: 'Escape' });
      
      // 應該調用 onCancel（即使在加載狀態下）
      expect(mockOnCancel).toHaveBeenCalledTimes(1);
    });
  });

  describe('文件信息顯示', () => {
    it('應該正確顯示文件名和大小', () => {
      render(
        <ImagePreview
          file={mockFile}
          previewUrl={mockPreviewUrl}
          onSend={mockOnSend}
          onCancel={mockOnCancel}
          loading={false}
        />
      );

      // 檢查文件名
      expect(screen.getByText('test-image.png')).toBeInTheDocument();
      
      // 檢查文件大小（mock 文件大小為 4 bytes）
      expect(screen.getByText('0.00 KB')).toBeInTheDocument();
      
      // 檢查文件類型
      expect(screen.getByText('PNG')).toBeInTheDocument();
    });

    it('應該顯示正確的說明文字', () => {
      render(
        <ImagePreview
          file={mockFile}
          previewUrl={mockPreviewUrl}
          onSend={mockOnSend}
          onCancel={mockOnCancel}
          loading={false}
        />
      );

      expect(screen.getByText(/選擇的圖片將發送給 AI 助手進行分析/)).toBeInTheDocument();
      expect(screen.getByText(/按 Enter 發送，按 Esc 取消/)).toBeInTheDocument();
    });
  });

  describe('背景點擊關閉', () => {
    it('應該在點擊背景時關閉預覽', () => {
      render(
        <ImagePreview
          file={mockFile}
          previewUrl={mockPreviewUrl}
          onSend={mockOnSend}
          onCancel={mockOnCancel}
          loading={false}
        />
      );

      const overlay = screen.getByRole('dialog');
      
      // 點擊背景（overlay）
      fireEvent.click(overlay);
      
      // 應該調用 onCancel
      expect(mockOnCancel).toHaveBeenCalledTimes(1);
    });

    it('應該在點擊內容區域時不關閉預覽', () => {
      render(
        <ImagePreview
          file={mockFile}
          previewUrl={mockPreviewUrl}
          onSend={mockOnSend}
          onCancel={mockOnCancel}
          loading={false}
        />
      );

      const card = screen.getByText('test-image.png').closest('.ant-card');
      
      // 點擊卡片內容
      if (card) {
        fireEvent.click(card);
      }
      
      // onCancel 不應該被調用
      expect(mockOnCancel).not.toHaveBeenCalled();
    });
  });

  describe('無障礙功能', () => {
    it('應該具有正確的 ARIA 屬性', () => {
      render(
        <ImagePreview
          file={mockFile}
          previewUrl={mockPreviewUrl}
          onSend={mockOnSend}
          onCancel={mockOnCancel}
          loading={false}
        />
      );

      const dialog = screen.getByRole('dialog');
      
      expect(dialog).toHaveAttribute('aria-modal', 'true');
      expect(dialog).toHaveAttribute('aria-labelledby', 'image-preview-title');
      expect(dialog).toHaveAttribute('aria-describedby', 'image-preview-description');
    });
  });
});
