/**
 * VoicePreview 集成測試 - 測試語音發送後界面關閉功能
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import VoicePreview from '../VoicePreview';

// Mock MediaService
vi.mock('@/services/MediaService', () => ({
  processRecordedAudio: vi.fn().mockResolvedValue({
    success: true,
    data: 'mock-audio-data'
  })
}));

// Mock formatDuration 和 formatFileSize
vi.mock('@/utils/mediaUtils', () => ({
  formatDuration: (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  },
  formatFileSize: (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  }
}));

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mock-audio-url');
global.URL.revokeObjectURL = vi.fn();

// Mock HTMLAudioElement
class MockAudioElement {
  src = '';
  currentTime = 0;
  duration = 30;
  paused = true;
  
  addEventListener = vi.fn();
  removeEventListener = vi.fn();
  play = vi.fn().mockResolvedValue(undefined);
  pause = vi.fn();
  load = vi.fn();
}

global.HTMLAudioElement = MockAudioElement as any;

describe('VoicePreview Integration Tests', () => {
  const mockAudioBlob = new Blob(['mock audio data'], { type: 'audio/wav' });
  const mockOnSend = vi.fn();
  const mockOnCancel = vi.fn();

  const defaultProps = {
    audioBlob: mockAudioBlob,
    audioUrl: 'mock-audio-url',
    duration: 30,
    onSend: mockOnSend,
    onCancel: mockOnCancel,
    loading: false
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('語音發送功能', () => {
    it('應該正確處理語音發送', async () => {
      render(<VoicePreview {...defaultProps} />);
      
      const sendButton = screen.getByText('發送');
      expect(sendButton).toBeInTheDocument();
      expect(sendButton).not.toBeDisabled();
      
      fireEvent.click(sendButton);
      
      await waitFor(() => {
        expect(mockOnSend).toHaveBeenCalledTimes(1);
      });
    });

    it('應該在發送過程中禁用按鈕', () => {
      render(<VoicePreview {...defaultProps} loading={true} />);
      
      const sendButton = screen.getByText('發送中...');
      expect(sendButton).toBeDisabled();
    });

    it('應該防止重複點擊發送按鈕', async () => {
      render(<VoicePreview {...defaultProps} />);
      
      const sendButton = screen.getByText('發送');
      
      // 快速點擊多次
      fireEvent.click(sendButton);
      fireEvent.click(sendButton);
      fireEvent.click(sendButton);
      
      // 應該只調用一次
      await waitFor(() => {
        expect(mockOnSend).toHaveBeenCalledTimes(1);
      });
    });

    it('應該正確處理取消操作', () => {
      render(<VoicePreview {...defaultProps} />);
      
      const cancelButton = screen.getByText('取消');
      fireEvent.click(cancelButton);
      
      expect(mockOnCancel).toHaveBeenCalledTimes(1);
    });
  });

  describe('界面顯示', () => {
    it('應該顯示語音信息', () => {
      render(<VoicePreview {...defaultProps} />);
      
      expect(screen.getByText('語音消息')).toBeInTheDocument();
      expect(screen.getByText('0:30 • 15 B')).toBeInTheDocument();
    });

    it('應該顯示播放控制按鈕', () => {
      render(<VoicePreview {...defaultProps} />);
      
      expect(screen.getByRole('button', { name: /play/ })).toBeInTheDocument();
    });

    it('應該顯示發送和取消按鈕', () => {
      render(<VoicePreview {...defaultProps} />);
      
      expect(screen.getByText('發送')).toBeInTheDocument();
      expect(screen.getByText('取消')).toBeInTheDocument();
    });

    it('應該在加載狀態下顯示正確的文字', () => {
      render(<VoicePreview {...defaultProps} loading={true} />);
      
      expect(screen.getByText('發送中...')).toBeInTheDocument();
    });
  });

  describe('音頻播放功能', () => {
    it('應該能夠播放音頻', async () => {
      render(<VoicePreview {...defaultProps} />);
      
      const playButton = screen.getByRole('button', { name: /play/ });
      fireEvent.click(playButton);
      
      // 由於 Mock 的限制，這裡主要測試按鈕點擊不會報錯
      expect(playButton).toBeInTheDocument();
    });

    it('應該顯示音頻波形', () => {
      render(<VoicePreview {...defaultProps} />);
      
      // 檢查是否有 canvas 元素（AudioWaveform 組件）
      const canvas = document.querySelector('canvas');
      expect(canvas).toBeInTheDocument();
    });
  });

  describe('響應式設計', () => {
    it('應該有正確的最大寬度', () => {
      const { container } = render(<VoicePreview {...defaultProps} />);
      
      const card = container.querySelector('.voice-preview-card');
      expect(card).toHaveStyle({ maxWidth: '400px' });
    });

    it('應該有正確的寬度設置', () => {
      const { container } = render(<VoicePreview {...defaultProps} />);
      
      const card = container.querySelector('.voice-preview-card');
      expect(card).toHaveStyle({ width: '100%' });
    });
  });

  describe('鍵盤操作', () => {
    it('應該支援 Enter 鍵發送', () => {
      render(<VoicePreview {...defaultProps} />);
      
      fireEvent.keyDown(document, { key: 'Enter', code: 'Enter' });
      
      expect(mockOnSend).toHaveBeenCalledTimes(1);
    });

    it('應該支援 Escape 鍵取消', () => {
      render(<VoicePreview {...defaultProps} />);
      
      fireEvent.keyDown(document, { key: 'Escape', code: 'Escape' });
      
      expect(mockOnCancel).toHaveBeenCalledTimes(1);
    });

    it('應該在加載狀態下忽略 Enter 鍵', () => {
      render(<VoicePreview {...defaultProps} loading={true} />);
      
      fireEvent.keyDown(document, { key: 'Enter', code: 'Enter' });
      
      expect(mockOnSend).not.toHaveBeenCalled();
    });
  });

  describe('錯誤處理', () => {
    it('應該處理無效的音頻 Blob', () => {
      const invalidProps = {
        ...defaultProps,
        audioBlob: new Blob([], { type: 'text/plain' })
      };
      
      expect(() => {
        render(<VoicePreview {...invalidProps} />);
      }).not.toThrow();
    });

    it('應該處理無效的音頻 URL', () => {
      const invalidProps = {
        ...defaultProps,
        audioUrl: ''
      };
      
      expect(() => {
        render(<VoicePreview {...invalidProps} />);
      }).not.toThrow();
    });
  });
});
