/**
 * 語音錄製按鈕組件
 */

import React from 'react';
import { <PERSON><PERSON>, Tooltip, Progress } from 'antd';
import {
  AudioOutlined,
  LoadingOutlined,
  PlayCircleOutlined,
  StopOutlined
} from '@ant-design/icons';
import { VoiceRecordStatus } from '@/types/media';
import { formatDuration } from '@/utils/mediaUtils';

interface VoiceRecordButtonProps {
  status: VoiceRecordStatus;
  duration: number;
  volume: number;
  maxDuration: number;
  onStartRecord: () => void;
  onStopRecord: () => void;
  onPauseRecord: () => void;
  onResumeRecord: () => void;
  disabled?: boolean;
  className?: string;
}

const VoiceRecordButton: React.FC<VoiceRecordButtonProps> = ({
  status,
  duration,
  volume,
  maxDuration,
  onStartRecord,
  onStopRecord,
  onPauseRecord: _onPauseRecord,
  onResumeRecord,
  disabled = false,
  className = ''
}) => {

  /**
   * 獲取按鈕配置
   */
  const getButtonConfig = () => {
    switch (status) {
      case VoiceRecordStatus.IDLE:
        return {
          icon: <AudioOutlined />,
          tooltip: '開始錄音',
          type: 'default' as const,
          onClick: onStartRecord,
          danger: false
        };
        
      case VoiceRecordStatus.RECORDING:
        return {
          icon: <StopOutlined />,
          tooltip: `錄音中 ${formatDuration(duration)}`,
          type: 'primary' as const,
          onClick: onStopRecord,
          danger: true
        };
        
      case VoiceRecordStatus.PAUSED:
        return {
          icon: <PlayCircleOutlined />,
          tooltip: `已暫停 ${formatDuration(duration)}`,
          type: 'default' as const,
          onClick: onResumeRecord,
          danger: false
        };
        
      case VoiceRecordStatus.STOPPED:
        return {
          icon: <AudioOutlined />,
          tooltip: '錄音完成',
          type: 'primary' as const,
          onClick: onStartRecord,
          danger: false
        };
        
      case VoiceRecordStatus.PROCESSING:
        return {
          icon: <LoadingOutlined spin />,
          tooltip: '處理中...',
          type: 'default' as const,
          onClick: () => {},
          danger: false
        };
        
      default:
        return {
          icon: <AudioOutlined />,
          tooltip: '開始錄音',
          type: 'default' as const,
          onClick: onStartRecord,
          danger: false
        };
    }
  };

  const buttonConfig = getButtonConfig();
  const isRecording = status === VoiceRecordStatus.RECORDING;
  const isProcessing = status === VoiceRecordStatus.PROCESSING;
  const progressPercent = (duration / maxDuration) * 100;

  return (
    <div className={`voice-record-button-container ${className}`}>
      <Tooltip title={buttonConfig.tooltip} placement="top">
        <div className="relative">
          <Button
            type={buttonConfig.type}
            icon={buttonConfig.icon}
            onClick={buttonConfig.onClick}
            disabled={disabled || isProcessing}
            danger={buttonConfig.danger}
            className="voice-record-button"
            style={{
              borderRadius: '6px',
              height: '36px',
              width: '36px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
              overflow: 'hidden'
            }}
          />
          
          {/* 錄音進度環 */}
          {isRecording && (
            <div 
              className="absolute inset-0 pointer-events-none"
              style={{
                borderRadius: '6px'
              }}
            >
              <Progress
                type="circle"
                percent={progressPercent}
                size={36}
                strokeWidth={3}
                strokeColor={{
                  '0%': '#ff4d4f',
                  '50%': '#faad14',
                  '100%': '#52c41a'
                }}
                trailColor="transparent"
                showInfo={false}
                className="absolute inset-0"
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '36px',
                  height: '36px'
                }}
              />
            </div>
          )}
          
          {/* 音量指示器 */}
          {isRecording && (
            <div 
              className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-green-400 to-red-500 opacity-70"
              style={{
                width: `${volume}%`,
                borderRadius: '0 0 6px 6px',
                transition: 'width 0.1s ease-out'
              }}
            />
          )}
        </div>
      </Tooltip>
      
      {/* 錄音時長顯示 */}
      {(isRecording || status === VoiceRecordStatus.PAUSED) && (
        <div 
          className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded"
          style={{
            fontSize: '10px',
            whiteSpace: 'nowrap'
          }}
        >
          {formatDuration(duration)} / {formatDuration(maxDuration)}
        </div>
      )}
    </div>
  );
};

export default VoiceRecordButton;
