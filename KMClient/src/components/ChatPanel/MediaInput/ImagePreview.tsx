/**
 * 圖片預覽組件
 */

import React, { useEffect } from 'react';
import { Card, Button, Space, Typography, Image } from 'antd';
import { SendOutlined, CloseOutlined, EyeOutlined } from '@ant-design/icons';
import { formatFileSize } from '@/utils/mediaUtils';

const { Text } = Typography;

interface ImagePreviewProps {
  file: File;
  previewUrl: string;
  onSend: () => void;
  onCancel: () => void;
  loading?: boolean;
  className?: string;
}

const ImagePreview: React.FC<ImagePreviewProps> = ({
  file,
  previewUrl,
  onSend,
  onCancel,
  loading = false,
  className = ''
}) => {

  // 防重複點擊處理
  const handleSend = () => {
    if (loading) {
      console.log('⚠️ 正在發送中，忽略重複點擊');
      return;
    }
    onSend();
  };

  // 鍵盤事件處理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onCancel();
      } else if (event.key === 'Enter' && !loading) {
        handleSend();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onCancel, loading]);

  // 點擊背景關閉
  const handleOverlayClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget) {
      onCancel();
    }
  };

  return (
    <div
      className="image-preview-overlay"
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="image-preview-title"
      aria-describedby="image-preview-description"
    >
      <div className="image-preview-modal">
        <Card
          className={`image-preview-card ${className}`}
          style={{
            width: '100%',
            maxWidth: '480px',
            margin: '0 auto',
            borderRadius: '12px',
            overflow: 'hidden',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)'
          }}
          bodyStyle={{ padding: '16px' }}
          cover={
            <div
              className="image-preview-cover"
              style={{
                minHeight: '240px',
                maxHeight: '360px',
                overflow: 'hidden',
                backgroundColor: '#f8f9fa',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative'
              }}
            >
              <Image
                src={previewUrl}
                alt="預覽圖片"
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: 'contain',
                  borderRadius: '8px 8px 0 0'
                }}
                preview={{
                  mask: (
                    <div className="flex items-center justify-center">
                      <EyeOutlined className="text-white text-xl" />
                      <span className="ml-2 text-white text-sm">點擊放大</span>
                    </div>
                  )
                }}
              />
              {/* 圖片加載指示器 */}
              <div className="image-preview-badge">
                <span className="text-xs text-white bg-black bg-opacity-60 px-2 py-1 rounded">
                  圖片預覽
                </span>
              </div>
            </div>
          }
        >
        {/* 文件信息 */}
        <div className="image-preview-info mb-4">
          <div className="flex items-center justify-between mb-2">
            <Text
              strong
              className="text-base text-gray-800"
              id="image-preview-title"
            >
              {file.name}
            </Text>
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <span>{formatFileSize(file.size)}</span>
              <span>•</span>
              <span>{file.type.split('/')[1]?.toUpperCase()}</span>
            </div>
          </div>
          <Text
            type="secondary"
            className="text-sm"
            id="image-preview-description"
          >
            選擇的圖片將發送給 AI 助手進行分析。按 Enter 發送，按 Esc 取消。
          </Text>
        </div>

        {/* 操作按鈕 */}
        <div className="image-preview-actions">
          <Space size="middle" className="w-full justify-center">
            <Button
              icon={<CloseOutlined />}
              onClick={onCancel}
              disabled={loading}
              size="middle"
              className="min-w-[100px]"
            >
              取消
            </Button>
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleSend}
              loading={loading}
              disabled={loading}
              size="middle"
              className="min-w-[120px] bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400"
            >
              {loading ? '發送中...' : '發送圖片'}
            </Button>
          </Space>
        </div>
        </Card>
      </div>
    </div>
  );
};

export default ImagePreview;
