/**
 * 語音播放組件
 * 專門用於播放已發送的語音消息
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Button, Slider, Typography, message } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  SoundOutlined,
  LoadingOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import {
  VoicePlayState,
  VoicePlayStatus,
  VoicePlayerProps
} from '@/types/media';
import {
  formatDuration,
  formatFileSize,
  dataURIToBlobURL,
  revokeBlobURL
} from '@/utils/mediaUtils';

const { Text } = Typography;

const VoicePlayer: React.FC<VoicePlayerProps> = ({
  audioData,
  fileName,
  fileSize,
  className = '',
  compact = false,
  showWaveform = false,
  showDownload = true,
  onPlay,
  onPause,
  onEnded,
  onError
}) => {
  // 播放狀態
  const [playState, setPlayState] = useState<VoicePlayState>({
    status: VoicePlayStatus.IDLE,
    currentTime: 0,
    duration: 0,
    volume: 1.0,
    muted: false
  });

  // 引用
  const audioRef = useRef<HTMLAudioElement>(null);
  const blobURLRef = useRef<string | null>(null);

  /**
   * 初始化音頻元素
   */
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio || !audioData) return;

    console.log('🎵 VoicePlayer 初始化音頻:', {
      audioData: audioData.substring(0, 50) + '...',
      fileName,
      fileSize
    });

    // 轉換 Data URI 為 Blob URL
    const blobURL = dataURIToBlobURL(audioData);
    blobURLRef.current = blobURL;

    // 設置音頻源
    audio.src = blobURL;
    audio.volume = playState.volume;
    audio.muted = playState.muted;

    // 事件處理器
    const handleLoadedMetadata = () => {
      console.log('📊 VoicePlayer 音頻元數據加載完成:', {
        duration: audio.duration,
        readyState: audio.readyState
      });
      
      setPlayState(prev => ({
        ...prev,
        duration: audio.duration || 0,
        status: VoicePlayStatus.IDLE
      }));
    };

    const handleTimeUpdate = () => {
      setPlayState(prev => ({
        ...prev,
        currentTime: audio.currentTime
      }));
    };

    const handleEnded = () => {
      console.log('🏁 VoicePlayer 播放結束');
      setPlayState(prev => ({
        ...prev,
        status: VoicePlayStatus.IDLE,
        currentTime: 0
      }));
      onEnded?.();
    };

    const handleError = (e: Event) => {
      const target = e.target as HTMLAudioElement;
      const errorMsg = `音頻播放錯誤: ${target.error?.message || '未知錯誤'}`;
      
      console.error('❌ VoicePlayer 播放錯誤:', {
        error: target.error,
        networkState: target.networkState,
        readyState: target.readyState,
        src: target.src
      });

      setPlayState(prev => ({
        ...prev,
        status: VoicePlayStatus.ERROR,
        error: errorMsg
      }));

      message.error(errorMsg);
      onError?.(errorMsg);
    };

    const handleCanPlay = () => {
      console.log('✅ VoicePlayer 音頻可以播放');
      setPlayState(prev => ({
        ...prev,
        status: prev.status === VoicePlayStatus.LOADING ? VoicePlayStatus.IDLE : prev.status
      }));
    };

    const handleLoadStart = () => {
      console.log('🔄 VoicePlayer 音頻開始加載');
      setPlayState(prev => ({
        ...prev,
        status: VoicePlayStatus.LOADING
      }));
    };

    // 添加事件監聽器
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('loadstart', handleLoadStart);

    // 清理函數
    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('loadstart', handleLoadStart);

      // 清理 Blob URL
      if (blobURLRef.current) {
        revokeBlobURL(blobURLRef.current);
        blobURLRef.current = null;
      }
    };
  }, [audioData, fileName, fileSize, playState.volume, playState.muted, onEnded, onError]);

  /**
   * 播放/暫停切換
   */
  const togglePlayPause = useCallback(async () => {
    const audio = audioRef.current;
    if (!audio) {
      console.error('❌ VoicePlayer 音頻元素不存在');
      return;
    }

    try {
      if (playState.status === VoicePlayStatus.PLAYING) {
        // 暫停播放
        audio.pause();
        setPlayState(prev => ({ ...prev, status: VoicePlayStatus.PAUSED }));
        console.log('⏸️ VoicePlayer 音頻已暫停');
        onPause?.();
      } else {
        // 開始播放
        console.log('▶️ VoicePlayer 開始播放音頻...');
        
        // 確保音頻已準備好
        if (audio.readyState < 2) {
          setPlayState(prev => ({ ...prev, status: VoicePlayStatus.LOADING }));
          
          // 等待音頻準備就緒
          await new Promise<void>((resolve, reject) => {
            const timeout = setTimeout(() => {
              reject(new Error('音頻加載超時'));
            }, 10000);

            const onCanPlay = () => {
              clearTimeout(timeout);
              audio.removeEventListener('canplay', onCanPlay);
              audio.removeEventListener('error', onErrorLoad);
              resolve();
            };

            const onErrorLoad = (e: Event) => {
              clearTimeout(timeout);
              audio.removeEventListener('canplay', onCanPlay);
              audio.removeEventListener('error', onErrorLoad);
              reject(e);
            };

            audio.addEventListener('canplay', onCanPlay);
            audio.addEventListener('error', onErrorLoad);
          });
        }

        // 開始播放
        await audio.play();
        setPlayState(prev => ({ ...prev, status: VoicePlayStatus.PLAYING }));
        console.log('✅ VoicePlayer 音頻播放成功');
        onPlay?.();
      }
    } catch (error: any) {
      console.error('❌ VoicePlayer 播放失敗:', error);
      
      const errorMsg = `播放失敗: ${error.message || '未知錯誤'}`;
      setPlayState(prev => ({
        ...prev,
        status: VoicePlayStatus.ERROR,
        error: errorMsg
      }));

      message.error(errorMsg);
      onError?.(errorMsg);
    }
  }, [playState.status, onPlay, onPause, onError]);

  /**
   * 拖動進度條
   */
  const handleSeek = useCallback((value: number) => {
    const audio = audioRef.current;
    if (!audio || playState.duration === 0) return;

    const newTime = (value / 100) * playState.duration;
    audio.currentTime = newTime;
    setPlayState(prev => ({ ...prev, currentTime: newTime }));
  }, [playState.duration]);

  /**
   * 設置音量
   */
  const handleVolumeChange = useCallback((value: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    const volume = value / 100;
    audio.volume = volume;
    setPlayState(prev => ({ ...prev, volume }));
    console.log('🔊 VoicePlayer 音量設置為:', volume);
  }, []);

  /**
   * 下載音頻文件
   */
  const handleDownload = useCallback(() => {
    try {
      const link = document.createElement('a');
      link.href = audioData;
      link.download = fileName || `voice-${Date.now()}.wav`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      console.log('📥 VoicePlayer 下載音頻文件:', fileName);
    } catch (error) {
      console.error('❌ VoicePlayer 下載失敗:', error);
      message.error('下載失敗');
    }
  }, [audioData, fileName]);

  /**
   * 獲取播放進度百分比
   */
  const getProgressPercent = useCallback(() => {
    if (playState.duration === 0) return 0;
    return (playState.currentTime / playState.duration) * 100;
  }, [playState.currentTime, playState.duration]);

  /**
   * 獲取播放按鈕圖標
   */
  const getPlayIcon = () => {
    switch (playState.status) {
      case VoicePlayStatus.LOADING:
        return <LoadingOutlined spin />;
      case VoicePlayStatus.PLAYING:
        return <PauseCircleOutlined />;
      default:
        return <PlayCircleOutlined />;
    }
  };

  return (
    <div className={`voice-player ${className} ${compact ? 'compact' : ''}`}>
      {/* 隱藏的音頻元素 */}
      <audio
        ref={audioRef}
        preload="metadata"
        playsInline
        crossOrigin="anonymous"
      />

      <div className="flex items-center space-x-3">
        {/* 播放按鈕 */}
        <Button
          type="text"
          icon={getPlayIcon()}
          onClick={togglePlayPause}
          disabled={playState.status === VoicePlayStatus.LOADING || playState.status === VoicePlayStatus.ERROR}
          size={compact ? 'small' : 'middle'}
          className="flex items-center justify-center text-blue-500 hover:text-blue-600"
          style={{
            fontSize: compact ? '16px' : '20px',
            padding: 0,
            height: 'auto',
            width: 'auto'
          }}
        />

        {/* 播放信息和進度 */}
        <div className="flex-1 min-w-0">
          {!compact && (
            <div className="flex items-center justify-between mb-1">
              <Text strong className="text-sm truncate">
                {fileName || '語音消息'}
              </Text>
              {fileSize && (
                <Text type="secondary" className="text-xs ml-2">
                  {formatFileSize(fileSize)}
                </Text>
              )}
            </div>
          )}
          
          <div className="flex items-center space-x-2">
            <Text type="secondary" className="text-xs whitespace-nowrap">
              {formatDuration(playState.currentTime)}
            </Text>
            
            <Slider
              value={getProgressPercent()}
              onChange={handleSeek}
              tooltip={{ formatter: null }}
              trackStyle={{ backgroundColor: '#1890ff' }}
              handleStyle={{ borderColor: '#1890ff' }}
              className="flex-1 mb-0"
              disabled={playState.status === VoicePlayStatus.LOADING || playState.status === VoicePlayStatus.ERROR}
            />
            
            <Text type="secondary" className="text-xs whitespace-nowrap">
              {formatDuration(playState.duration)}
            </Text>
          </div>

          {playState.error && (
            <Text type="danger" className="text-xs">
              {playState.error}
            </Text>
          )}
        </div>

        {/* 下載按鈕 */}
        {showDownload && (
          <Button
            type="text"
            icon={<DownloadOutlined />}
            onClick={handleDownload}
            size="small"
            className="text-gray-500 hover:text-gray-700"
          />
        )}
      </div>
    </div>
  );
};

export default VoicePlayer;
