/**
 * 聊天面板組件
 */

import React, { useState, useRef, useEffect } from 'react';
import { Layout, Button, Typography, Space, Avatar, message, Tag } from 'antd';
import {
  PlusOutlined,
  UserOutlined,
  PaperClipOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import {
  MdDelete,
  MdSend,
  MdSmartToy
} from 'react-icons/md';
// UploadProps 已移至 AttachmentPanel 組件中使用
import { useAppStore } from '@/hooks/useAppStore';
import { useChatService } from '@/services/chatService';
import { MessageParser, hasAttachmentReference } from '@/utils/messageParser';
import { ChatMessage } from '@/types/chat';
import { MediaType, Base64MediaData } from '@/types/media';
import MessageRenderer from './MessageRenderer';
import MediaMessageRenderer from './MediaMessageRenderer';
import ResponseTimer from './ResponseTimer';
// 媒體輸入組件
import ImageUploadButton from './MediaInput/ImageUploadButton';
import VoiceRecordButton from './MediaInput/VoiceRecordButton';
import ImagePreview from './MediaInput/ImagePreview';
import VoicePreview from './MediaInput/VoicePreview';
import RecordingIndicator from './MediaInput/RecordingIndicator';
// 媒體輸入 Hooks
import { useMediaInput } from '@/hooks/useMediaInput';
import { useVoiceRecorder } from '@/hooks/useVoiceRecorder';
// 媒體輸入樣式
import '@/styles/media-input.css';
import { SpotlightCard, AnimatedText } from '@/components/ReactBits';
import {
  ListAnimation,
  AnimatedButton,
  AnimatedTextArea
} from '@/components/FramerMotion';
import { useResponseTimer } from '@/hooks/useResponseTimer';
import { useConfig } from '@/hooks/useConfig';

const { Header, Content } = Layout;
const { Title, Text } = Typography;

const ChatPanel: React.FC = () => {
  const { config } = useConfig();
  const appName = config?.app?.name || config?.app?.description || 'KM Client';

  const {
    currentSession,
    userInfo,
    attachments,
    chatLoading,
    addMessageToCurrentSession,
    createNewSession,
    setChatLoading,
  } = useAppStore();

  const chatService = useChatService();
  const [inputValue, setInputValue] = useState('');
  const [uploadingFile, setUploadingFile] = useState<File | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 媒體輸入狀態（暫時保留，未來可能用於狀態追蹤）
  const [, setMediaData] = useState<{
    data: Base64MediaData | null;
    type: MediaType | null;
  }>({
    data: null,
    type: null
  });

  // 媒體輸入 Hook
  const mediaInput = useMediaInput({
    onSuccess: (data: Base64MediaData, type: MediaType, fileName?: string) => {
      console.log('📤 媒體處理成功:', { type, size: data.size, fileName });
      setMediaData({ data, type });
      // 自動發送媒體消息
      handleSendMediaMessage(data, type, fileName);
    },
    onError: (error) => {
      console.error('❌ 媒體處理失敗:', error);
      message.error(error.message);
    }
  });

  // 語音錄製 Hook
  const voiceRecorder = useVoiceRecorder({
    onRecordComplete: (_audioBlob: Blob) => {
      console.log('🎤 語音錄製完成');
      // 語音錄製完成後自動顯示預覽界面
      // 不需要額外設置，VoicePreview 會根據 voiceRecorder.recordState 自動顯示
    },
    onError: (error) => {
      console.error('❌ 語音錄製失敗:', error);
      message.error(error.message);
    }
  });

  // 計時器Hook
  const {
    elapsedTime,
    isRunning: timerIsRunning,
    currentMessageId,
    startWaitingTimer,
    stopTimer,
    resetTimer,
  } = useResponseTimer();

  // 自動滾動到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [currentSession?.messages]);

  // 檢查是否有用戶信息
  const hasUserInfo = userInfo.tenant_id && userInfo.tenant_id.trim() !== '';

  // 創建新對話
  const handleNewChat = () => {
    if (!hasUserInfo) {
      message.warning('請先設置正確的 URL 參數');
      return;
    }
    createNewSession();
  };

  // 清空當前對話
  const handleClearChat = () => {
    if (currentSession && currentSession.messages.length > 0) {
      handleNewChat();
    }
  };



  // 發送媒體消息
  const handleSendMediaMessage = async (mediaData: Base64MediaData, mediaType: MediaType, fileName?: string) => {
    if (!currentSession || !hasUserInfo) {
      if (!hasUserInfo) {
        message.warning('請先設置正確的 URL 參數');
      }
      return;
    }

    // 用戶發送媒體消息時開始計時
    startWaitingTimer();

    // 創建用戶消息，包含媒體數據
    const userMessage: ChatMessage = {
      id: chatService.generateMessageId(),
      type: 'user',
      content: mediaType === 'image' ? '[圖片]' : '[語音]',
      timestamp: new Date().toISOString(),
      mediaData: {
        type: mediaType,
        data: mediaData.data,
        fileName: fileName,
        fileSize: mediaData.size,
        mimeType: mediaData.mimeType,
        thumbnailUrl: mediaType === 'image' ? mediaData.data : undefined // 圖片使用原始數據作為縮略圖
      }
    };

    // 添加用戶消息
    addMessageToCurrentSession(userMessage);
    setChatLoading(true);

    try {
      // 構建媒體聊天請求
      const messageType = mediaType === 'image' ? 'b64_image' : 'b64_voice';
      const request = chatService.buildMediaChatRequest(
        mediaData.data,
        messageType,
        userInfo,
        currentSession.id
      );

      console.log('📤 發送媒體消息:', {
        messageType,
        dataURILength: mediaData.data.length,
        mimeType: mediaData.mimeType,
        isDataURI: mediaData.data.startsWith('data:')
      });

      // 調用 AI API
      const response = await chatService.omnichannelChat(request);

      console.log('📥 AI API 回應:', response);

      // 處理響應 - 使用與普通消息相同的邏輯
      if (response.code === 0 && response.answer) {
        // 處理 answer 字段 - 可能是字符串、數組或對象
        let answerString: string;
        let parsedContent: any;

        if (typeof response.answer === 'string') {
          // 如果是字符串，直接使用
          answerString = response.answer;
          parsedContent = MessageParser.parseMessage(answerString);
        } else if (Array.isArray(response.answer)) {
          // 如果是數組，直接解析為消息內容
          parsedContent = MessageParser.parseMessageArray(response.answer);
          answerString = JSON.stringify(response.answer);
        } else {
          // 如果是對象，轉換為字符串並解析
          answerString = JSON.stringify(response.answer);
          parsedContent = MessageParser.parseMessage(answerString);
        }

        const assistantMessage: ChatMessage = {
          id: chatService.generateMessageId(),
          type: 'assistant',
          content: answerString,
          parsedContent, // 添加解析後的內容
          timestamp: new Date().toISOString(),
        };

        addMessageToCurrentSession(assistantMessage);
        console.log('✅ 媒體消息發送成功，解析內容:', parsedContent);

        // 如果回復中引用了附件，顯示提示
        if (hasAttachmentReference(response.answer)) {
          message.success('AI 已分析您的圖片內容');
        }
      } else {
        throw new Error(response.message || 'AI 回復失敗');
      }

      // 停止計時器
      stopTimer(userMessage.id);

    } catch (error) {
      console.error('❌ 媒體消息發送失敗:', error);

      const errorMessage: ChatMessage = {
        id: chatService.generateMessageId(),
        type: 'assistant',
        content: '抱歉，處理您的媒體消息時發生錯誤，請稍後再試。',
        timestamp: new Date().toISOString(),
      };

      addMessageToCurrentSession(errorMessage);
      message.error('媒體消息發送失敗');

      // 停止計時器
      stopTimer(userMessage.id);
    } finally {
      setChatLoading(false);
      // 清理媒體數據
      setMediaData({ data: null, type: null });
      mediaInput.resetState();
    }
  };

  // 發送文字消息
  const handleSendMessage = async (messageText?: string) => {
    // 用戶發送消息時開始計時
    startWaitingTimer();

    // 使用傳入的文本或輸入框的值，確保是字符串類型
    let textToSend: string;
    if (messageText !== undefined) {
      // 如果傳入了 messageText，確保它是字符串
      textToSend = typeof messageText === 'string' ? messageText : String(messageText);
    } else {
      // 否則使用輸入框的值
      textToSend = inputValue || '';
    }

    if (!textToSend.trim() || !currentSession || !hasUserInfo) {
      if (!hasUserInfo) {
        message.warning('請先設置正確的 URL 參數');
      }
      // 如果沒有發送消息，停止計時器
      resetTimer();
      return;
    }

    const question = textToSend.trim();
    const userMessage: ChatMessage = {
      id: chatService.generateMessageId(),
      type: 'user',
      content: question,
      timestamp: new Date().toISOString(),
    };

    // 添加用戶消息
    addMessageToCurrentSession(userMessage);
    setInputValue('');
    setChatLoading(true);

    try {
      // 構建請求
      let request;
      if (uploadingFile) {
        // 附件聊天
        request = chatService.buildOmnichannelAttachmentRequest(
          question,
          uploadingFile,
          userInfo,
          currentSession.id
        );
      } else {
        // 普通聊天，檢查是否有附件作為知識背景
        const hasAttachments = attachments.length > 0;
        request = chatService.buildOmnichannelRequest(
          question,
          userInfo,
          currentSession.id,
          hasAttachments
        );
      }

      // 調用 AI API
      const response = await chatService.omnichannelChat(request);

      if (response.code === 0 && response.answer) {
        // 處理 answer 字段 - 可能是字符串、數組或對象
        let answerString: string;
        let parsedContent: any;

        if (typeof response.answer === 'string') {
          // 如果是字符串，直接使用
          answerString = response.answer;
          parsedContent = MessageParser.parseMessage(answerString);
        } else if (Array.isArray(response.answer)) {
          // 如果是數組，直接解析為消息內容
          parsedContent = MessageParser.parseMessageArray(response.answer);
          answerString = JSON.stringify(response.answer);
        } else {
          // 如果是對象，轉換為字符串
          answerString = JSON.stringify(response.answer);
          parsedContent = MessageParser.parseMessage(answerString);
        }

        const aiMessage: ChatMessage = {
          id: chatService.generateMessageId(),
          type: 'assistant',
          content: answerString,
          parsedContent,
          timestamp: new Date().toISOString(),
        };

        // AI回復收到時停止計時器
        stopTimer(aiMessage.id);

        addMessageToCurrentSession(aiMessage);

        // 如果回復中引用了附件，顯示提示
        if (hasAttachmentReference(response.answer)) {
          message.success('AI 已基於您的附件內容回答');
        }
      } else {
        throw new Error(response.message || 'AI 回復失敗');
      }
    } catch (error) {
      console.error('發送消息失敗:', error);

      const errorMessage: ChatMessage = {
        id: chatService.generateMessageId(),
        type: 'assistant',
        content: `抱歉，發生了錯誤：${error instanceof Error ? error.message : '未知錯誤'}`,
        timestamp: new Date().toISOString(),
      };

      addMessageToCurrentSession(errorMessage);
      message.error('發送消息失敗');
    } finally {
      setChatLoading(false);
      setUploadingFile(null);
    }
  };

  // 處理鍵盤事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 處理快速回復（舊格式）
  const handleQuickReplyClick = (item: any) => {
    const text = item?.action?.text || item?.action?.label || item?.action?.title || item?.text || item?.label || item?.title;
    if (text && typeof text === 'string' && text.trim()) {
      // 設置輸入框顯示文本
      setInputValue(text);
      // 直接發送消息
      handleSendMessage(text);
    }
  };

  // 處理快速回復（新格式）
  const handleNewQuickReplyClick = (item: any) => {
    const text = item?.displayText || item?.title || item?.text || item?.label;
    if (text && typeof text === 'string' && text.trim()) {
      // 設置輸入框顯示文本
      setInputValue(text);
      // 直接發送消息
      handleSendMessage(text);
    }
  };

  // 移除上傳的文件
  const handleRemoveFile = () => {
    setUploadingFile(null);
  };

  // 上傳配置已移至 AttachmentPanel 組件中處理

  return (
    <Layout className="h-full transition-colors duration-300" style={{ background: 'var(--color-bg-secondary)' }}>
      {/* 聊天頭部 */}
      <Header
        className="px-6 flex items-center justify-between transition-colors duration-300"
        style={{
          background: 'var(--color-surface-primary)',
          borderBottom: '1px solid var(--color-border-primary)',
          height: '64px'
        }}
      >
        <div className="flex items-center space-x-4">
          <Title level={4} className="!mb-0" style={{ color: 'var(--color-text-primary)' }}>
            💬 智慧聊天
          </Title>
          {currentSession && (
            <Space>
              <Text className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                會話 ID: {currentSession.id.slice(-8)}
              </Text>
              {attachments.length > 0 && (
                <Tag color="blue" className="text-xs">
                  {attachments.length} 個附件作為知識背景
                </Tag>
              )}
            </Space>
          )}
        </div>

        <Space>
          <Button
            type="default"
            icon={<PlusOutlined />}
            onClick={handleNewChat}
            disabled={!hasUserInfo}
            style={{
              borderColor: 'var(--color-neon-blue)',
              color: 'var(--color-neon-blue)'
            }}
          >
            新對話
          </Button>
          {currentSession && currentSession.messages.length > 0 && (
            <Button
              type="default"
              icon={<MdDelete />}
              onClick={handleClearChat}
            >
              清空對話
            </Button>
          )}
        </Space>
      </Header>

      {/* 聊天內容區域 */}
      <Content className="flex flex-col transition-colors duration-300" style={{ background: 'var(--color-bg-secondary)' }}>
        {/* 消息列表 */}
        <div className="flex-1 overflow-y-auto p-4">
          {!hasUserInfo ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center space-y-4">
                <div className="text-6xl">⚠️</div>
                <Title level={3} style={{ color: 'var(--color-text-primary)' }}>
                  需要設置 URL 參數
                </Title>
                <Text style={{ color: 'var(--color-text-secondary)' }} className="block">
                  請在 URL 中添加必要的參數：<br />
                  ?TenantID=your_tenant&ServiceID=your_service&UserID=your_user
                </Text>
              </div>
            </div>
          ) : !currentSession ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center space-y-4">
                <div className="text-6xl">💬</div>
                <AnimatedText
                  text={`歡迎使用 ${appName}`}
                  animation="slideUp"
                  duration={0.8}
                  as="h3"
                  className="text-2xl font-semibold"
                  style={{ color: 'var(--color-text-primary)' }}
                />
                <AnimatedText
                  text={`點擊右上角"新對話"按鈕開始與 AI 助手交流${
                    attachments.length > 0
                      ? `\n已載入 ${attachments.length} 個附件作為知識背景`
                      : ''
                  }`}
                  animation="fadeIn"
                  duration={1}
                  delay={0.3}
                  as="p"
                  className="whitespace-pre-line"
                  style={{ color: 'var(--color-text-secondary)' }}
                />
              </div>
            </div>
          ) : currentSession.messages.length === 0 ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center space-y-4">
                <div className="text-4xl">🤖</div>
                <Title level={4} style={{ color: 'var(--color-text-primary)' }}>
                  開始新的對話
                </Title>
                <Text style={{ color: 'var(--color-text-secondary)' }}>
                  在下方輸入框中輸入您的問題，AI 助手將為您提供幫助
                </Text>
              </div>
            </div>
          ) : (
            <ListAnimation
              type="slideUp"
              stagger={0.1}
              duration={0.3}
              className="space-y-4"
            >
              {currentSession.messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex items-start space-x-3 max-w-[80%]`}>
                    {message.type === 'assistant' && (
                      <Avatar
                        icon={<MdSmartToy />}
                        style={{ background: 'var(--color-neon-blue)' }}
                      />
                    )}
                    <SpotlightCard
                      className="transition-colors duration-300"
                      backgroundColor={
                        message.type === 'user'
                          ? 'var(--color-neon-blue)'
                          : 'var(--color-surface-primary)'
                      }
                      spotlightColor={
                        message.type === 'user'
                          ? 'rgba(255, 255, 255, 0.2)'
                          : 'rgba(167, 139, 250, 0.3)'
                      }
                      padding="12px 16px"
                      borderRadius={12}
                      disabled={message.type === 'user'} // 用戶消息不需要光效
                      style={{
                        border: message.type === 'user'
                          ? 'none'
                          : '1px solid var(--color-border-primary)',
                        color: message.type === 'user'
                          ? 'white'
                          : 'var(--color-text-primary)'
                      }}
                    >
                      {/* 渲染媒體消息 */}
                      {message.mediaData ? (
                        <MediaMessageRenderer
                          mediaData={message.mediaData}
                          isUser={message.type === 'user'}
                        />
                      ) : message.parsedContent ? (
                        <MessageRenderer
                          parsedContent={message.parsedContent}
                          isUser={message.type === 'user'}
                          onQuickReplyClick={handleQuickReplyClick}
                          onNewQuickReplyClick={handleNewQuickReplyClick}
                        />
                      ) : (
                        <div className="whitespace-pre-wrap">{message.content}</div>
                      )}
                      <div className="text-xs mt-2 opacity-70">
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </div>
                    </SpotlightCard>
                    {message.type === 'user' && (
                      <Avatar
                        icon={<UserOutlined />}
                        style={{ background: 'var(--color-text-tertiary)' }}
                      />
                    )}
                  </div>
                </div>
              ))}
              {chatLoading && (
                <div className="flex justify-start">
                  <div className="flex items-start space-x-3">
                    <div className="flex items-center space-x-2">
                      <Avatar
                        icon={<MdSmartToy />}
                        style={{ background: 'var(--color-neon-blue)' }}
                      />
                      {/* 計時器顯示在機器人頭像後面 */}
                      {timerIsRunning && currentMessageId === 'waiting' && (
                        <div className="text-xs text-gray-500">
                          <ResponseTimer
                            elapsedTime={elapsedTime}
                            isRunning={true}
                          />
                        </div>
                      )}
                    </div>
                    <SpotlightCard
                      className="transition-colors duration-300"
                      backgroundColor="var(--color-surface-primary)"
                      spotlightColor="rgba(167, 139, 250, 0.3)"
                      padding="12px 16px"
                      borderRadius={12}
                      style={{
                        border: '1px solid var(--color-border-primary)',
                        color: 'var(--color-text-primary)'
                      }}
                    >
                      <div className="flex items-center space-x-2">
                        <AnimatedText
                          text="AI 正在思考中..."
                          animation="typewriter"
                          duration={2}
                          repeat={true}
                        />
                      </div>
                    </SpotlightCard>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </ListAnimation>
          )}
        </div>

        {/* 輸入區域 */}
        {currentSession && hasUserInfo && (
          <div
            className="border-t p-4 transition-colors duration-300"
            style={{ borderTop: '1px solid var(--color-border-primary)' }}
          >
            {/* 文件上傳提示 */}
            {uploadingFile && (
              <div className="mb-3 p-2 rounded-lg transition-colors duration-300"
                   style={{
                     background: 'var(--color-bg-tertiary)',
                     border: '1px solid var(--color-border-secondary)'
                   }}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <PaperClipOutlined style={{ color: 'var(--color-neon-blue)' }} />
                    <Text className="text-sm" style={{ color: 'var(--color-text-primary)' }}>
                      已選擇文件：{uploadingFile.name}
                    </Text>
                  </div>
                  <Button
                    type="text"
                    size="small"
                    onClick={handleRemoveFile}
                    style={{ color: 'var(--color-text-tertiary)' }}
                  >
                    移除
                  </Button>
                </div>
              </div>
            )}

            {/* 媒體預覽區域 */}
            {mediaInput.hasPreview && (
              <>
                {mediaInput.previewData.type === 'image' && mediaInput.previewData.file && (
                  <ImagePreview
                    file={mediaInput.previewData.file}
                    previewUrl={mediaInput.previewData.url!}
                    onSend={mediaInput.confirmSend}
                    onCancel={mediaInput.cancelOperation}
                    loading={mediaInput.isUploading}
                  />
                )}
              </>
            )}

            {/* 錄音狀態指示器 - 居中顯示 */}
            {(voiceRecorder.isRecording || voiceRecorder.isPaused || voiceRecorder.isProcessing) && (
              <div className="flex justify-center items-center w-full py-4">
                <RecordingIndicator
                  status={voiceRecorder.recordState.status}
                  duration={voiceRecorder.recordState.duration}
                  volume={voiceRecorder.recordState.volume}
                  maxDuration={60}
                  analyser={voiceRecorder.analyser || undefined}
                  onStop={voiceRecorder.stopRecording}
                  onPause={voiceRecorder.pauseRecording}
                  onResume={voiceRecorder.resumeRecording}
                />
              </div>
            )}

            {/* 語音預覽區域 - 居中顯示 */}
            {voiceRecorder.recordState.audioBlob && voiceRecorder.recordState.audioUrl && (
              <div className="flex justify-center items-center w-full py-4">
                <VoicePreview
                  audioBlob={voiceRecorder.recordState.audioBlob}
                  audioUrl={voiceRecorder.recordState.audioUrl}
                  duration={voiceRecorder.recordState.duration}
                  onSend={async () => {
                    try {
                      await mediaInput.handleVoiceUpload(voiceRecorder.recordState.audioBlob!);
                      // 語音發送成功後重置錄音狀態，關閉預覽界面
                      voiceRecorder.resetRecording();
                    } catch (error) {
                      console.error('❌ 語音發送失敗:', error);
                      // 發送失敗時不重置狀態，讓用戶可以重試
                    }
                  }}
                  onCancel={() => {
                    voiceRecorder.resetRecording();
                  }}
                  loading={mediaInput.isUploading}
                />
              </div>
            )}

            <div className="flex space-x-2">
              {/* 媒體輸入按鈕 */}
              <div className="flex space-x-2">
                <ImageUploadButton
                  onFileSelect={mediaInput.handleFileSelect}
                  loading={mediaInput.isProcessing}
                  disabled={chatLoading || voiceRecorder.isRecording}
                  uploadStatus={mediaInput.uploadState.status}
                />
                <VoiceRecordButton
                  status={voiceRecorder.recordState.status}
                  duration={voiceRecorder.recordState.duration}
                  volume={voiceRecorder.recordState.volume}
                  maxDuration={60} // 60秒最大錄音時長
                  onStartRecord={voiceRecorder.startRecording}
                  onStopRecord={voiceRecorder.stopRecording}
                  onPauseRecord={voiceRecorder.pauseRecording}
                  onResumeRecord={voiceRecorder.resumeRecording}
                  disabled={chatLoading || mediaInput.isProcessing}
                />
              </div>

              <div className="flex-1">
                <AnimatedTextArea
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="輸入您的問題..."
                  autoSize={{ minRows: 1, maxRows: 4 }}
                  animationType="glow"
                  disabled={chatLoading || voiceRecorder.isRecording}
                  className="flex-1 transition-colors duration-300"
                  style={{
                    background: 'var(--color-surface-primary)',
                    border: '1px solid var(--color-border-primary)',
                    color: 'var(--color-text-primary)'
                  }}
                />
              </div>
              <AnimatedButton
                type="primary"
                icon={chatLoading ? <LoadingOutlined /> : <MdSend />}
                onClick={() => handleSendMessage()}
                loading={chatLoading}
                disabled={!inputValue.trim() || voiceRecorder.isRecording}
                style={{ background: 'var(--color-neon-blue)' }}
                animationType="glow"
                hoverScale={1.05}
                tapScale={0.95}
              >
                {chatLoading ? '發送中' : '發送'}
              </AnimatedButton>
            </div>
          </div>
        )}
      </Content>
    </Layout>
  );
};

export default ChatPanel;
