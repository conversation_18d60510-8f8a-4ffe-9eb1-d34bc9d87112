/**
 * 媒體消息渲染組件
 * 用於渲染圖片和語音消息
 */

import React, { useState } from 'react';
import { Image, Button, Typography, Space, Modal, Card } from 'antd';
import {
  PictureOutlined,
  SoundOutlined,
  EyeOutlined,
  DownloadOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import { MediaData } from '@/types/chat';
import { formatFileSize } from '@/utils/mediaUtils';
import VoicePlayer from './MediaInput/VoicePlayer';

const { Text } = Typography;

interface MediaMessageRendererProps {
  mediaData: MediaData;
  isUser?: boolean;
  className?: string;
}

/**
 * 媒體消息渲染組件
 */
const MediaMessageRenderer: React.FC<MediaMessageRendererProps> = ({
  mediaData,
  isUser = false,
  className = ''
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);

  // 處理圖片預覽
  const handleImagePreview = () => {
    setPreviewVisible(true);
  };

  // 處理下載
  const handleDownload = () => {
    try {
      const link = document.createElement('a');
      link.href = mediaData.data;
      link.download = mediaData.fileName || `media-${Date.now()}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('下載失敗:', error);
    }
  };

  // 渲染圖片消息
  const renderImageMessage = () => {
    return (
      <div className={`media-message image-message ${className}`}>
        <div 
          className={`
            relative inline-block rounded-lg overflow-hidden shadow-sm
            ${isUser ? 'bg-blue-50' : 'bg-gray-50'}
            hover:shadow-md transition-shadow duration-200
          `}
          style={{ maxWidth: '200px' }}
        >
          {/* 圖片縮略圖 */}
          <Image
            src={mediaData.thumbnailUrl || mediaData.data}
            alt={mediaData.fileName || '圖片'}
            style={{
              width: '100%',
              maxWidth: '200px',
              height: 'auto',
              maxHeight: '150px',
              objectFit: 'cover',
              cursor: 'pointer'
            }}
            preview={false}
            onClick={handleImagePreview}
            placeholder={
              <div className="flex items-center justify-center h-32 bg-gray-100">
                <PictureOutlined className="text-2xl text-gray-400" />
              </div>
            }
          />
          
          {/* 懸浮操作按鈕 */}
          <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center opacity-0 hover:opacity-100">
            <Space>
              <Button
                type="text"
                icon={<EyeOutlined />}
                onClick={handleImagePreview}
                className="text-white hover:text-blue-300"
                size="small"
              />
              <Button
                type="text"
                icon={<DownloadOutlined />}
                onClick={handleDownload}
                className="text-white hover:text-blue-300"
                size="small"
              />
            </Space>
          </div>
        </div>

        {/* 圖片信息 */}
        <div className="mt-1">
          <Text type="secondary" className="text-xs">
            {mediaData.fileName && (
              <span className="mr-2">{mediaData.fileName}</span>
            )}
            {mediaData.fileSize && (
              <span>{formatFileSize(mediaData.fileSize)}</span>
            )}
          </Text>
        </div>

        {/* 圖片預覽模態框 */}
        <Modal
          open={previewVisible}
          onCancel={() => setPreviewVisible(false)}
          footer={[
            <Button key="download" icon={<DownloadOutlined />} onClick={handleDownload}>
              下載
            </Button>,
            <Button key="close" type="primary" onClick={() => setPreviewVisible(false)}>
              關閉
            </Button>
          ]}
          width="80%"
          style={{ maxWidth: '800px' }}
          title={mediaData.fileName || '圖片預覽'}
          centered
        >
          <div className="flex justify-center">
            <Image
              src={mediaData.data}
              alt={mediaData.fileName || '圖片'}
              style={{
                maxWidth: '100%',
                maxHeight: '70vh',
                objectFit: 'contain'
              }}
              preview={false}
            />
          </div>
        </Modal>
      </div>
    );
  };

  // 渲染語音消息
  const renderVoiceMessage = () => {
    return (
      <div className={`media-message voice-message ${className}`}>
        <Card
          size="small"
          className={`
            inline-block max-w-sm
            ${isUser ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'}
          `}
          bodyStyle={{ padding: '12px 16px' }}
        >
          {/* 使用 VoicePlayer 組件 */}
          <VoicePlayer
            audioData={mediaData.data}
            fileName={mediaData.fileName}
            fileSize={mediaData.fileSize}
            compact={true}
            showWaveform={false}
            showDownload={true}
            className={isUser ? 'voice-player-user' : 'voice-player-assistant'}
            onPlay={() => {
              console.log('🎵 開始播放語音消息:', mediaData.fileName);
            }}
            onPause={() => {
              console.log('⏸️ 暫停播放語音消息:', mediaData.fileName);
            }}
            onEnded={() => {
              console.log('🏁 語音消息播放結束:', mediaData.fileName);
            }}
            onError={(error) => {
              console.error('❌ 語音消息播放錯誤:', error);
            }}
          />
        </Card>
      </div>
    );
  };

  // 根據媒體類型渲染對應組件
  if (mediaData.type === 'image') {
    return renderImageMessage();
  } else if (mediaData.type === 'voice') {
    return renderVoiceMessage();
  }

  // 未知媒體類型的後備渲染
  return (
    <div className={`media-message unknown-message ${className}`}>
      <Card size="small" className="inline-block">
        <div className="flex items-center space-x-2">
          <PictureOutlined className="text-gray-400" />
          <Text type="secondary">不支援的媒體類型</Text>
        </div>
      </Card>
    </div>
  );
};

export default MediaMessageRenderer;
