/**
 * MediaMessageRenderer 組件測試
 */


import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import MediaMessageRenderer from '../MediaMessageRenderer';
import { MediaData } from '@/types/chat';

// Mock Antd 組件
vi.mock('antd', async () => {
  const actual = await vi.importActual('antd');
  return {
    ...actual,
    Image: ({ src, alt, onClick, placeholder }: any) => (
      <div data-testid="mock-image" onClick={onClick}>
        {src ? <img src={src} alt={alt} /> : placeholder}
      </div>
    ),
    Modal: ({ open, children, onCancel, title }: any) => 
      open ? (
        <div data-testid="mock-modal">
          <div>{title}</div>
          <button onClick={onCancel}>關閉</button>
          {children}
        </div>
      ) : null,
  };
});

describe('MediaMessageRenderer', () => {
  const mockImageData: MediaData = {
    type: 'image',
    data: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    fileName: 'test-image.png',
    fileSize: 1024,
    mimeType: 'image/png',
    thumbnailUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
  };

  const mockVoiceData: MediaData = {
    type: 'voice',
    data: 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT',
    fileName: 'test-voice.wav',
    fileSize: 2048,
    mimeType: 'audio/wav'
  };

  describe('圖片消息渲染', () => {
    it('應該正確渲染圖片消息', () => {
      render(<MediaMessageRenderer mediaData={mockImageData} />);
      
      expect(screen.getByTestId('mock-image')).toBeInTheDocument();
      expect(screen.getByText('test-image.png')).toBeInTheDocument();
      expect(screen.getByText('1.00 KB')).toBeInTheDocument();
    });

    it('應該在用戶消息中應用正確的樣式', () => {
      render(<MediaMessageRenderer mediaData={mockImageData} isUser={true} />);
      
      const imageContainer = screen.getByTestId('mock-image').parentElement;
      expect(imageContainer).toHaveClass('bg-blue-50');
    });

    it('應該在點擊圖片時打開預覽模態框', () => {
      render(<MediaMessageRenderer mediaData={mockImageData} />);
      
      // 點擊圖片
      fireEvent.click(screen.getByTestId('mock-image'));
      
      // 檢查模態框是否出現
      expect(screen.getByTestId('mock-modal')).toBeInTheDocument();
      expect(screen.getByText('test-image.png')).toBeInTheDocument();
    });

    it('應該能夠關閉預覽模態框', () => {
      render(<MediaMessageRenderer mediaData={mockImageData} />);
      
      // 打開模態框
      fireEvent.click(screen.getByTestId('mock-image'));
      expect(screen.getByTestId('mock-modal')).toBeInTheDocument();
      
      // 關閉模態框
      fireEvent.click(screen.getByText('關閉'));
      expect(screen.queryByTestId('mock-modal')).not.toBeInTheDocument();
    });

    it('應該處理沒有文件名的情況', () => {
      const dataWithoutFileName = { ...mockImageData, fileName: undefined };
      render(<MediaMessageRenderer mediaData={dataWithoutFileName} />);
      
      expect(screen.getByTestId('mock-image')).toBeInTheDocument();
      expect(screen.getByText('1.00 KB')).toBeInTheDocument();
    });
  });

  describe('語音消息渲染', () => {
    it('應該正確渲染語音消息', () => {
      render(<MediaMessageRenderer mediaData={mockVoiceData} />);
      
      expect(screen.getByText('語音消息')).toBeInTheDocument();
      expect(screen.getByText('test-voice.wav')).toBeInTheDocument();
      expect(screen.getByText('2.00 KB')).toBeInTheDocument();
      expect(screen.getByText('播放')).toBeInTheDocument();
    });

    it('應該在用戶消息中應用正確的樣式', () => {
      render(<MediaMessageRenderer mediaData={mockVoiceData} isUser={true} />);
      
      const voiceCard = screen.getByText('語音消息').closest('.ant-card');
      expect(voiceCard).toHaveClass('bg-blue-50', 'border-blue-200');
    });

    it('應該在點擊播放按鈕時觸發播放', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      render(<MediaMessageRenderer mediaData={mockVoiceData} />);
      
      fireEvent.click(screen.getByText('播放'));
      
      expect(consoleSpy).toHaveBeenCalledWith('播放語音:', mockVoiceData.data);
      
      consoleSpy.mockRestore();
    });

    it('應該處理沒有文件名的情況', () => {
      const dataWithoutFileName = { ...mockVoiceData, fileName: undefined };
      render(<MediaMessageRenderer mediaData={dataWithoutFileName} />);
      
      expect(screen.getByText('語音消息')).toBeInTheDocument();
      expect(screen.getByText('2.00 KB')).toBeInTheDocument();
    });
  });

  describe('下載功能', () => {
    it('應該在圖片消息中提供下載功能', () => {
      // Mock document.createElement 和相關方法
      const mockLink = {
        href: '',
        download: '',
        click: vi.fn(),
      };
      const createElementSpy = vi.spyOn(document, 'createElement').mockReturnValue(mockLink as any);
      const appendChildSpy = vi.spyOn(document.body, 'appendChild').mockImplementation(() => mockLink as any);
      const removeChildSpy = vi.spyOn(document.body, 'removeChild').mockImplementation(() => mockLink as any);

      render(<MediaMessageRenderer mediaData={mockImageData} />);
      
      // 打開預覽模態框
      fireEvent.click(screen.getByTestId('mock-image'));
      
      // 點擊下載按鈕
      fireEvent.click(screen.getByText('下載'));
      
      expect(createElementSpy).toHaveBeenCalledWith('a');
      expect(mockLink.href).toBe(mockImageData.data);
      expect(mockLink.download).toBe(mockImageData.fileName);
      expect(mockLink.click).toHaveBeenCalled();
      expect(appendChildSpy).toHaveBeenCalledWith(mockLink);
      expect(removeChildSpy).toHaveBeenCalledWith(mockLink);

      // 清理 mocks
      createElementSpy.mockRestore();
      appendChildSpy.mockRestore();
      removeChildSpy.mockRestore();
    });
  });

  describe('未知媒體類型', () => {
    it('應該渲染未知媒體類型的後備內容', () => {
      const unknownMediaData = {
        ...mockImageData,
        type: 'unknown' as any
      };
      
      render(<MediaMessageRenderer mediaData={unknownMediaData} />);
      
      expect(screen.getByText('不支援的媒體類型')).toBeInTheDocument();
    });
  });

  describe('自定義樣式', () => {
    it('應該應用自定義 className', () => {
      const { container } = render(
        <MediaMessageRenderer 
          mediaData={mockImageData} 
          className="custom-class" 
        />
      );
      
      expect(container.firstChild).toHaveClass('custom-class');
    });
  });
});
