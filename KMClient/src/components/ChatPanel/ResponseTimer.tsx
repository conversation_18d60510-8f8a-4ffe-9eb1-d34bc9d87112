/**
 * AI回復計時器組件
 * 顯示AI回復完成後的經過時間
 */

import React from 'react';
import { Typography } from 'antd';
import { ClockCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface ResponseTimerProps {
  elapsedTime: number;
  isRunning: boolean;
  className?: string;
  style?: React.CSSProperties;
}

const ResponseTimer: React.FC<ResponseTimerProps> = ({
  elapsedTime,
  isRunning,
  className = '',
  style = {},
}) => {
  // 如果計時器沒有運行，不顯示
  if (!isRunning) {
    return null;
  }

  // 格式化時間顯示
  const formatTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}秒`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}分${remainingSeconds}秒`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;
      return `${hours}時${minutes}分${remainingSeconds}秒`;
    }
  };

  return (
    <div 
      className={`response-timer flex items-center space-x-1 mt-2 ${className}`}
      style={{
        opacity: 0.7,
        fontSize: '12px',
        color: 'var(--color-text-tertiary)',
        ...style,
      }}
    >
      <ClockCircleOutlined 
        style={{ 
          fontSize: '12px',
          color: 'var(--color-text-tertiary)',
        }} 
      />
      <Text 
        style={{ 
          fontSize: '12px',
          color: 'var(--color-text-tertiary)',
          fontWeight: 400,
        }}
      >
        {formatTime(elapsedTime)}
      </Text>
    </div>
  );
};

export default ResponseTimer;
