/**
 * AnimatedModal - 帶有動畫的模態框組件
 * 提供流暢的顯示/隱藏動畫效果
 */

import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useMotionPreferences } from './MotionProvider';

export type ModalAnimationType = 
  | 'fade' 
  | 'scale' 
  | 'slideUp' 
  | 'slideDown' 
  | 'slideLeft' 
  | 'slideRight'
  | 'flip'
  | 'zoom';

interface AnimatedModalProps {
  children: React.ReactNode;
  isOpen: boolean;
  onClose?: () => void;
  animationType?: ModalAnimationType;
  duration?: number;
  className?: string;
  overlayClassName?: string;
  contentClassName?: string;
  style?: React.CSSProperties;
  overlayStyle?: React.CSSProperties;
  contentStyle?: React.CSSProperties;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
}

const AnimatedModal: React.FC<AnimatedModalProps> = ({
  children,
  isOpen,
  onClose,
  animationType = 'scale',
  duration = 0.3,
  className = '',
  overlayClassName = '',
  contentClassName = '',
  style = {},
  overlayStyle = {},
  contentStyle = {},
  closeOnOverlayClick = true,
  closeOnEscape = true,
}) => {
  const { prefersReducedMotion, animationSpeed } = useMotionPreferences();

  // ESC 鍵關閉模態框
  useEffect(() => {
    if (!closeOnEscape || !isOpen) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose?.();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, onClose]);

  // 防止背景滾動
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // 遮罩動畫變體
  const overlayVariants = {
    open: {
      opacity: 1,
      transition: {
        duration: prefersReducedMotion ? 0 : duration / animationSpeed,
      },
    },
    closed: {
      opacity: 0,
      transition: {
        duration: prefersReducedMotion ? 0 : duration / animationSpeed,
      },
    },
  };

  // 內容動畫變體
  const getContentVariants = () => {
    const adjustedDuration = prefersReducedMotion ? 0 : duration / animationSpeed;

    switch (animationType) {
      case 'fade':
        return {
          open: {
            opacity: 1,
            transition: { duration: adjustedDuration },
          },
          closed: {
            opacity: 0,
            transition: { duration: adjustedDuration },
          },
        };

      case 'scale':
        return {
          open: {
            opacity: 1,
            scale: 1,
            transition: {
              duration: adjustedDuration,
              ease: "easeOut" as const,
            },
          },
          closed: {
            opacity: 0,
            scale: 0.8,
            transition: {
              duration: adjustedDuration,
              ease: "easeIn" as const,
            },
          },
        };

      case 'slideUp':
        return {
          open: {
            opacity: 1,
            y: 0,
            transition: {
              duration: adjustedDuration,
              ease: "easeOut" as const,
            },
          },
          closed: {
            opacity: 0,
            y: 100,
            transition: {
              duration: adjustedDuration,
              ease: "easeIn" as const,
            },
          },
        };

      case 'slideDown':
        return {
          open: {
            opacity: 1,
            y: 0,
            transition: {
              duration: adjustedDuration,
              ease: "easeOut" as const,
            },
          },
          closed: {
            opacity: 0,
            y: -100,
            transition: {
              duration: adjustedDuration,
              ease: "easeIn" as const,
            },
          },
        };

      case 'slideLeft':
        return {
          open: {
            opacity: 1,
            x: 0,
            transition: {
              duration: adjustedDuration,
              ease: "easeOut" as const,
            },
          },
          closed: {
            opacity: 0,
            x: 100,
            transition: {
              duration: adjustedDuration,
              ease: "easeIn" as const,
            },
          },
        };

      case 'slideRight':
        return {
          open: {
            opacity: 1,
            x: 0,
            transition: {
              duration: adjustedDuration,
              ease: "easeOut" as const,
            },
          },
          closed: {
            opacity: 0,
            x: -100,
            transition: {
              duration: adjustedDuration,
              ease: "easeIn" as const,
            },
          },
        };

      case 'flip':
        return {
          open: {
            opacity: 1,
            rotateX: 0,
            transition: {
              duration: adjustedDuration,
              ease: "easeOut" as const,
            },
          },
          closed: {
            opacity: 0,
            rotateX: -90,
            transition: {
              duration: adjustedDuration,
              ease: "easeIn" as const,
            },
          },
        };

      case 'zoom':
        return {
          open: {
            opacity: 1,
            scale: 1,
            transition: {
              duration: adjustedDuration,
              type: "spring" as const,
              stiffness: 300,
              damping: 20,
            },
          },
          closed: {
            opacity: 0,
            scale: 0.3,
            transition: {
              duration: adjustedDuration,
              ease: "easeIn" as const,
            },
          },
        };

      default:
        return {
          open: { opacity: 1 },
          closed: { opacity: 0 },
        };
    }
  };

  const contentVariants = getContentVariants();

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && closeOnOverlayClick) {
      onClose?.();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className={`fixed inset-0 z-50 flex items-center justify-center ${className}`}
          style={style}
          initial="closed"
          animate="open"
          exit="closed"
        >
          {/* 遮罩層 */}
          <motion.div
            className={`absolute inset-0 bg-black ${overlayClassName}`}
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              ...overlayStyle,
            }}
            variants={overlayVariants}
            onClick={handleOverlayClick}
          />

          {/* 模態框內容 */}
          <motion.div
            className={`relative z-10 ${contentClassName}`}
            style={{
              maxWidth: '90vw',
              maxHeight: '90vh',
              ...contentStyle,
            }}
            variants={contentVariants}
          >
            {children}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AnimatedModal;
