/**
 * AnimatedInput - 帶有動畫的輸入框組件
 * 基於 Ant Design Input 組件，添加 Framer Motion 動畫效果
 */

import React, { useState } from 'react';
import { Input, InputProps } from 'antd';
import { motion } from 'framer-motion';
import { useMotionPreferences } from './MotionProvider';

const { TextArea } = Input;

interface AnimatedInputProps extends InputProps {
  focusScale?: number;
  focusBorderColor?: string;
  focusGlow?: boolean;
  animationType?: 'scale' | 'glow' | 'lift' | 'none';
}

const AnimatedInput: React.FC<AnimatedInputProps> = ({
  focusScale = 1.02,
  focusBorderColor = 'var(--color-neon-blue)',
  focusGlow = true,
  animationType = 'glow',
  disabled = false,
  ...inputProps
}) => {
  const { prefersReducedMotion, animationSpeed } = useMotionPreferences();
  const [isFocused, setIsFocused] = useState(false);

  // 基礎動畫配置
  const baseTransition = {
    duration: prefersReducedMotion ? 0 : 0.2 / animationSpeed,
    ease: "easeOut" as const, // 使用字符串常量
  };

  // 獲取動畫變體
  const getAnimationVariants = () => {
    if (prefersReducedMotion || disabled || animationType === 'none') {
      return {
        initial: {},
        focused: {},
      };
    }

    switch (animationType) {
      case 'scale':
        return {
          initial: {
            scale: 1,
          },
          focused: {
            scale: focusScale,
            transition: baseTransition,
          },
        };

      case 'glow':
        return {
          initial: {
            boxShadow: '0 0 0 0 transparent',
          },
          focused: {
            boxShadow: focusGlow 
              ? `0 0 0 3px rgba(59, 130, 246, 0.2), 0 0 20px rgba(59, 130, 246, 0.1)`
              : `0 0 0 3px rgba(59, 130, 246, 0.2)`,
            transition: baseTransition,
          },
        };

      case 'lift':
        return {
          initial: {
            y: 0,
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          },
          focused: {
            y: -1,
            boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15)',
            transition: baseTransition,
          },
        };

      default:
        return {
          initial: {},
          focused: {},
        };
    }
  };

  const animationVariants = getAnimationVariants();

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    inputProps.onFocus?.(e);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    inputProps.onBlur?.(e);
  };

  return (
    <motion.div
      variants={animationVariants}
      initial="initial"
      animate={isFocused ? 'focused' : 'initial'}
      style={{ width: '100%' }}
    >
      <Input
        {...inputProps}
        disabled={disabled}
        onFocus={handleFocus}
        onBlur={handleBlur}
      />
    </motion.div>
  );
};

// TextArea 組件
interface AnimatedTextAreaProps extends React.ComponentProps<typeof TextArea> {
  focusScale?: number;
  focusBorderColor?: string;
  focusGlow?: boolean;
  animationType?: 'scale' | 'glow' | 'lift' | 'none';
}

export const AnimatedTextArea: React.FC<AnimatedTextAreaProps> = ({
  focusScale = 1.02,
  focusBorderColor = 'var(--color-neon-blue)',
  focusGlow = true,
  animationType = 'glow',
  disabled = false,
  ...textAreaProps
}) => {
  const { prefersReducedMotion, animationSpeed } = useMotionPreferences();
  const [isFocused, setIsFocused] = useState(false);

  // 基礎動畫配置
  const baseTransition = {
    duration: prefersReducedMotion ? 0 : 0.2 / animationSpeed,
    ease: "easeOut" as const, // 使用字符串常量
  };

  // 獲取動畫變體
  const getAnimationVariants = () => {
    if (prefersReducedMotion || disabled || animationType === 'none') {
      return {
        initial: {},
        focused: {},
      };
    }

    switch (animationType) {
      case 'scale':
        return {
          initial: {
            scale: 1,
          },
          focused: {
            scale: focusScale,
            transition: baseTransition,
          },
        };

      case 'glow':
        return {
          initial: {
            boxShadow: '0 0 0 0 transparent',
          },
          focused: {
            boxShadow: focusGlow 
              ? `0 0 0 3px rgba(59, 130, 246, 0.2), 0 0 20px rgba(59, 130, 246, 0.1)`
              : `0 0 0 3px rgba(59, 130, 246, 0.2)`,
            transition: baseTransition,
          },
        };

      case 'lift':
        return {
          initial: {
            y: 0,
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          },
          focused: {
            y: -1,
            boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15)',
            transition: baseTransition,
          },
        };

      default:
        return {
          initial: {},
          focused: {},
        };
    }
  };

  const animationVariants = getAnimationVariants();

  const handleFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    setIsFocused(true);
    textAreaProps.onFocus?.(e);
  };

  const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    setIsFocused(false);
    textAreaProps.onBlur?.(e);
  };

  return (
    <motion.div
      variants={animationVariants}
      initial="initial"
      animate={isFocused ? 'focused' : 'initial'}
      style={{ width: '100%' }}
    >
      <TextArea
        {...textAreaProps}
        disabled={disabled}
        onFocus={handleFocus}
        onBlur={handleBlur}
      />
    </motion.div>
  );
};

// 預設動畫輸入框組件
export const ScaleInput: React.FC<AnimatedInputProps> = (props) => (
  <AnimatedInput {...props} animationType="scale" />
);

export const GlowInput: React.FC<AnimatedInputProps> = (props) => (
  <AnimatedInput {...props} animationType="glow" />
);

export const LiftInput: React.FC<AnimatedInputProps> = (props) => (
  <AnimatedInput {...props} animationType="lift" />
);

export default AnimatedInput;
