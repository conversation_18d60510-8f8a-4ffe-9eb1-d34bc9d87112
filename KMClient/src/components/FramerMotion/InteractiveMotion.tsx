/**
 * InteractiveMotion - 交互動畫組件
 * 提供懸停、點擊、聚焦等交互狀態的動畫反饋
 */

import React from 'react';
import { motion, MotionProps } from 'framer-motion';
import { useMotionPreferences } from './MotionProvider';

export type InteractionType = 'hover' | 'tap' | 'focus' | 'all';

interface InteractiveMotionProps extends Omit<MotionProps, 'whileHover' | 'whileTap' | 'whileFocus'> {
  children: React.ReactNode;
  interactions?: InteractionType[];
  hoverScale?: number;
  tapScale?: number;
  focusScale?: number;
  hoverRotate?: number;
  tapRotate?: number;
  hoverBrightness?: number;
  tapBrightness?: number;
  shadowIntensity?: number;
  className?: string;
  style?: React.CSSProperties;
  disabled?: boolean;
}

const InteractiveMotion: React.FC<InteractiveMotionProps> = ({
  children,
  interactions = ['all'],
  hoverScale = 1.05,
  tapScale = 0.95,
  focusScale = 1.02,
  hoverRotate = 0,
  tapRotate = 0,
  hoverBrightness = 1.1,
  tapBrightness = 0.9,
  shadowIntensity = 1,
  className = '',
  style = {},
  disabled = false,
  ...motionProps
}) => {
  const { prefersReducedMotion, animationSpeed } = useMotionPreferences();

  const hasInteraction = (type: InteractionType) => 
    !disabled && (interactions.includes('all') || interactions.includes(type));

  // 基礎動畫配置
  const baseTransition = {
    duration: prefersReducedMotion ? 0 : 0.2 / animationSpeed,
    ease: "easeOut" as const,
  };

  // 懸停動畫
  const whileHover = hasInteraction('hover') ? {
    scale: prefersReducedMotion ? 1 : hoverScale,
    rotate: prefersReducedMotion ? 0 : hoverRotate,
    filter: prefersReducedMotion ? 'none' : `brightness(${hoverBrightness})`,
    boxShadow: prefersReducedMotion 
      ? 'none' 
      : `0 ${8 * shadowIntensity}px ${25 * shadowIntensity}px rgba(0, 0, 0, 0.15)`,
    transition: baseTransition,
  } : undefined;

  // 點擊動畫
  const whileTap = hasInteraction('tap') ? {
    scale: prefersReducedMotion ? 1 : tapScale,
    rotate: prefersReducedMotion ? 0 : tapRotate,
    filter: prefersReducedMotion ? 'none' : `brightness(${tapBrightness})`,
    transition: {
      ...baseTransition,
      duration: prefersReducedMotion ? 0 : 0.1 / animationSpeed,
    },
  } : undefined;

  // 聚焦動畫
  const whileFocus = hasInteraction('focus') ? {
    scale: prefersReducedMotion ? 1 : focusScale,
    boxShadow: prefersReducedMotion 
      ? 'none' 
      : `0 0 0 3px rgba(59, 130, 246, 0.3)`,
    transition: baseTransition,
  } : undefined;

  return (
    <motion.div
      className={className}
      style={{
        cursor: disabled ? 'not-allowed' : 'pointer',
        opacity: disabled ? 0.6 : 1,
        ...style,
      }}
      whileHover={whileHover}
      whileTap={whileTap}
      whileFocus={whileFocus}
      transition={baseTransition}
      {...motionProps}
    >
      {children}
    </motion.div>
  );
};

// 預設交互組件
export const HoverMotion: React.FC<Omit<InteractiveMotionProps, 'interactions'>> = (props) => (
  <InteractiveMotion {...props} interactions={['hover']} />
);

export const TapMotion: React.FC<Omit<InteractiveMotionProps, 'interactions'>> = (props) => (
  <InteractiveMotion {...props} interactions={['tap']} />
);

export const FocusMotion: React.FC<Omit<InteractiveMotionProps, 'interactions'>> = (props) => (
  <InteractiveMotion {...props} interactions={['focus']} />
);

// 按鈕專用動畫組件
interface ButtonMotionProps extends Omit<InteractiveMotionProps, 'interactions'> {
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'small' | 'medium' | 'large';
}

export const ButtonMotion: React.FC<ButtonMotionProps> = ({
  variant = 'primary',
  size = 'medium',
  hoverScale,
  tapScale,
  shadowIntensity,
  ...props
}) => {
  // 根據變體調整動畫參數
  const getAnimationParams = () => {
    switch (variant) {
      case 'primary':
        return {
          hoverScale: hoverScale ?? 1.05,
          tapScale: tapScale ?? 0.95,
          shadowIntensity: shadowIntensity ?? 1.2,
        };
      case 'secondary':
        return {
          hoverScale: hoverScale ?? 1.03,
          tapScale: tapScale ?? 0.97,
          shadowIntensity: shadowIntensity ?? 0.8,
        };
      case 'ghost':
        return {
          hoverScale: hoverScale ?? 1.02,
          tapScale: tapScale ?? 0.98,
          shadowIntensity: shadowIntensity ?? 0.5,
        };
      default:
        return {
          hoverScale: hoverScale ?? 1.05,
          tapScale: tapScale ?? 0.95,
          shadowIntensity: shadowIntensity ?? 1,
        };
    }
  };

  const animationParams = getAnimationParams();

  return (
    <InteractiveMotion
      {...props}
      {...animationParams}
      interactions={['hover', 'tap']}
    />
  );
};

// 卡片專用動畫組件
export const CardMotion: React.FC<Omit<InteractiveMotionProps, 'interactions'>> = (props) => (
  <InteractiveMotion
    {...props}
    interactions={['hover']}
    hoverScale={1.02}
    shadowIntensity={1.5}
    hoverBrightness={1.05}
  />
);

export default InteractiveMotion;
