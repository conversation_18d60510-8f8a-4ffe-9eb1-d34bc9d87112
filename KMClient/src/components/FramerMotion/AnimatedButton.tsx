/**
 * AnimatedButton - 帶有動畫的按鈕組件
 * 基於 Ant Design Button 組件，添加 Framer Motion 動畫效果
 */

import React from 'react';
import { Button, ButtonProps } from 'antd';
import { motion } from 'framer-motion';
import { useMotionPreferences } from './MotionProvider';

interface AnimatedButtonProps extends ButtonProps {
  hoverScale?: number;
  tapScale?: number;
  hoverBrightness?: number;
  tapBrightness?: number;
  shadowIntensity?: number;
  animationType?: 'scale' | 'lift' | 'glow' | 'bounce' | 'none';
}

const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  children,
  hoverScale = 1.05,
  tapScale = 0.95,
  hoverBrightness = 1.1,
  tapBrightness = 0.9,
  shadowIntensity = 1,
  animationType = 'scale',
  disabled = false,
  ...buttonProps
}) => {
  const { prefersReducedMotion, animationSpeed } = useMotionPreferences();

  // 基礎動畫配置
  const baseTransition = {
    duration: prefersReducedMotion ? 0 : 0.2 / animationSpeed,
    ease: "easeOut" as const, // 使用字符串常量
  };

  // 獲取動畫變體
  const getAnimationVariants = () => {
    if (prefersReducedMotion || disabled || animationType === 'none') {
      return {
        whileHover: {},
        whileTap: {},
      };
    }

    switch (animationType) {
      case 'scale':
        return {
          whileHover: {
            scale: hoverScale,
            filter: `brightness(${hoverBrightness})`,
            transition: baseTransition,
          },
          whileTap: {
            scale: tapScale,
            filter: `brightness(${tapBrightness})`,
            transition: {
              ...baseTransition,
              duration: 0.1 / animationSpeed,
            },
          },
        };

      case 'lift':
        return {
          whileHover: {
            y: -2,
            boxShadow: `0 ${8 * shadowIntensity}px ${25 * shadowIntensity}px rgba(0, 0, 0, 0.15)`,
            filter: `brightness(${hoverBrightness})`,
            transition: baseTransition,
          },
          whileTap: {
            y: 0,
            boxShadow: `0 ${2 * shadowIntensity}px ${8 * shadowIntensity}px rgba(0, 0, 0, 0.1)`,
            filter: `brightness(${tapBrightness})`,
            transition: {
              ...baseTransition,
              duration: 0.1 / animationSpeed,
            },
          },
        };

      case 'glow':
        return {
          whileHover: {
            boxShadow: `0 0 ${20 * shadowIntensity}px rgba(59, 130, 246, 0.4)`,
            filter: `brightness(${hoverBrightness})`,
            transition: baseTransition,
          },
          whileTap: {
            boxShadow: `0 0 ${10 * shadowIntensity}px rgba(59, 130, 246, 0.6)`,
            filter: `brightness(${tapBrightness})`,
            transition: {
              ...baseTransition,
              duration: 0.1 / animationSpeed,
            },
          },
        };

      case 'bounce':
        return {
          whileHover: {
            scale: hoverScale,
            transition: {
              type: "spring" as const,
              stiffness: 400,
              damping: 10,
            },
          },
          whileTap: {
            scale: tapScale,
            transition: {
              type: "spring" as const,
              stiffness: 600,
              damping: 15,
            },
          },
        };

      default:
        return {
          whileHover: {},
          whileTap: {},
        };
    }
  };

  const animationVariants = getAnimationVariants();

  return (
    <motion.div
      style={{ display: 'inline-block' }}
      {...animationVariants}
    >
      <Button
        {...buttonProps}
        disabled={disabled}
      >
        {children}
      </Button>
    </motion.div>
  );
};

// 預設動畫按鈕組件
export const ScaleButton: React.FC<AnimatedButtonProps> = (props) => (
  <AnimatedButton {...props} animationType="scale" />
);

export const LiftButton: React.FC<AnimatedButtonProps> = (props) => (
  <AnimatedButton {...props} animationType="lift" />
);

export const GlowButton: React.FC<AnimatedButtonProps> = (props) => (
  <AnimatedButton {...props} animationType="glow" />
);

export const BounceButton: React.FC<AnimatedButtonProps> = (props) => (
  <AnimatedButton {...props} animationType="bounce" />
);

export default AnimatedButton;
