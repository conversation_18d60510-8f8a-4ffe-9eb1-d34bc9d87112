/**
 * AnimatedRoute - 帶有動畫的路由組件
 * 為路由切換提供頁面過渡動畫
 */

import React from 'react';
import { useLocation } from 'react-router-dom';
import { PageTransitionContainer, TransitionType } from './PageTransition';

interface AnimatedRouteProps {
  children: React.ReactNode;
  transitionType?: TransitionType;
  duration?: number;
  className?: string;
  style?: React.CSSProperties;
}

const AnimatedRoute: React.FC<AnimatedRouteProps> = ({
  children,
  transitionType = 'slideRight',
  duration = 0.3,
  className = '',
  style = {},
}) => {
  const location = useLocation();

  return (
    <PageTransitionContainer
      pageKey={location.pathname}
      type={transitionType}
      duration={duration}
      className={className}
      style={style}
    >
      {children}
    </PageTransitionContainer>
  );
};

export default AnimatedRoute;
