/**
 * StatusIndicators - 狀態指示器組件
 * 提供各種狀態的視覺反饋動畫
 */

import React from 'react';
import { motion, Variants } from 'framer-motion';
import { useMotionPreferences } from './MotionProvider';
import { CheckCircleOutlined, ExclamationCircleOutlined, LoadingOutlined } from '@ant-design/icons';

export type StatusType = 'loading' | 'success' | 'error' | 'warning' | 'info';

interface StatusIndicatorProps {
  status: StatusType;
  message?: string;
  size?: 'small' | 'medium' | 'large';
  showIcon?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  message,
  size = 'medium',
  showIcon = true,
  className = '',
  style = {},
}) => {
  const { prefersReducedMotion, animationSpeed } = useMotionPreferences();

  // 尺寸配置
  const getSizeConfig = () => {
    switch (size) {
      case 'small':
        return { iconSize: 16, fontSize: '12px', padding: '8px 12px' };
      case 'large':
        return { iconSize: 24, fontSize: '16px', padding: '16px 20px' };
      default:
        return { iconSize: 20, fontSize: '14px', padding: '12px 16px' };
    }
  };

  const sizeConfig = getSizeConfig();

  // 狀態配置
  const getStatusConfig = () => {
    switch (status) {
      case 'loading':
        return {
          color: 'var(--color-neon-blue)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderColor: 'rgba(59, 130, 246, 0.3)',
          icon: <LoadingOutlined style={{ fontSize: sizeConfig.iconSize }} />,
        };
      case 'success':
        return {
          color: 'var(--color-success)',
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          borderColor: 'rgba(34, 197, 94, 0.3)',
          icon: <CheckCircleOutlined style={{ fontSize: sizeConfig.iconSize }} />,
        };
      case 'error':
        return {
          color: 'var(--color-error)',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          borderColor: 'rgba(239, 68, 68, 0.3)',
          icon: <ExclamationCircleOutlined style={{ fontSize: sizeConfig.iconSize }} />,
        };
      case 'warning':
        return {
          color: 'var(--color-warning)',
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          borderColor: 'rgba(245, 158, 11, 0.3)',
          icon: <ExclamationCircleOutlined style={{ fontSize: sizeConfig.iconSize }} />,
        };
      case 'info':
        return {
          color: 'var(--color-info)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderColor: 'rgba(59, 130, 246, 0.3)',
          icon: <ExclamationCircleOutlined style={{ fontSize: sizeConfig.iconSize }} />,
        };
      default:
        return {
          color: 'var(--color-text-primary)',
          backgroundColor: 'var(--color-surface-primary)',
          borderColor: 'var(--color-border-primary)',
          icon: null,
        };
    }
  };

  const statusConfig = getStatusConfig();

  // 動畫變體
  const containerVariants: Variants = {
    initial: {
      opacity: 0,
      scale: 0.9,
    },
    animate: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: prefersReducedMotion ? 0 : 0.3 / animationSpeed,
        ease: 'easeOut',
      },
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      transition: {
        duration: prefersReducedMotion ? 0 : 0.2 / animationSpeed,
        ease: 'easeIn',
      },
    },
  };

  const iconVariants: Variants = {
    animate: status === 'loading' && !prefersReducedMotion ? {
      rotate: 360,
      transition: {
        duration: 1 / animationSpeed,
        repeat: Infinity,
        ease: 'linear',
      },
    } : {},
  };

  return (
    <motion.div
      className={`inline-flex items-center space-x-2 rounded-lg border ${className}`}
      style={{
        padding: sizeConfig.padding,
        fontSize: sizeConfig.fontSize,
        color: statusConfig.color,
        backgroundColor: statusConfig.backgroundColor,
        borderColor: statusConfig.borderColor,
        ...style,
      }}
      variants={containerVariants}
      initial="initial"
      animate="animate"
      exit="exit"
    >
      {showIcon && statusConfig.icon && (
        <motion.div variants={iconVariants} animate="animate">
          {statusConfig.icon}
        </motion.div>
      )}
      {message && <span>{message}</span>}
    </motion.div>
  );
};

// 進度指示器
interface ProgressIndicatorProps {
  progress: number; // 0-100
  showPercentage?: boolean;
  size?: 'small' | 'medium' | 'large';
  color?: string;
  className?: string;
  style?: React.CSSProperties;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  progress,
  showPercentage = true,
  size = 'medium',
  color = 'var(--color-neon-blue)',
  className = '',
  style = {},
}) => {
  const { prefersReducedMotion, animationSpeed } = useMotionPreferences();

  const getSizeConfig = () => {
    switch (size) {
      case 'small':
        return { height: 4, fontSize: '12px' };
      case 'large':
        return { height: 12, fontSize: '16px' };
      default:
        return { height: 8, fontSize: '14px' };
    }
  };

  const sizeConfig = getSizeConfig();

  return (
    <div className={`w-full ${className}`} style={style}>
      {showPercentage && (
        <div className="flex justify-between items-center mb-2">
          <span style={{ fontSize: sizeConfig.fontSize, color: 'var(--color-text-secondary)' }}>
            進度
          </span>
          <span style={{ fontSize: sizeConfig.fontSize, color: 'var(--color-text-primary)' }}>
            {Math.round(progress)}%
          </span>
        </div>
      )}
      <div
        className="bg-gray-200 rounded-full overflow-hidden"
        style={{ height: sizeConfig.height }}
      >
        <motion.div
          className="h-full rounded-full"
          style={{ backgroundColor: color }}
          initial={{ width: 0 }}
          animate={{ width: `${Math.max(0, Math.min(100, progress))}%` }}
          transition={{
            duration: prefersReducedMotion ? 0 : 0.5 / animationSpeed,
            ease: 'easeOut',
          }}
        />
      </div>
    </div>
  );
};

// 脈衝點指示器
interface PulseIndicatorProps {
  active?: boolean;
  color?: string;
  size?: number;
  className?: string;
  style?: React.CSSProperties;
}

export const PulseIndicator: React.FC<PulseIndicatorProps> = ({
  active = true,
  color = 'var(--color-neon-blue)',
  size = 8,
  className = '',
  style = {},
}) => {
  const { prefersReducedMotion, animationSpeed } = useMotionPreferences();

  const pulseVariants: Variants = {
    animate: active && !prefersReducedMotion ? {
      scale: [1, 1.5, 1],
      opacity: [1, 0.5, 1],
      transition: {
        duration: 1.5 / animationSpeed,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    } : {},
  };

  return (
    <motion.div
      className={`rounded-full ${className}`}
      style={{
        width: size,
        height: size,
        backgroundColor: color,
        ...style,
      }}
      variants={pulseVariants}
      animate="animate"
    />
  );
};

export default StatusIndicator;
