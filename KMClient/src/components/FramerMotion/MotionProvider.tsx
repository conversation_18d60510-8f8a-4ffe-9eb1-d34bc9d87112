/**
 * MotionProvider - Framer Motion 全局配置提供者
 * 提供動畫偏好設置和全局動畫配置
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { MotionConfig } from 'framer-motion';

interface MotionContextType {
  prefersReducedMotion: boolean;
  animationSpeed: number;
  setPrefersReducedMotion: (value: boolean) => void;
  setAnimationSpeed: (value: number) => void;
}

const MotionContext = createContext<MotionContextType | undefined>(undefined);

export const useMotionPreferences = () => {
  const context = useContext(MotionContext);
  if (!context) {
    throw new Error('useMotionPreferences must be used within a MotionProvider');
  }
  return context;
};

interface MotionProviderProps {
  children: React.ReactNode;
}

export const MotionProvider: React.FC<MotionProviderProps> = ({ children }) => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  const [animationSpeed, setAnimationSpeed] = useState(1);

  // 檢測用戶的動畫偏好
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // 從 localStorage 讀取用戶設置
  useEffect(() => {
    const savedReducedMotion = localStorage.getItem('prefersReducedMotion');
    const savedAnimationSpeed = localStorage.getItem('animationSpeed');

    if (savedReducedMotion !== null) {
      setPrefersReducedMotion(JSON.parse(savedReducedMotion));
    }

    if (savedAnimationSpeed !== null) {
      setAnimationSpeed(parseFloat(savedAnimationSpeed));
    }
  }, []);

  // 保存設置到 localStorage
  useEffect(() => {
    localStorage.setItem('prefersReducedMotion', JSON.stringify(prefersReducedMotion));
  }, [prefersReducedMotion]);

  useEffect(() => {
    localStorage.setItem('animationSpeed', animationSpeed.toString());
  }, [animationSpeed]);

  const contextValue: MotionContextType = {
    prefersReducedMotion,
    animationSpeed,
    setPrefersReducedMotion,
    setAnimationSpeed,
  };

  return (
    <MotionContext.Provider value={contextValue}>
      <MotionConfig
        reducedMotion={prefersReducedMotion ? 'always' : 'never'}
        transition={{
          duration: prefersReducedMotion ? 0 : 0.3 / animationSpeed,
          ease: 'easeOut',
        }}
      >
        {children}
      </MotionConfig>
    </MotionContext.Provider>
  );
};

export default MotionProvider;
