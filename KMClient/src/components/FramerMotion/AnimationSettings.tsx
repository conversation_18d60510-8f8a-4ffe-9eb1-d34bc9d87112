/**
 * AnimationSettings - 動畫設置組件
 * 提供用戶控制動畫偏好的界面
 */

import React from 'react';
import { Card, Switch, Slider, Space, Typography, Divider, Tooltip } from 'antd';
import { SettingOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { useMotionPreferences } from './MotionProvider';
import { motion } from 'framer-motion';

const { Text } = Typography;

interface AnimationSettingsProps {
  className?: string;
  style?: React.CSSProperties;
}

const AnimationSettings: React.FC<AnimationSettingsProps> = ({
  className = '',
  style = {},
}) => {
  const {
    prefersReducedMotion,
    animationSpeed,
    setPrefersReducedMotion,
    setAnimationSpeed,
  } = useMotionPreferences();

  // 動畫速度標記
  const speedMarks = {
    0.5: '慢',
    1: '正常',
    1.5: '快',
    2: '很快',
  };

  return (
    <Card
      className={className}
      style={style}
      title={
        <Space>
          <SettingOutlined />
          <span>動畫設置</span>
        </Space>
      }
      size="small"
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 減少動畫設置 */}
        <div>
          <Space align="start">
            <div style={{ flex: 1 }}>
              <Space>
                <Text strong>減少動畫</Text>
                <Tooltip title="開啟後將減少或禁用所有動畫效果，適合對動畫敏感的用戶">
                  <InfoCircleOutlined style={{ color: 'var(--color-text-tertiary)' }} />
                </Tooltip>
              </Space>
              <div style={{ marginTop: 4 }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {prefersReducedMotion 
                    ? '已禁用動畫效果，提供更穩定的視覺體驗' 
                    : '動畫效果已啟用，提供豐富的視覺反饋'
                  }
                </Text>
              </div>
            </div>
            <Switch
              checked={prefersReducedMotion}
              onChange={setPrefersReducedMotion}
              checkedChildren="開"
              unCheckedChildren="關"
            />
          </Space>
        </div>

        <Divider style={{ margin: '8px 0' }} />

        {/* 動畫速度設置 */}
        <div>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Space>
              <Text strong>動畫速度</Text>
              <Tooltip title="調整動畫播放速度，數值越大動畫越快">
                <InfoCircleOutlined style={{ color: 'var(--color-text-tertiary)' }} />
              </Tooltip>
            </Space>
            
            <div style={{ padding: '0 8px' }}>
              <Slider
                min={0.5}
                max={2}
                step={0.1}
                value={animationSpeed}
                onChange={setAnimationSpeed}
                marks={speedMarks}
                disabled={prefersReducedMotion}
                tooltip={{
                  formatter: (value) => `${value}x`,
                }}
              />
            </div>
            
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {prefersReducedMotion 
                ? '動畫已禁用，速度設置無效' 
                : `當前速度: ${animationSpeed}x`
              }
            </Text>
          </Space>
        </div>

        <Divider style={{ margin: '8px 0' }} />

        {/* 動畫預覽 */}
        <div>
          <Text strong>動畫預覽</Text>
          <div style={{ marginTop: 8, display: 'flex', justifyContent: 'center' }}>
            <motion.div
              style={{
                width: 40,
                height: 40,
                backgroundColor: 'var(--color-neon-blue)',
                borderRadius: 8,
              }}
              animate={
                prefersReducedMotion
                  ? {}
                  : {
                      scale: [1, 1.2, 1],
                      rotate: [0, 180, 360],
                    }
              }
              transition={
                prefersReducedMotion
                  ? {}
                  : {
                      duration: 2 / animationSpeed,
                      repeat: Infinity,
                      ease: 'easeInOut',
                    }
              }
            />
          </div>
          <Text type="secondary" style={{ fontSize: '12px', textAlign: 'center', display: 'block', marginTop: 8 }}>
            {prefersReducedMotion ? '動畫已禁用' : '動畫效果預覽'}
          </Text>
        </div>

        {/* 性能提示 */}
        <div style={{ 
          padding: 12, 
          backgroundColor: 'var(--color-bg-tertiary)', 
          borderRadius: 6,
          border: '1px solid var(--color-border-secondary)'
        }}>
          <Space direction="vertical" size="small">
            <Text strong style={{ fontSize: '12px' }}>
              💡 性能提示
            </Text>
            <Text style={{ fontSize: '11px', color: 'var(--color-text-tertiary)' }}>
              • 動畫使用 GPU 加速的 transform 和 opacity 屬性
            </Text>
            <Text style={{ fontSize: '11px', color: 'var(--color-text-tertiary)' }}>
              • 自動檢測系統的動畫偏好設置
            </Text>
            <Text style={{ fontSize: '11px', color: 'var(--color-text-tertiary)' }}>
              • 在低性能設備上建議開啟"減少動畫"
            </Text>
          </Space>
        </div>
      </Space>
    </Card>
  );
};

// 簡化版動畫設置組件
export const SimpleAnimationSettings: React.FC = () => {
  const { prefersReducedMotion, setPrefersReducedMotion } = useMotionPreferences();

  return (
    <Space>
      <Text style={{ fontSize: '12px' }}>動畫效果:</Text>
      <Switch
        size="small"
        checked={!prefersReducedMotion}
        onChange={(checked) => setPrefersReducedMotion(!checked)}
        checkedChildren="開"
        unCheckedChildren="關"
      />
    </Space>
  );
};

export default AnimationSettings;
