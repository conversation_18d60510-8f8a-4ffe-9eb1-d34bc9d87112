/**
 * LoadingAnimations - 加載動畫組件
 * 提供各種加載狀態的動畫指示器
 */

import React from 'react';
import { motion, Variants } from 'framer-motion';
import { useMotionPreferences } from './MotionProvider';

export type LoadingType = 
  | 'spinner' 
  | 'dots' 
  | 'pulse' 
  | 'skeleton' 
  | 'progress'
  | 'bounce'
  | 'wave';

interface LoadingAnimationProps {
  type?: LoadingType;
  size?: 'small' | 'medium' | 'large';
  color?: string;
  className?: string;
  style?: React.CSSProperties;
}

const LoadingAnimation: React.FC<LoadingAnimationProps> = ({
  type = 'spinner',
  size = 'medium',
  color = 'var(--color-neon-blue)',
  className = '',
  style = {},
}) => {
  const { prefersReducedMotion, animationSpeed } = useMotionPreferences();

  // 尺寸配置
  const getSizeConfig = () => {
    switch (size) {
      case 'small':
        return { width: 16, height: 16, fontSize: '12px' };
      case 'large':
        return { width: 48, height: 48, fontSize: '18px' };
      default:
        return { width: 24, height: 24, fontSize: '14px' };
    }
  };

  const sizeConfig = getSizeConfig();

  // 旋轉動畫
  const spinnerVariants: Variants = {
    animate: {
      rotate: prefersReducedMotion ? 0 : 360,
      transition: {
        duration: prefersReducedMotion ? 0 : 1 / animationSpeed,
        repeat: prefersReducedMotion ? 0 : Infinity,
        ease: 'linear',
      },
    },
  };

  // 點動畫
  const dotsVariants: Variants = {
    animate: {
      scale: prefersReducedMotion ? 1 : [1, 1.2, 1],
      opacity: prefersReducedMotion ? 1 : [0.5, 1, 0.5],
      transition: {
        duration: prefersReducedMotion ? 0 : 0.6 / animationSpeed,
        repeat: prefersReducedMotion ? 0 : Infinity,
        ease: 'easeInOut',
      },
    },
  };

  // 脈衝動畫
  const pulseVariants: Variants = {
    animate: {
      scale: prefersReducedMotion ? 1 : [1, 1.1, 1],
      opacity: prefersReducedMotion ? 1 : [0.7, 1, 0.7],
      transition: {
        duration: prefersReducedMotion ? 0 : 1 / animationSpeed,
        repeat: prefersReducedMotion ? 0 : Infinity,
        ease: 'easeInOut',
      },
    },
  };

  // 彈跳動畫
  const bounceVariants: Variants = {
    animate: {
      y: prefersReducedMotion ? 0 : [0, -10, 0],
      transition: {
        duration: prefersReducedMotion ? 0 : 0.6 / animationSpeed,
        repeat: prefersReducedMotion ? 0 : Infinity,
        ease: 'easeInOut',
      },
    },
  };

  // 波浪動畫
  const waveVariants: Variants = {
    animate: {
      scaleY: prefersReducedMotion ? 1 : [1, 2, 1],
      transition: {
        duration: prefersReducedMotion ? 0 : 0.8 / animationSpeed,
        repeat: prefersReducedMotion ? 0 : Infinity,
        ease: 'easeInOut',
      },
    },
  };

  const renderLoading = () => {
    switch (type) {
      case 'spinner':
        return (
          <motion.div
            className={`inline-block border-2 border-current border-t-transparent rounded-full ${className}`}
            style={{
              ...sizeConfig,
              borderColor: `${color} transparent ${color} ${color}`,
              ...style,
            }}
            variants={spinnerVariants}
            animate="animate"
          />
        );

      case 'dots':
        return (
          <div className={`flex space-x-1 ${className}`} style={style}>
            {[0, 1, 2].map((index) => (
              <motion.div
                key={index}
                className="rounded-full"
                style={{
                  width: sizeConfig.width / 3,
                  height: sizeConfig.height / 3,
                  backgroundColor: color,
                }}
                variants={dotsVariants}
                animate="animate"
                transition={{
                  delay: prefersReducedMotion ? 0 : (index * 0.2) / animationSpeed,
                }}
              />
            ))}
          </div>
        );

      case 'pulse':
        return (
          <motion.div
            className={`rounded-full ${className}`}
            style={{
              ...sizeConfig,
              backgroundColor: color,
              ...style,
            }}
            variants={pulseVariants}
            animate="animate"
          />
        );

      case 'bounce':
        return (
          <div className={`flex space-x-1 ${className}`} style={style}>
            {[0, 1, 2].map((index) => (
              <motion.div
                key={index}
                className="rounded-full"
                style={{
                  width: sizeConfig.width / 4,
                  height: sizeConfig.height / 4,
                  backgroundColor: color,
                }}
                variants={bounceVariants}
                animate="animate"
                transition={{
                  delay: prefersReducedMotion ? 0 : (index * 0.1) / animationSpeed,
                }}
              />
            ))}
          </div>
        );

      case 'wave':
        return (
          <div className={`flex items-end space-x-1 ${className}`} style={style}>
            {[0, 1, 2, 3, 4].map((index) => (
              <motion.div
                key={index}
                style={{
                  width: sizeConfig.width / 8,
                  height: sizeConfig.height / 2,
                  backgroundColor: color,
                }}
                variants={waveVariants}
                animate="animate"
                transition={{
                  delay: prefersReducedMotion ? 0 : (index * 0.1) / animationSpeed,
                }}
              />
            ))}
          </div>
        );

      case 'skeleton':
        return (
          <motion.div
            className={`bg-gray-300 rounded ${className}`}
            style={{
              ...sizeConfig,
              ...style,
            }}
            animate={
              prefersReducedMotion
                ? {}
                : {
                    opacity: [0.5, 1, 0.5],
                  }
            }
            transition={{
              duration: prefersReducedMotion ? 0 : 1.5 / animationSpeed,
              repeat: prefersReducedMotion ? 0 : Infinity,
              ease: 'easeInOut',
            }}
          />
        );

      case 'progress':
        return (
          <div
            className={`bg-gray-200 rounded-full overflow-hidden ${className}`}
            style={{
              width: sizeConfig.width * 3,
              height: sizeConfig.height / 3,
              ...style,
            }}
          >
            <motion.div
              className="h-full rounded-full"
              style={{
                backgroundColor: color,
                width: '50%',
              }}
              animate={
                prefersReducedMotion
                  ? { width: '100%' }
                  : {
                      x: ['-100%', '100%'],
                    }
              }
              transition={{
                duration: prefersReducedMotion ? 0 : 1.5 / animationSpeed,
                repeat: prefersReducedMotion ? 0 : Infinity,
                ease: 'easeInOut',
              }}
            />
          </div>
        );

      default:
        return null;
    }
  };

  return renderLoading();
};

// 骨架屏組件
interface SkeletonProps {
  width?: string | number;
  height?: string | number;
  className?: string;
  style?: React.CSSProperties;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = '1rem',
  className = '',
  style = {},
}) => {
  return (
    <LoadingAnimation
      type="skeleton"
      className={className}
      style={{
        width,
        height,
        ...style,
      }}
    />
  );
};

// 文字骨架屏
export const TextSkeleton: React.FC<{ lines?: number; className?: string }> = ({
  lines = 3,
  className = '',
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          height="1rem"
          width={index === lines - 1 ? '60%' : '100%'}
        />
      ))}
    </div>
  );
};

export default LoadingAnimation;
