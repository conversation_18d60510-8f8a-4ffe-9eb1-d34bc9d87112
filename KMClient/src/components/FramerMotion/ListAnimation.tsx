/**
 * ListAnimation - 列表動畫組件
 * 提供列表項目的進入/退出動畫和 stagger 效果
 */

import React from 'react';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import { useMotionPreferences } from './MotionProvider';

export type ListAnimationType = 
  | 'fadeIn' 
  | 'slideUp' 
  | 'slideLeft' 
  | 'scale' 
  | 'bounce'
  | 'flip';

interface ListAnimationProps {
  children: React.ReactNode[];
  type?: ListAnimationType;
  stagger?: number;
  duration?: number;
  className?: string;
  itemClassName?: string;
  style?: React.CSSProperties;
}

const ListAnimation: React.FC<ListAnimationProps> = ({
  children,
  type = 'fadeIn',
  stagger = 0.1,
  duration = 0.3,
  className = '',
  itemClassName = '',
  style = {},
}) => {
  const { prefersReducedMotion, animationSpeed } = useMotionPreferences();

  // 容器動畫變體
  const containerVariants: Variants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: prefersReducedMotion ? 0 : stagger / animationSpeed,
      },
    },
  };

  // 項目動畫變體
  const getItemVariants = (): Variants => {
    const adjustedDuration = prefersReducedMotion ? 0 : duration / animationSpeed;

    switch (type) {
      case 'fadeIn':
        return {
          hidden: { opacity: 0 },
          visible: { 
            opacity: 1,
            transition: { duration: adjustedDuration, ease: 'easeOut' }
          },
          exit: { 
            opacity: 0,
            transition: { duration: adjustedDuration / 2 }
          },
        };

      case 'slideUp':
        return {
          hidden: { opacity: 0, y: 20 },
          visible: { 
            opacity: 1, 
            y: 0,
            transition: { duration: adjustedDuration, ease: 'easeOut' }
          },
          exit: { 
            opacity: 0, 
            y: -20,
            transition: { duration: adjustedDuration / 2 }
          },
        };

      case 'slideLeft':
        return {
          hidden: { opacity: 0, x: 20 },
          visible: { 
            opacity: 1, 
            x: 0,
            transition: { duration: adjustedDuration, ease: 'easeOut' }
          },
          exit: { 
            opacity: 0, 
            x: -20,
            transition: { duration: adjustedDuration / 2 }
          },
        };

      case 'scale':
        return {
          hidden: { opacity: 0, scale: 0.8 },
          visible: { 
            opacity: 1, 
            scale: 1,
            transition: { duration: adjustedDuration, ease: 'easeOut' }
          },
          exit: { 
            opacity: 0, 
            scale: 0.8,
            transition: { duration: adjustedDuration / 2 }
          },
        };

      case 'bounce':
        return {
          hidden: { opacity: 0, scale: 0.3 },
          visible: { 
            opacity: 1, 
            scale: 1,
            transition: { 
              duration: adjustedDuration,
              type: 'spring',
              stiffness: 400,
              damping: 10
            }
          },
          exit: { 
            opacity: 0, 
            scale: 0.3,
            transition: { duration: adjustedDuration / 2 }
          },
        };

      case 'flip':
        return {
          hidden: { opacity: 0, rotateX: -90 },
          visible: { 
            opacity: 1, 
            rotateX: 0,
            transition: { duration: adjustedDuration, ease: 'easeOut' }
          },
          exit: { 
            opacity: 0, 
            rotateX: 90,
            transition: { duration: adjustedDuration / 2 }
          },
        };

      default:
        return {
          hidden: { opacity: 0 },
          visible: { opacity: 1 },
          exit: { opacity: 0 },
        };
    }
  };

  const itemVariants = getItemVariants();

  return (
    <motion.div
      className={className}
      style={style}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <AnimatePresence>
        {children.map((child, index) => (
          <motion.div
            key={index}
            className={itemClassName}
            variants={itemVariants}
            layout
          >
            {child}
          </motion.div>
        ))}
      </AnimatePresence>
    </motion.div>
  );
};

// 單個列表項目動畫組件
interface ListItemAnimationProps {
  children: React.ReactNode;
  type?: ListAnimationType;
  duration?: number;
  delay?: number;
  className?: string;
  style?: React.CSSProperties;
}

export const ListItemAnimation: React.FC<ListItemAnimationProps> = ({
  children,
  type = 'fadeIn',
  duration = 0.3,
  delay = 0,
  className = '',
  style = {},
}) => {
  const { prefersReducedMotion, animationSpeed } = useMotionPreferences();

  const getVariants = (): Variants => {
    const adjustedDuration = prefersReducedMotion ? 0 : duration / animationSpeed;
    const adjustedDelay = prefersReducedMotion ? 0 : delay / animationSpeed;

    switch (type) {
      case 'fadeIn':
        return {
          hidden: { opacity: 0 },
          visible: { 
            opacity: 1,
            transition: { 
              duration: adjustedDuration, 
              delay: adjustedDelay,
              ease: 'easeOut' 
            }
          },
        };

      case 'slideUp':
        return {
          hidden: { opacity: 0, y: 20 },
          visible: { 
            opacity: 1, 
            y: 0,
            transition: { 
              duration: adjustedDuration, 
              delay: adjustedDelay,
              ease: 'easeOut' 
            }
          },
        };

      case 'scale':
        return {
          hidden: { opacity: 0, scale: 0.8 },
          visible: { 
            opacity: 1, 
            scale: 1,
            transition: { 
              duration: adjustedDuration, 
              delay: adjustedDelay,
              ease: 'easeOut' 
            }
          },
        };

      default:
        return {
          hidden: { opacity: 0 },
          visible: { opacity: 1 },
        };
    }
  };

  return (
    <motion.div
      className={className}
      style={style}
      variants={getVariants()}
      initial="hidden"
      animate="visible"
    >
      {children}
    </motion.div>
  );
};

export default ListAnimation;
