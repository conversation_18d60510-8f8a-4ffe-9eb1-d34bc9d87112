import React, { useState } from 'react';
import { Card, Input, Button, Typography, Divider, Space, Alert } from 'antd';
import { MessageParser } from '@/utils/messageParser';
import MessageRenderer from '@/components/ChatPanel/MessageRenderer';

const { Title, Text } = Typography;
const { TextArea } = Input;

/**
 * AI 回應調試器組件
 * 用於測試和調試 AI API 回應的解析和渲染
 */
const AIResponseDebugger: React.FC = () => {
  const [rawResponse, setRawResponse] = useState('');
  const [parsedResult, setParsedResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // 預設的測試數據（基於用戶提供的 AI 回應）
  const sampleResponse = `{
  "quickReply": {
    "items": [
      {
        "action": {
          "data": "圖片中的人物是誰？",
          "displayText": "圖片中的人物是誰？",
          "title": "圖片中的人物是誰？",
          "type": "Postback"
        },
        "type": "Action"
      },
      {
        "action": {
          "data": "他們在做什麼？",
          "displayText": "他們在做什麼？",
          "title": "他們在做什麼？",
          "type": "Postback"
        },
        "type": "Action"
      },
      {
        "action": {
          "data": "這個場景是什麼活動？",
          "displayText": "這個場景是什麼活動？",
          "title": "這個場景是什麼活動？",
          "type": "Postback"
        },
        "type": "Action"
      }
    ]
  },
  "text": "這張圖片顯示了五個人坐成一排，似乎在一個室內現場。左邊第一位女士穿著色上衣，她旁邊是一位男士穿著色高領毛衣和黑色外套有片的黑色外套，接著是一位穿著色上衣的女士，再往右是穿著藍色毛衣的女士，最右邊的男士穿著朝前方，表情平靜或略帶常帶微笑。整體氛圍看起來是在觀賞表演或聆聽典禮，請問您想了解關於這張圖片的哪些方面？",
  "type": "Text"
}`;

  const handleParse = () => {
    try {
      setError(null);
      
      if (!rawResponse.trim()) {
        setError('請輸入 AI 回應數據');
        return;
      }

      // 解析 AI 回應
      const parsed = MessageParser.parseMessage(rawResponse);
      setParsedResult(parsed);
      
      console.log('🔍 調試解析結果:', parsed);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '解析失敗';
      setError(errorMessage);
      setParsedResult(null);
    }
  };

  const handleLoadSample = () => {
    setRawResponse(sampleResponse);
    setError(null);
    setParsedResult(null);
  };

  const handleClear = () => {
    setRawResponse('');
    setParsedResult(null);
    setError(null);
  };

  // 創建模擬的 ChatMessage 用於渲染測試（如果需要的話）
  // const createMockMessage = (): ChatMessage | null => {
  //   if (!parsedResult) return null;
  //
  //   return {
  //     id: 'debug-message',
  //     type: 'assistant',
  //     content: rawResponse,
  //     parsedContent: parsedResult,
  //     timestamp: new Date().toISOString()
  //   };
  // };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <Title level={2}>🔍 AI 回應調試器</Title>
      <Text type="secondary">
        用於測試和調試 AI API 回應的解析和渲染效果
      </Text>
      
      <Divider />
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 輸入區域 */}
        <Card title="📥 AI 回應數據輸入" className="h-fit">
          <Space direction="vertical" className="w-full">
            <div className="flex gap-2 mb-4">
              <Button onClick={handleLoadSample} type="default">
                載入範例數據
              </Button>
              <Button onClick={handleClear} type="default">
                清空
              </Button>
              <Button onClick={handleParse} type="primary">
                解析數據
              </Button>
            </div>
            
            <TextArea
              value={rawResponse}
              onChange={(e) => setRawResponse(e.target.value)}
              placeholder="請貼上 AI API 回應的 JSON 數據..."
              rows={20}
              className="font-mono text-sm"
            />
            
            {error && (
              <Alert
                message="解析錯誤"
                description={error}
                type="error"
                showIcon
              />
            )}
          </Space>
        </Card>

        {/* 解析結果區域 */}
        <div className="space-y-6">
          {/* 解析結果 */}
          {parsedResult && (
            <Card title="📊 解析結果" className="h-fit">
              <div className="space-y-4">
                <div>
                  <Text strong>內容數量: </Text>
                  <Text>{parsedResult.contents?.length || 0}</Text>
                </div>
                
                <div>
                  <Text strong>是否有快速回復: </Text>
                  <Text>{parsedResult.hasQuickReply ? '是' : '否'}</Text>
                </div>
                
                <div>
                  <Text strong>快速回復項目數: </Text>
                  <Text>{parsedResult.quickReplyItems?.length || 0}</Text>
                </div>
                
                <Divider />
                
                <div>
                  <Text strong>詳細結構:</Text>
                  <pre className="mt-2 p-3 bg-gray-50 rounded text-xs overflow-auto max-h-60">
                    {JSON.stringify(parsedResult, null, 2)}
                  </pre>
                </div>
              </div>
            </Card>
          )}

          {/* 渲染效果預覽 */}
          {parsedResult && (
            <Card title="🎨 渲染效果預覽" className="h-fit">
              <div className="border rounded p-4 bg-gray-50">
                <MessageRenderer
                  parsedContent={parsedResult}
                  isUser={false}
                />
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIResponseDebugger;
