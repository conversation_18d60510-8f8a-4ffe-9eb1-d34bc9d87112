/**
 * 頭像設置面板組件
 * 提供頭像選擇、預覽和上傳功能
 */

import React, { useState } from 'react';
import {
  Upload,
  Button,
  Avatar,
  Typography,
  Space,
  Progress,
  Alert
} from 'antd';
import type { UploadProps } from 'antd';
import { 
  UserOutlined, 
  UploadOutlined, 
  LoadingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useAvatarService } from '@/hooks/useAvatarService';
import { useAppStore } from '@/hooks/useAppStore';
import { AvatarUploadStatus, AvatarSettingsPanelProps } from '@/types/avatar';
import { AnimatedText } from '@/components/ReactBits';

const { Text } = Typography;

/**
 * 頭像設置面板組件
 */
const AvatarSettingsPanel: React.FC<AvatarSettingsPanelProps> = ({
  className = '',
  onAvatarUpdate,
  onError
}) => {
  const { userInfo } = useAppStore();
  const {
    uploadState,
    currentAvatarUrl,
    isUploading,
    canUpload,
    updateAvatar,
    validateFile,
    resetUploadState,
    allowedFormats,
    maxFileSize
  } = useAvatarService();

  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  /**
   * 處理檔案選擇前的驗證
   */
  const beforeUpload: UploadProps['beforeUpload'] = (file) => {
    console.log('📁 選擇檔案:', file);

    // 驗證檔案
    const validation = validateFile(file);
    if (!validation.isValid) {
      console.error('❌ 檔案驗證失敗:', validation.error);
      onError?.(validation.error || '檔案驗證失敗');
      return false;
    }

    // 設置選中的檔案
    setSelectedFile(file);

    // 生成預覽 URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);

    // 重置上傳狀態
    resetUploadState();

    console.log('✅ 檔案驗證通過，已設置預覽');
    return false; // 阻止自動上傳
  };

  /**
   * 處理頭像更新
   */
  const handleUpdateAvatar = async () => {
    if (!selectedFile) {
      console.warn('⚠️ 沒有選擇檔案');
      return;
    }

    console.log('🎭 開始更新頭像');
    
    const success = await updateAvatar(selectedFile);
    
    if (success) {
      // 更新成功回調
      if (uploadState.avatarUrl) {
        onAvatarUpdate?.(uploadState.avatarUrl);
      }
      
      // 清理預覽
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
        setPreviewUrl('');
      }
      setSelectedFile(null);
    } else {
      // 更新失敗回調
      onError?.(uploadState.error || '頭像更新失敗');
    }
  };

  /**
   * 處理取消選擇
   */
  const handleCancel = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl('');
    }
    setSelectedFile(null);
    resetUploadState();
  };

  /**
   * 獲取狀態圖標
   */
  const getStatusIcon = () => {
    switch (uploadState.status) {
      case AvatarUploadStatus.UPLOADING:
        return <LoadingOutlined />;
      case AvatarUploadStatus.SUCCESS:
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case AvatarUploadStatus.ERROR:
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return null;
    }
  };

  /**
   * 獲取當前顯示的頭像 URL
   */
  const getDisplayAvatarUrl = () => {
    return previewUrl || currentAvatarUrl || '';
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 標題 */}
      <div className="flex items-center justify-between">
        <AnimatedText
          text="👤 頭像設置"
          animation="slideUp"
          duration={0.5}
          style={{ color: 'var(--color-neon-blue)' }}
        />
        {getStatusIcon()}
      </div>

      {/* 服務號ID 顯示 */}
      {userInfo.service_id && (
        <div className="text-xs p-2 rounded" style={{
          backgroundColor: 'var(--color-surface-secondary)',
          borderLeft: '3px solid var(--color-neon-blue)'
        }}>
          <Text type="secondary">
            服務號：<Text code style={{ color: 'var(--color-text-primary)' }}>
              {userInfo.service_id}
            </Text>
          </Text>
        </div>
      )}

      {/* 頭像預覽區域 */}
      <div className="flex items-center space-x-4">
        <Avatar
          size={80}
          src={getDisplayAvatarUrl()}
          icon={<UserOutlined />}
          style={{
            border: '2px solid var(--color-border-primary)',
            backgroundColor: 'var(--color-surface-secondary)'
          }}
        />
        
        <div className="flex-1 space-y-2">
          {/* 檔案選擇器 */}
          <Upload
            beforeUpload={beforeUpload}
            showUploadList={false}
            accept={allowedFormats.map((format: string) => `.${format}`).join(',')}
            disabled={!canUpload || isUploading}
          >
            <Button 
              icon={<UploadOutlined />}
              disabled={!canUpload || isUploading}
              style={{ 
                borderColor: 'var(--color-neon-blue)',
                color: 'var(--color-neon-blue)'
              }}
            >
              選擇頭像
            </Button>
          </Upload>

          {/* 操作按鈕 */}
          {selectedFile && (
            <Space>
              <Button
                type="primary"
                loading={isUploading}
                onClick={handleUpdateAvatar}
                disabled={!canUpload}
                style={{ background: 'var(--color-neon-blue)' }}
              >
                更新頭像
              </Button>
              <Button onClick={handleCancel} disabled={isUploading}>
                取消
              </Button>
            </Space>
          )}
        </div>
      </div>

      {/* 上傳進度 */}
      {isUploading && (
        <Progress 
          percent={uploadState.progress} 
          status="active"
          strokeColor="var(--color-neon-blue)"
        />
      )}

      {/* 錯誤提示 */}
      {uploadState.status === AvatarUploadStatus.ERROR && uploadState.error && (
        <Alert
          message="上傳失敗"
          description={uploadState.error}
          type="error"
          showIcon
          closable
          onClose={resetUploadState}
        />
      )}

      {/* 成功提示 */}
      {uploadState.status === AvatarUploadStatus.SUCCESS && (
        <Alert
          message="頭像更新成功"
          type="success"
          showIcon
          closable
          onClose={resetUploadState}
        />
      )}

      {/* 格式提示 */}
      <div className="text-xs" style={{ color: 'var(--color-text-tertiary)' }}>
        <Text type="secondary">
          支援格式：{allowedFormats.join(', ').toUpperCase()} | 
          最大大小：{maxFileSize}
        </Text>
      </div>

      {/* 選中檔案信息 */}
      {selectedFile && (
        <div className="text-xs" style={{ color: 'var(--color-text-secondary)' }}>
          <Text>
            已選擇：{selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
          </Text>
        </div>
      )}
    </div>
  );
};

export default AvatarSettingsPanel;
