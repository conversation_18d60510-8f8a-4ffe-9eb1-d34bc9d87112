/**
 * 頭像設置面板組件測試
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import AvatarSettingsPanel from '../AvatarSettingsPanel';

// Mock hooks
vi.mock('@/hooks/useAvatarService', () => ({
  useAvatarService: () => ({
    uploadState: {
      status: 'idle',
      progress: 0
    },
    currentAvatarUrl: '',
    isUploading: false,
    canUpload: true,
    updateAvatar: vi.fn(),
    validateFile: vi.fn(() => ({ isValid: true })),
    resetUploadState: vi.fn(),
    allowedFormats: ['png', 'jpg', 'jpeg', 'gif'],
    maxFileSize: '5MB'
  })
}));

vi.mock('@/hooks/useAppStore', () => ({
  useAppStore: () => ({
    userInfo: {
      tenant_id: 'test-tenant',
      service_id: 'test-service-123',
      user_id: 'test-user'
    }
  })
}));

vi.mock('@/components/ReactBits', () => ({
  AnimatedText: ({ text, children, ...props }: any) => (
    <div data-testid="animated-text" {...props}>
      {text || children}
    </div>
  )
}));

describe('AvatarSettingsPanel', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('應該正確渲染頭像設置面板', () => {
    render(<AvatarSettingsPanel />);
    
    // 檢查標題
    expect(screen.getByTestId('animated-text')).toHaveTextContent('👤 頭像設置');
    
    // 檢查選擇按鈕
    expect(screen.getByText('選擇頭像')).toBeInTheDocument();
    
    // 檢查格式提示
    expect(screen.getByText(/支援格式：PNG, JPG, JPEG, GIF/)).toBeInTheDocument();
    expect(screen.getByText(/最大大小：5MB/)).toBeInTheDocument();
  });

  it('應該顯示頭像預覽', () => {
    render(<AvatarSettingsPanel />);
    
    // 檢查頭像組件存在
    const avatar = document.querySelector('.ant-avatar');
    expect(avatar).toBeInTheDocument();
  });

  it('應該處理回調函數', () => {
    const onAvatarUpdate = vi.fn();
    const onError = vi.fn();
    
    render(
      <AvatarSettingsPanel 
        onAvatarUpdate={onAvatarUpdate}
        onError={onError}
      />
    );
    
    // 組件應該正常渲染
    expect(screen.getByText('選擇頭像')).toBeInTheDocument();
  });

  it('應該顯示格式提示信息', () => {
    render(<AvatarSettingsPanel />);

    const formatText = screen.getByText(/支援格式：PNG, JPG, JPEG, GIF/);
    expect(formatText).toBeInTheDocument();

    const sizeText = screen.getByText(/最大大小：5MB/);
    expect(sizeText).toBeInTheDocument();
  });

  it('應該顯示服務號ID', () => {
    render(<AvatarSettingsPanel />);

    const serviceIdText = screen.getByText(/服務號：/);
    expect(serviceIdText).toBeInTheDocument();

    const serviceIdValue = screen.getByText('test-service-123');
    expect(serviceIdValue).toBeInTheDocument();
  });
});
