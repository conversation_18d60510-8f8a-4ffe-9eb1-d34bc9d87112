/**
 * 主要佈局組件 - 左右分欄設計
 */

import React from 'react';
import { Layout, Button, Typography, Space, Tooltip } from 'antd';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import { MdLightMode, MdDarkMode } from 'react-icons/md';
import { useAppStore } from '@/hooks/useAppStore';
import AttachmentPanel from '@/features/attachments/AttachmentPanel';
import ChatPanel from '@/features/chat/ChatPanel';
import AvatarSettingsPanel from '@/components/AvatarSettings/AvatarSettingsPanel';
import { AnimatedBackground, AnimatedText } from '@/ui/animations/react-bits';
import { SimpleAnimationSettings } from '@/ui/animations/framer-motion';
import { useConfig } from '@/hooks/useConfig';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

interface MainLayoutProps {
  children?: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const { sidebarCollapsed, toggleSidebar, isDarkMode, toggleDarkMode } = useAppStore();
  const { config } = useConfig();
  const appName = config?.app?.name || 'KM Client';

  return (
    <Layout className="min-h-screen">
      {/* 左側服務管理面板 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={sidebarCollapsed}
        width={400}
        collapsedWidth={0}
        className="transition-all duration-300"
        style={{
          background: 'var(--color-surface-primary)',
          borderRight: '1px solid var(--color-border-primary)',
          boxShadow: 'var(--shadow-lg)',
          transition: 'all 0.3s ease'
        }}
        breakpoint="lg"
        onBreakpoint={(broken) => {
          // 在小屏幕上自動收起側邊欄
          if (broken && !sidebarCollapsed) {
            toggleSidebar();
          }
        }}
      >
        <div className="h-full flex flex-col">
          {/* 服務管理面板標題 */}
          <div
            className="p-4 border-b transition-colors duration-300"
            style={{
              borderBottom: '1px solid var(--color-border-primary)',
              background: 'var(--color-surface-secondary)'
            }}
          >
            <Title level={4} className="!mb-0" style={{ color: 'var(--color-neon-blue)' }}>
              ⚙️ 服務管理
            </Title>
            <div className="text-xs mt-1" style={{ color: 'var(--color-text-tertiary)' }}>
              管理服務號的頭像設置和知識內容
            </div>
          </div>

          {/* 頭像設置區塊 */}
          <div
            className="p-4 border-b transition-colors duration-300"
            style={{
              borderBottom: '1px solid var(--color-border-primary)',
              background: 'var(--color-surface-primary)'
            }}
          >
            <AvatarSettingsPanel
              onAvatarUpdate={(url) => console.log('✅ 頭像更新成功:', url)}
              onError={(error) => console.error('❌ 頭像更新錯誤:', error)}
            />
          </div>

          {/* 知識內容管理組件 */}
          <div className="flex-1 overflow-hidden">
            <AttachmentPanel />
          </div>
        </div>
      </Sider>

      {/* 主內容區域 */}
      <Layout>
        {/* 頂部導航欄 */}
        <Header
          className="px-4 flex items-center justify-between transition-colors duration-300"
          style={{
            background: 'var(--color-surface-primary)',
            borderBottom: '1px solid var(--color-border-primary)',
            height: '64px'
          }}
        >
          <Space align="center">
            {/* 側邊欄切換按鈕 */}
            <Button
              type="text"
              icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={toggleSidebar}
              className="hover:bg-opacity-10 hover:bg-gray-500 transition-colors duration-200"
              style={{
                color: 'var(--color-text-secondary)',
                fontSize: '16px'
              }}
            />

            {/* 應用標題 */}
            <AnimatedText
              text={`🧠 ${appName}`}
              animation="glow"
              duration={1}
              as="h3"
              className="!mb-0 text-xl font-bold"
              style={{ color: 'var(--color-neon-blue)' }}
            />
          </Space>

          {/* 右側操作按鈕 */}
          <Space>
            <SimpleAnimationSettings />
            <Tooltip title={isDarkMode ? '切換到淺色模式' : '切換到深色模式'}>
              <Button
                type="text"
                icon={isDarkMode ? <MdLightMode /> : <MdDarkMode />}
                onClick={toggleDarkMode}
                className="hover:bg-opacity-10 hover:bg-gray-500 transition-colors duration-200"
                style={{ color: 'var(--color-text-secondary)' }}
              />
            </Tooltip>
          </Space>
        </Header>

        {/* 主內容區域 - 聊天面板 */}
        <Content
          className="transition-colors duration-300 relative"
          style={{
            background: 'var(--color-bg-secondary)',
            minHeight: 'calc(100vh - 64px)'
          }}
        >
          <AnimatedBackground
            type="particles"
            opacity={0.05}
            speed={0.5}
            density={30}
          >
            {children || <ChatPanel />}
          </AnimatedBackground>
        </Content>
      </Layout>

    </Layout>
  );
};

export default MainLayout;
