@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* CSS 變量定義 */
:root {
  /* 間距變量 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* 科技感配色 - 保持一致 */
  --color-neon-blue: #3b82f6;
  --color-neon-purple: #8b5cf6;
  --color-neon-pink: #ec4899;
  --color-neon-green: #10b981;
  --color-neon-orange: #f59e0b;

  /* 深色模式專用紫色系 */
  --color-neon-purple-light: #a78bfa;
  --color-neon-purple-bright: #c084fc;
}

/* 淺色模式變量 (默認) */
:root {
  /* 背景色 */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8fafc;
  --color-bg-tertiary: #f1f5f9;
  --color-bg-elevated: #ffffff;

  /* 邊框色 */
  --color-border-primary: #e2e8f0;
  --color-border-secondary: #cbd5e1;
  --color-border-tertiary: #94a3b8;

  /* 文字色 - 淺色模式高對比度 */
  --color-text-primary: #0f172a;    /* 更深的主要文字色 */
  --color-text-secondary: #334155;  /* 更深的次要文字色 */
  --color-text-tertiary: #475569;   /* 更深的第三級文字色 */
  --color-text-quaternary: #64748b; /* 保持原有的第四級文字色 */

  /* 表面色 */
  --color-surface-primary: #ffffff;
  --color-surface-secondary: #f7fafc;
  --color-surface-tertiary: #edf2f7;

  /* 陰影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 兼容舊變量 */
  --color-dark-50: #f8fafc;
  --color-dark-100: #f1f5f9;
  --color-dark-200: #e2e8f0;
  --color-dark-300: #cbd5e1;
  --color-dark-400: #94a3b8;
  --color-dark-500: #64748b;
  --color-dark-600: #4a5568;
  --color-dark-700: #2d3748;
  --color-dark-800: #1a202c;
  --color-dark-900: #171923;
  --color-light-50: #ffffff;
  --color-light-100: #f7fafc;
  --color-light-200: #edf2f7;
  --color-light-300: #e2e8f0;
  --color-light-400: #cbd5e1;
}

/* 深色模式變量 */
.dark {
  /* 背景色 */
  --color-bg-primary: #0f172a;
  --color-bg-secondary: #1e293b;
  --color-bg-tertiary: #334155;
  --color-bg-elevated: #1e293b;

  /* 邊框色 */
  --color-border-primary: #334155;
  --color-border-secondary: #475569;
  --color-border-tertiary: #64748b;

  /* 文字色 - 深色模式高對比度 */
  --color-text-primary: #ffffff;    /* 純白色主要文字 */
  --color-text-secondary: #f1f5f9;  /* 非常淺的次要文字色 */
  --color-text-tertiary: #e2e8f0;   /* 淺灰色第三級文字色 */
  --color-text-quaternary: #cbd5e1; /* 中等灰色第四級文字色 */

  /* 表面色 */
  --color-surface-primary: #1e293b;
  --color-surface-secondary: #334155;
  --color-surface-tertiary: #475569;

  /* 陰影 - 深色模式下使用更深的陰影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.4);

  /* 深色模式專用主題色 - 使用紫色系 */
  --color-neon-blue: #a78bfa; /* 在深色模式下使用紫色替代藍色 */

  /* 兼容舊變量 - 深色模式下的映射 */
  --color-dark-50: #0f172a;
  --color-dark-100: #1e293b;
  --color-dark-200: #334155;
  --color-dark-300: #475569;
  --color-dark-400: #64748b;
  --color-dark-500: #94a3b8;
  --color-dark-600: #cbd5e1;
  --color-dark-700: #e2e8f0;
  --color-dark-800: #f1f5f9;
  --color-dark-900: #f8fafc;
  --color-light-50: #1e293b;
  --color-light-100: #334155;
  --color-light-200: #475569;
  --color-light-300: #64748b;
  --color-light-400: #94a3b8;
}

/* 全局樣式重置 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--color-bg-primary);
  min-height: 100vh;
  color: var(--color-text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 載入動畫 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 212, 255, 0.2);
  border-top: 3px solid var(--color-neon-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 科技感效果 */
.glow-effect {
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
}

.glow-effect:hover {
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.5), 0 0 60px rgba(0, 212, 255, 0.2);
  transform: translateY(-2px);
}

.glow-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: inherit;
}

.glow-effect:hover::before {
  opacity: 1;
}

/* 霓虹邊框效果 */
.neon-border {
  border: 1px solid var(--color-neon-blue);
  box-shadow:
    0 0 5px var(--color-neon-blue),
    inset 0 0 5px rgba(0, 212, 255, 0.1);
  animation: neon-pulse 2s ease-in-out infinite alternate;
}

@keyframes neon-pulse {
  0% {
    box-shadow:
      0 0 5px var(--color-neon-blue),
      inset 0 0 5px rgba(0, 212, 255, 0.1);
  }
  100% {
    box-shadow:
      0 0 20px var(--color-neon-blue),
      0 0 30px rgba(0, 212, 255, 0.3),
      inset 0 0 10px rgba(0, 212, 255, 0.2);
  }
}

/* 漸變文字效果 */
.gradient-text {
  background: linear-gradient(45deg, var(--color-neon-blue), var(--color-neon-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 科技感背景 - 響應主題 */
.tech-bg {
  background:
    radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
    var(--color-bg-secondary);
  transition: background 0.3s ease;
}

/* 數據流動畫 */
.data-flow {
  position: relative;
  overflow: hidden;
}

.data-flow::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 212, 255, 0.2),
    transparent
  );
  animation: data-flow 3s infinite;
}

@keyframes data-flow {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 自定義滾動條 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-neon-blue);
  border-radius: 4px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  opacity: 1;
}

/* Ant Design 主題自定義 */
.ant-btn-primary {
  background: var(--color-neon-blue) !important;
  border-color: var(--color-neon-blue) !important;
  box-shadow: 0 2px 8px rgba(0, 212, 255, 0.3) !important;
}

.ant-btn-primary:hover {
  background: rgba(0, 212, 255, 0.8) !important;
  border-color: rgba(0, 212, 255, 0.8) !important;
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.4) !important;
}

/* 響應式設計 */
@media (max-width: 1200px) {
  .glow-effect {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.2);
  }

  .glow-effect:hover {
    box-shadow: 0 0 25px rgba(59, 130, 246, 0.4);
  }
}

/* 主佈局響應式優化 */
@media (max-width: 1024px) {
  /* 平板設備優化 */
  .ant-layout-sider {
    width: 350px !important;
  }
}

@media (max-width: 992px) {
  /* 小平板設備優化 */
  .ant-layout-sider {
    width: 300px !important;
  }
}

@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* 移動端佈局優化 */
  .ant-layout-header {
    padding: 0 16px !important;
  }

  .ant-layout-sider {
    width: 280px !important;
  }

  /* 聊天面板移動端優化 */
  .ant-card {
    margin: 8px 0;
  }

  .ant-typography h3 {
    font-size: 1.2rem !important;
  }

  .ant-typography h4 {
    font-size: 1.1rem !important;
  }

  /* 移動端優化 */
  .tech-bg {
    background: linear-gradient(135deg, var(--color-dark-800) 0%, var(--color-dark-700) 100%);
  }

  .card-hover:hover {
    transform: translateY(-4px) scale(1.01);
  }

  .glow-effect {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.2);
  }

  .glow-effect:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  /* 文字大小調整 */
  .gradient-text {
    font-size: 1.5rem;
  }

  /* 動畫性能優化 */
  .data-flow::after {
    animation-duration: 4s;
  }

  .btn-pulse {
    animation: none; /* 移動端禁用脈衝動畫以節省電池 */
  }
}

@media (max-width: 480px) {
  /* 小屏幕設備優化 */
  .neon-border {
    animation: none; /* 禁用霓虹動畫 */
    border: 1px solid var(--color-neon-blue);
    box-shadow: 0 0 5px var(--color-neon-blue);
  }

  .text-glow {
    animation: none;
    text-shadow: 0 0 5px var(--color-neon-blue);
  }

  /* 簡化動畫 */
  .bounce-in {
    animation: fadeIn 0.5s ease-out;
  }

  .scale-in {
    animation: fadeIn 0.4s ease-out;
  }

  .rotate-in {
    animation: fadeIn 0.3s ease-out;
  }
}

/* 高分辨率屏幕優化 */
@media (min-width: 1920px) {
  .glow-effect {
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.4);
  }

  .glow-effect:hover {
    box-shadow: 0 0 50px rgba(0, 212, 255, 0.6), 0 0 100px rgba(0, 212, 255, 0.3);
  }

  .neon-border {
    box-shadow:
      0 0 10px var(--color-neon-blue),
      0 0 20px rgba(0, 212, 255, 0.3),
      inset 0 0 10px rgba(0, 212, 255, 0.1);
  }
}

/* 深色模式優化 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-dark-900: #0a0a0a;
    --color-dark-800: #1a1a1a;
  }
}

/* 減少動畫偏好設置 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .glow-effect {
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
  }

  .neon-border {
    animation: none;
    box-shadow: 0 0 5px var(--color-neon-blue);
  }
}

/* 觸控設備優化 */
@media (hover: none) and (pointer: coarse) {
  .card-hover:hover {
    transform: none;
  }

  .glow-effect:hover {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  }

  /* 增大觸控目標 */
  button {
    min-height: 44px;
    min-width: 44px;
  }
}

/* 動畫效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes slideInFromRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes rotateIn {
  from { transform: rotate(-180deg) scale(0.5); opacity: 0; }
  to { transform: rotate(0deg) scale(1); opacity: 1; }
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); opacity: 0.8; }
  70% { transform: scale(0.9); opacity: 0.9; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes typewriter {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink {
  0%, 50% { border-color: transparent; }
  51%, 100% { border-color: var(--color-neon-blue); }
}

/* 頁面過渡動畫 */
.page-enter {
  animation: fadeIn 0.5s ease-out;
}

.page-exit {
  animation: fadeOut 0.3s ease-in;
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(-20px); }
}

/* 卡片動畫 */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(0, 212, 255, 0.2);
}

/* 按鈕動畫 */
.btn-pulse {
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 5px var(--color-neon-blue);
  }
  50% {
    box-shadow: 0 0 20px var(--color-neon-blue), 0 0 30px rgba(0, 212, 255, 0.3);
  }
  100% {
    box-shadow: 0 0 5px var(--color-neon-blue);
  }
}

/* 載入動畫增強 */
.loading-enhanced {
  position: relative;
}

.loading-enhanced::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  margin: -30px 0 0 -30px;
  border: 3px solid transparent;
  border-top: 3px solid var(--color-neon-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 文字動畫 */
.text-glow {
  text-shadow: 0 0 10px var(--color-neon-blue);
  animation: text-pulse 2s ease-in-out infinite alternate;
}

@keyframes text-pulse {
  0% {
    text-shadow: 0 0 10px var(--color-neon-blue);
  }
  100% {
    text-shadow:
      0 0 20px var(--color-neon-blue),
      0 0 30px rgba(0, 212, 255, 0.5);
  }
}

/* 應用動畫類 */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in {
  animation: slideIn 0.4s ease-out;
}

.slide-in-right {
  animation: slideInFromRight 0.5s ease-out;
}

.scale-in {
  animation: scaleIn 0.4s ease-out;
}

.rotate-in {
  animation: rotateIn 0.6s ease-out;
}

.bounce-in {
  animation: bounceIn 0.8s ease-out;
}

/* 延遲動畫 */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }

/* 快速回覆按鈕樣式 */
.quick-reply-btn {
  border-radius: 16px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.quick-reply-btn:hover {
  background-color: var(--color-neon-blue) !important;
  border-color: var(--color-neon-blue) !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;
  transform: translateY(-1px);
}

/* Enhanced Text Renderer 樣式 */
.enhanced-text-renderer {
  line-height: 1.6;
}

.enhanced-text-renderer h1,
.enhanced-text-renderer h2,
.enhanced-text-renderer h3,
.enhanced-text-renderer h4,
.enhanced-text-renderer h5,
.enhanced-text-renderer h6 {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.enhanced-text-renderer h1 {
  font-size: 1.5em;
  border-bottom: 2px solid var(--color-border-primary);
  padding-bottom: 0.3em;
}

.enhanced-text-renderer h2 {
  font-size: 1.3em;
  border-bottom: 1px solid var(--color-border-secondary);
  padding-bottom: 0.2em;
}

.enhanced-text-renderer h3 {
  font-size: 1.1em;
}

.enhanced-text-renderer ul,
.enhanced-text-renderer ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.enhanced-text-renderer li {
  margin: 0.2em 0;
}

.enhanced-text-renderer p {
  margin: 0.5em 0;
}

.enhanced-text-renderer blockquote {
  margin: 0.5em 0;
  padding: 0.5em 1em;
  background-color: var(--color-surface-secondary);
  border-radius: 4px;
}

.enhanced-text-renderer pre {
  margin: 0.5em 0;
  padding: 1em;
  background-color: var(--color-surface-secondary);
  border-radius: 8px;
  overflow-x: auto;
  font-family: 'Courier New', Consolas, monospace;
}

.enhanced-text-renderer code {
  font-family: 'Courier New', Consolas, monospace;
  font-size: 0.9em;
}

.enhanced-text-renderer table {
  border-collapse: collapse;
  margin: 0.5em 0;
  width: 100%;
}

.enhanced-text-renderer th,
.enhanced-text-renderer td {
  border: 1px solid var(--color-border-primary);
  padding: 0.5em;
  text-align: left;
}

.enhanced-text-renderer th {
  background-color: var(--color-surface-secondary);
  font-weight: 600;
}

.enhanced-text-renderer a {
  color: var(--color-neon-blue);
  text-decoration: underline;
  transition: color 0.2s ease;
}

.enhanced-text-renderer a:hover {
  color: var(--color-neon-blue-hover, #2563eb);
  text-decoration: underline;
}

/* 圖片樣式 */
.enhanced-text-renderer .ant-image {
  border-radius: 8px;
  overflow: hidden;
}

.enhanced-text-renderer .ant-image img {
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.enhanced-text-renderer .ant-image:hover img {
  transform: scale(1.02);
}

.quick-reply-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
}

/* 深色模式下的快速回覆按鈕懸停效果 */
[data-theme="dark"] .quick-reply-btn:hover {
  background-color: var(--color-neon-blue) !important;
  border-color: var(--color-neon-blue) !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(167, 139, 250, 0.4) !important;
}

/* 圖標樣式修復 - 防止變形 */
/* Ant Design 圖標樣式 */
.anticon {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 1em !important;
  height: 1em !important;
  font-size: inherit !important;
  line-height: 1 !important;
  text-align: center !important;
  vertical-align: middle !important;
  transform: none !important;
  flex-shrink: 0 !important;
}

/* React Icons 圖標樣式 */
svg[data-icon] {
  display: inline-block !important;
  width: 1em !important;
  height: 1em !important;
  font-size: inherit !important;
  vertical-align: middle !important;
  transform: none !important;
  flex-shrink: 0 !important;
}

/* Avatar 中的圖標樣式 */
.ant-avatar .anticon,
.ant-avatar svg {
  width: 1em !important;
  height: 1em !important;
  font-size: 16px !important;
  line-height: 1 !important;
  transform: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Button 中的圖標樣式 */
.ant-btn .anticon,
.ant-btn svg {
  width: 1em !important;
  height: 1em !important;
  font-size: inherit !important;
  line-height: 1 !important;
  transform: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
}

/* 確保圖標在各種容器中都不會變形 */
.ant-avatar,
.ant-btn,
.quick-reply-btn {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important; /* 防止在 flex 容器中被壓縮 */
}

/* 修復可能的圖標拉伸問題 */
.ant-avatar .anticon svg,
.ant-btn .anticon svg,
svg[data-icon] {
  width: 100% !important;
  height: 100% !important;
  max-width: 1em !important;
  max-height: 1em !important;
  object-fit: contain !important;
  transform: none !important;
}

/* 特別針對 MdSmartToy 等 React Icons */
svg[data-icon="md-smart-toy"],
svg[data-icon="md-send"],
svg[data-icon="md-delete"] {
  width: 1em !important;
  height: 1em !important;
  transform: none !important;
  flex-shrink: 0 !important;
}
