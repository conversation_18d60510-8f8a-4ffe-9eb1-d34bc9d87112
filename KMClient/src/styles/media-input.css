/**
 * 媒體輸入組件樣式
 */

/* 媒體輸入按鈕通用樣式 */
.media-input-button {
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.media-input-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.media-input-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 語音錄製按鈕容器 */
.voice-record-button-container {
  position: relative;
  display: inline-block;
}

/* 語音錄製按鈕 */
.voice-record-button {
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

/* 錄音中的脈衝動畫 */
.voice-record-button.recording {
  animation: pulse-recording 1.5s ease-in-out infinite;
}

@keyframes pulse-recording {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 77, 79, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
  }
}

/* 圖片預覽覆蓋層 */
.image-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  z-index: 1050;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

/* 圖片預覽模態框 */
.image-preview-modal {
  width: 100%;
  max-width: 520px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

/* 圖片預覽卡片 */
.image-preview-card {
  border: none;
  transition: all 0.3s ease;
  background: white;
}

.image-preview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 圖片預覽封面 */
.image-preview-cover {
  position: relative;
}

.image-preview-cover .image-preview-badge {
  position: absolute;
  top: 12px;
  right: 12px;
}

/* 圖片預覽信息區域 */
.image-preview-info {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

/* 圖片預覽操作區域 */
.image-preview-actions {
  padding-top: 8px;
}

/* 語音預覽卡片 */
.voice-preview-card {
  border: 1px solid #d9d9d9;
  background: linear-gradient(135deg, #f6f9fc 0%, #ffffff 100%);
  transition: all 0.3s ease;
}

.voice-preview-card:hover {
  border-color: #40a9ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

/* 媒體輸入區域 */
.media-input-area {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.2s ease;
}

.media-input-area:hover {
  border-color: #40a9ff;
  background: #f0f8ff;
}

/* 媒體預覽容器 */
.media-preview-container {
  /* 容器本身不需要特殊定位，由子組件決定顯示方式 */
}

/* 響應式設計 */
@media (max-width: 768px) {
  .media-input-button {
    height: 32px;
    width: 32px;
    font-size: 14px;
  }

  .image-preview-overlay {
    padding: 12px;
  }

  .image-preview-modal {
    max-width: 100%;
  }

  .image-preview-card {
    border-radius: 16px;
  }

  .image-preview-cover {
    min-height: 200px;
    max-height: 280px;
  }

  .voice-preview-card {
    max-width: 100%;
    margin: 4px 0;
    border-radius: 12px;
  }

  /* 移動端優化的輸入區域 */
  .media-input-area {
    padding: 6px;
    gap: 6px;
  }

  /* 移動端語音錄製按鈕 */
  .voice-record-button-container .recording-duration {
    top: -28px;
    font-size: 10px;
    padding: 2px 6px;
  }

  /* 移動端波形可視化 */
  .waveform-container {
    height: 24px;
    padding: 2px;
  }

  .waveform-bar {
    width: 1.5px;
  }
}

/* 平板設計 */
@media (min-width: 769px) and (max-width: 1024px) {
  .media-input-button {
    height: 34px;
    width: 34px;
  }

  .image-preview-overlay {
    padding: 16px;
  }

  .image-preview-modal {
    max-width: 480px;
  }

  .image-preview-cover {
    min-height: 260px;
    max-height: 340px;
  }

  .voice-preview-card {
    max-width: 350px;
  }
}

/* 大屏幕優化 */
@media (min-width: 1025px) {
  .media-input-button {
    height: 36px;
    width: 36px;
  }

  .media-input-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
  }

  .image-preview-overlay {
    padding: 20px;
  }

  .image-preview-modal {
    max-width: 520px;
  }

  .image-preview-cover {
    min-height: 280px;
    max-height: 400px;
  }

  .voice-preview-card {
    max-width: 400px;
  }
}

/* 暗色主題支持 */
@media (prefers-color-scheme: dark) {
  .media-input-area {
    background: #1f1f1f;
    border-color: #434343;
  }
  
  .media-input-area:hover {
    border-color: #177ddc;
    background: #111b26;
  }
  
  .voice-preview-card {
    background: linear-gradient(135deg, #1f1f1f 0%, #2a2a2a 100%);
  }
}

/* 動畫效果 */
.media-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.media-fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(10px);
  }
}

/* 圖片預覽專用動畫 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideDown {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
}

/* 音量指示器 */
.volume-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: linear-gradient(90deg, #52c41a 0%, #faad14 50%, #ff4d4f 100%);
  border-radius: 0 0 6px 6px;
  transition: width 0.1s ease-out;
}

/* 進度環覆蓋 */
.progress-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  border-radius: 6px;
}

/* 錄音時長顯示 */
.recording-duration {
  position: absolute;
  top: -32px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1001;
}

.recording-duration::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.8);
}

/* 波形可視化 */
.waveform-container {
  display: flex;
  align-items: end;
  justify-content: center;
  height: 32px;
  background: #f5f5f5;
  border-radius: 4px;
  padding: 4px;
  gap: 1px;
}

.waveform-bar {
  width: 2px;
  background: #1890ff;
  border-radius: 1px;
  transition: all 0.1s ease;
}

.waveform-bar.active {
  background: #52c41a;
}

/* 加載狀態 */
.media-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  color: #666;
}

.media-loading .anticon {
  margin-right: 8px;
}

/* 錯誤狀態 */
.media-error {
  color: #ff4d4f;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  padding: 8px 12px;
  margin: 4px 0;
}

/* 成功狀態 */
.media-success {
  color: #52c41a;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  padding: 8px 12px;
  margin: 4px 0;
}

/* 性能優化 - 硬件加速 */
.media-input-button,
.voice-record-button,
.image-preview-card,
.voice-preview-card {
  will-change: transform;
  transform: translateZ(0);
}

/* 用戶體驗改進 - 觸摸友好 */
@media (hover: none) and (pointer: coarse) {
  .media-input-button {
    min-height: 44px;
    min-width: 44px;
  }

  .media-input-button:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
}

/* 無障礙設計 */
.media-input-button:focus-visible {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* 高對比度模式支持 */
@media (prefers-contrast: high) {
  .media-input-button {
    border: 2px solid currentColor;
  }

  .image-preview-card,
  .voice-preview-card {
    border: 2px solid currentColor;
  }
}

/* 減少動畫偏好 */
@media (prefers-reduced-motion: reduce) {
  .media-input-button,
  .voice-record-button,
  .image-preview-card,
  .voice-preview-card,
  .media-fade-in,
  .media-fade-out {
    animation: none;
    transition: none;
  }

  .pulse-recording {
    animation: none;
  }
}

/* 加載狀態優化 */
.media-input-button.loading {
  pointer-events: none;
  opacity: 0.7;
}

/* 拖拽支持 */
.media-input-area.drag-over {
  border-color: #1890ff;
  background: #e6f7ff;
  transform: scale(1.02);
}

.media-input-area.drag-active {
  border-color: #52c41a;
  background: #f6ffed;
}

/* 錯誤狀態動畫 */
.media-input-button.error {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

/* 成功狀態動畫 */
.media-input-button.success {
  animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 語音錄製視覺反饋 */
.voice-recording-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 12px;
  height: 12px;
  background: #ff4d4f;
  border-radius: 50%;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
  100% { opacity: 1; transform: scale(1); }
}

/* 工具提示樣式優化 */
.ant-tooltip-inner {
  font-size: 12px;
  padding: 4px 8px;
}

/* 進度指示器 */
.media-progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: #1890ff;
  transition: width 0.3s ease;
  border-radius: 0 0 6px 6px;
}

/* VoicePlayer 組件樣式 */
.voice-player {
  width: 100%;
  min-width: 280px;
}

.voice-player.compact {
  min-width: 240px;
}

/* 用戶語音播放器樣式 */
.voice-player-user .ant-slider-track {
  background-color: #1890ff !important;
}

.voice-player-user .ant-slider-handle {
  border-color: #1890ff !important;
}

/* 助手語音播放器樣式 */
.voice-player-assistant .ant-slider-track {
  background-color: #52c41a !important;
}

.voice-player-assistant .ant-slider-handle {
  border-color: #52c41a !important;
}

/* 語音播放器錯誤狀態 */
.voice-player .ant-typography-danger {
  font-size: 11px;
  margin-top: 2px;
}

/* 語音播放器加載狀態 */
.voice-player .anticon-loading {
  color: #1890ff;
}

/* 語音播放器按鈕樣式 */
.voice-player .ant-btn[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 響應式設計 - VoicePlayer */
@media (max-width: 768px) {
  .voice-player {
    min-width: 200px;
  }

  .voice-player.compact {
    min-width: 180px;
  }

  .voice-player .ant-btn {
    font-size: 14px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .voice-player {
    min-width: 260px;
  }

  .voice-player.compact {
    min-width: 220px;
  }
}

/* 暗色主題 - VoicePlayer */
@media (prefers-color-scheme: dark) {
  .voice-player-user .ant-slider-track {
    background-color: #177ddc !important;
  }

  .voice-player-user .ant-slider-handle {
    border-color: #177ddc !important;
  }

  .voice-player-assistant .ant-slider-track {
    background-color: #49aa19 !important;
  }

  .voice-player-assistant .ant-slider-handle {
    border-color: #49aa19 !important;
  }
}
