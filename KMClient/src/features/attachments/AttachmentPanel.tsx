/**
 * 附件管理面板組件
 */

import React, { useState } from 'react';
import { Tabs, Button, Space, Badge, Tooltip, Empty } from 'antd';
import type { TabsProps } from 'antd';
import {
  FileOutlined,
  GlobalOutlined,
  YoutubeOutlined,
  FileTextOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useAppStore } from '@/hooks/useAppStore';
import { AttachmentType } from '@/types/attachment';
import FileUploadTab from './FileUploadTab';
import WebsiteTab from './WebsiteTab';
import YoutubeTab from './YoutubeTab';
import PlainTextTab from './PlainTextTab';
import { AnimatedText } from '@/components/ReactBits';

/**
 * 附件管理面板
 */
const AttachmentPanel: React.FC = () => {
  const { attachments, attachmentsLoading, refreshAttachments } = useAppStore();
  const [activeTab, setActiveTab] = useState<string>('files');

  // 根據類型獲取附件數量
  const getAttachmentCount = (type: AttachmentType) =>
    attachments.filter(a => a.type === type).length;

  // Tab 切換處理
  const handleTabChange = (key: string) => {
    setActiveTab(key);

    // 頁籤切換時刷新附件數據
    console.log(`🔄 頁籤切換到 ${key}，刷新附件數據...`);
    refreshAttachments();
  };



  // Tab 項目配置
  const tabItems: TabsProps['items'] = [
    {
      key: 'files',
      label: (
        <Space>
          <FileOutlined />
          檔案
          <Badge count={getAttachmentCount(AttachmentType.FILE)} size="small" />
        </Space>
      ),
      children: <FileUploadTab />,
    },
    {
      key: 'websites',
      label: (
        <Space>
          <GlobalOutlined />
          網站
          <Badge count={getAttachmentCount(AttachmentType.WEBSITE)} size="small" />
        </Space>
      ),
      children: <WebsiteTab />,
    },
    {
      key: 'youtube',
      label: (
        <Space>
          <YoutubeOutlined />
          影片
          <Badge count={getAttachmentCount(AttachmentType.YOUTUBE)} size="small" />
        </Space>
      ),
      children: <YoutubeTab />,
    },
    {
      key: 'text',
      label: (
        <Space>
          <FileTextOutlined />
          文本
          <Badge count={getAttachmentCount(AttachmentType.PLAIN_TEXT)} size="small" />
        </Space>
      ),
      children: <PlainTextTab />,
    },
  ];

  return (
    <div className="p-4 space-y-4">
      {/* 標題與操作區域 */}
      <div className="flex items-center justify-between">
        <AnimatedText text="📚 知識內容" animation="slideUp" duration={0.5} />
        <Space>
          <Tooltip title="刷新列表">
            <Button icon={<ReloadOutlined />} onClick={refreshAttachments} />
          </Tooltip>
        </Space>
      </div>

      {/* 標籤區域 */}
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={tabItems}
      />

      {/* 空狀態提示（若全部為空）*/}
      {attachments.length === 0 && !attachmentsLoading && (
        <div className="text-center py-8">
          <Empty description="尚未添加任何附件" />
        </div>
      )}
    </div>
  );
};

export default AttachmentPanel;

