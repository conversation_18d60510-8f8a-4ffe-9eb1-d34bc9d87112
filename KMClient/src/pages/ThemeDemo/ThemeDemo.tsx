/**
 * 主題切換演示組件
 */

import React from 'react';
import { Card, Button, Typography, Divider } from 'antd';
import {
  MdLightMode,
  MdDarkMode,
  // MdInsertDriveFile, MdLanguage 暫時未使用
} from 'react-icons/md';
import { useAppStore } from '@/hooks/useAppStore';
import {
  SpotlightCard,
  AnimatedText,
  AnimatedBackground,
  TypewriterText,
  GlowText,
  FadeInText
} from '@/ui/animations/react-bits';

const { Text } = Typography;

const ThemeDemo: React.FC = () => {
  const { isDarkMode, toggleDarkMode } = useAppStore();

  return (
    <AnimatedBackground type="particles" opacity={0.05} speed={0.5} density={30}>
      <div className="p-6 space-y-6">
        {/* 標題區域 */}
        <div className="text-center">
          <GlowText
            text="🎨 React Bits + 主題切換演示"
            duration={2}
            as="h2"
            className="text-3xl font-bold mb-4"
            style={{ color: 'var(--color-text-primary)' }}
          />
          <FadeInText
            text={`當前模式：${isDarkMode ? '深色模式' : '淺色模式'}`}
            duration={1}
            delay={0.5}
            style={{ color: 'var(--color-text-secondary)' }}
          />
        </div>

        {/* 切換按鈕 */}
        <div className="text-center">
          <Button
            type="primary"
            size="large"
            icon={isDarkMode ? <MdLightMode /> : <MdDarkMode />}
            onClick={toggleDarkMode}
            style={{ background: 'var(--color-neon-blue)' }}
          >
            切換到{isDarkMode ? '淺色' : '深色'}模式
          </Button>
        </div>

        <Divider />

        {/* 簡化的演示區域 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* SpotlightCard 演示 */}
          <SpotlightCard
            className="transition-colors duration-300"
            backgroundColor="var(--color-surface-primary)"
            spotlightColor="rgba(167, 139, 250, 0.3)"
            padding="24px"
            borderRadius={12}
          >
            <AnimatedText
              text="✨ SpotlightCard 演示"
              animation="slideUp"
              duration={0.6}
              as="h4"
              className="text-lg font-semibold mb-4"
              style={{ color: 'var(--color-text-primary)' }}
            />
            <Text style={{ color: 'var(--color-text-secondary)' }}>
              將鼠標懸停在此卡片上查看光效！
            </Text>
          </SpotlightCard>

          {/* 文字動畫演示 */}
          <SpotlightCard
            className="transition-colors duration-300"
            backgroundColor="var(--color-surface-primary)"
            spotlightColor="rgba(167, 139, 250, 0.3)"
            padding="24px"
            borderRadius={12}
          >
            <AnimatedText
              text="📝 文字動畫演示"
              animation="bounce"
              duration={0.8}
              as="h4"
              className="text-lg font-semibold mb-4"
              style={{ color: 'var(--color-text-primary)' }}
            />
            <TypewriterText
              text="這是打字機效果的演示文字..."
              duration={3}
              repeat={true}
              style={{ color: 'var(--color-text-secondary)' }}
            />
          </SpotlightCard>
        </div>

        {/* 功能說明 */}
        <Card 
          className="transition-colors duration-300"
          style={{ 
            background: 'var(--color-surface-primary)',
            borderColor: 'var(--color-border-primary)'
          }}
        >
          <AnimatedText
            text="🎯 React Bits 集成功能"
            animation="glow"
            duration={2}
            as="h4"
            className="text-lg font-semibold mb-4"
            style={{ color: 'var(--color-neon-blue)' }}
          />
          <ul style={{ color: 'var(--color-text-secondary)' }}>
            <li>✅ SpotlightCard - 光效互動卡片</li>
            <li>✅ AnimatedText - 多種文字動畫效果</li>
            <li>✅ AnimatedBackground - 粒子背景系統</li>
            <li>✅ 完美支持深色/淺色主題切換</li>
            <li>✅ 保持所有原有功能不變</li>
          </ul>
        </Card>
      </div>
    </AnimatedBackground>
  );
};

export default ThemeDemo;

