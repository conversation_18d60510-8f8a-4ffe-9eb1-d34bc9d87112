/**
 * MotionDemo - Framer Motion 動效演示頁面
 * 展示所有動效組件的功能和效果
 */

import React, { useState } from 'react';
import { Card, Space, Button, Typography, Divider, Row, Col } from 'antd';
import MainLayout from '@/components/Layout/MainLayout';
import {
  AnimatedButton,
  AnimatedInput,
  AnimatedTextArea,
  ListAnimation,
  LoadingAnimation,
  StatusIndicator,
  ProgressIndicator,
  PulseIndicator,
  AnimationSettings,
  InteractiveMotion,
  CardMotion,
  // PageTransition 暫時未使用
  AnimatedModal,
} from '@/ui/animations/framer-motion';
import { SpotlightCard, AnimatedText } from '@/ui/animations/react-bits';

const { Title, Text } = Typography;

const MotionDemo: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [progress, setProgress] = useState(30);
  const [listItems, setListItems] = useState(['項目 1', '項目 2', '項目 3']);

  const addListItem = () => {
    const newItem = `項目 ${listItems.length + 1}`;
    setListItems([...listItems, newItem]);
  };

  const removeListItem = () => {
    if (listItems.length > 0) {
      setListItems(listItems.slice(0, -1));
    }
  };

  const updateProgress = () => {
    setProgress((prev) => (prev >= 100 ? 0 : prev + 10));
  };

  return (
    <MainLayout>
      <div style={{ padding: 24, maxWidth: 1200, margin: '0 auto' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 標題 */}
          <div style={{ textAlign: 'center' }}>
            <AnimatedText
              text="🎬 Framer Motion 動效演示"
              animation="glow"
              duration={1}
              as="h1"
              style={{ color: 'var(--color-neon-blue)', marginBottom: 8 }}
            />
            <Text type="secondary">
              展示現代化的動畫效果和交互體驗
            </Text>
          </div>

          {/* 動畫設置 */}
          <Card title="動畫設置" size="small">
            <AnimationSettings />
          </Card>

          {/* 按鈕動畫 */}
          <Card title="按鈕動畫" size="small">
            <Space wrap>
              <AnimatedButton type="primary" animationType="scale">
                縮放按鈕
              </AnimatedButton>
              <AnimatedButton type="default" animationType="lift">
                懸浮按鈕
              </AnimatedButton>
              <AnimatedButton type="dashed" animationType="glow">
                發光按鈕
              </AnimatedButton>
              <AnimatedButton type="text" animationType="bounce">
                彈跳按鈕
              </AnimatedButton>
            </Space>
          </Card>

          {/* 輸入框動畫 */}
          <Card title="輸入框動畫" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Text>發光效果:</Text>
                  <AnimatedInput placeholder="聚焦時發光" animationType="glow" />
                </Col>
                <Col span={8}>
                  <Text>縮放效果:</Text>
                  <AnimatedInput placeholder="聚焦時縮放" animationType="scale" />
                </Col>
                <Col span={8}>
                  <Text>懸浮效果:</Text>
                  <AnimatedInput placeholder="聚焦時懸浮" animationType="lift" />
                </Col>
              </Row>
              <div>
                <Text>文本域動畫:</Text>
                <AnimatedTextArea 
                  placeholder="這是一個帶動畫的文本域" 
                  rows={3} 
                  animationType="glow"
                />
              </div>
            </Space>
          </Card>

          {/* 列表動畫 */}
          <Card 
            title="列表動畫" 
            size="small"
            extra={
              <Space>
                <Button size="small" onClick={addListItem}>添加項目</Button>
                <Button size="small" onClick={removeListItem}>移除項目</Button>
              </Space>
            }
          >
            <ListAnimation type="slideUp" stagger={0.1}>
              {listItems.map((item, index) => (
                <SpotlightCard key={index} className="mb-2">
                  <Text>{item}</Text>
                </SpotlightCard>
              ))}
            </ListAnimation>
          </Card>

          {/* 加載動畫 */}
          <Card title="加載動畫" size="small">
            <Row gutter={[16, 16]}>
              <Col span={6}>
                <Space direction="vertical" align="center">
                  <LoadingAnimation type="spinner" size="medium" />
                  <Text>旋轉加載</Text>
                </Space>
              </Col>
              <Col span={6}>
                <Space direction="vertical" align="center">
                  <LoadingAnimation type="dots" size="medium" />
                  <Text>點動畫</Text>
                </Space>
              </Col>
              <Col span={6}>
                <Space direction="vertical" align="center">
                  <LoadingAnimation type="pulse" size="medium" />
                  <Text>脈衝動畫</Text>
                </Space>
              </Col>
              <Col span={6}>
                <Space direction="vertical" align="center">
                  <LoadingAnimation type="wave" size="medium" />
                  <Text>波浪動畫</Text>
                </Space>
              </Col>
            </Row>
          </Card>

          {/* 狀態指示器 */}
          <Card title="狀態指示器" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Row gutter={16}>
                <Col span={6}>
                  <StatusIndicator status="loading" message="加載中..." />
                </Col>
                <Col span={6}>
                  <StatusIndicator status="success" message="操作成功" />
                </Col>
                <Col span={6}>
                  <StatusIndicator status="error" message="操作失敗" />
                </Col>
                <Col span={6}>
                  <StatusIndicator status="warning" message="警告信息" />
                </Col>
              </Row>
              
              <Divider />
              
              <div>
                <Space>
                  <Text>進度指示器:</Text>
                  <Button size="small" onClick={updateProgress}>更新進度</Button>
                </Space>
                <ProgressIndicator progress={progress} />
              </div>
              
              <div>
                <Space>
                  <Text>脈衝指示器:</Text>
                  <PulseIndicator active />
                  <PulseIndicator active={false} color="#ccc" />
                </Space>
              </div>
            </Space>
          </Card>

          {/* 交互動畫 */}
          <Card title="交互動畫" size="small">
            <Row gutter={16}>
              <Col span={8}>
                <InteractiveMotion interactions={['hover']}>
                  <div style={{ 
                    padding: 20, 
                    backgroundColor: 'var(--color-surface-primary)', 
                    border: '1px solid var(--color-border-primary)',
                    borderRadius: 8,
                    textAlign: 'center'
                  }}>
                    懸停我
                  </div>
                </InteractiveMotion>
              </Col>
              <Col span={8}>
                <CardMotion>
                  <div style={{ 
                    padding: 20, 
                    backgroundColor: 'var(--color-surface-primary)', 
                    border: '1px solid var(--color-border-primary)',
                    borderRadius: 8,
                    textAlign: 'center'
                  }}>
                    卡片動畫
                  </div>
                </CardMotion>
              </Col>
              <Col span={8}>
                <InteractiveMotion interactions={['tap']}>
                  <div style={{ 
                    padding: 20, 
                    backgroundColor: 'var(--color-surface-primary)', 
                    border: '1px solid var(--color-border-primary)',
                    borderRadius: 8,
                    textAlign: 'center'
                  }}>
                    點擊我
                  </div>
                </InteractiveMotion>
              </Col>
            </Row>
          </Card>

          {/* 模態框動畫 */}
          <Card title="模態框動畫" size="small">
            <Space>
              <Button onClick={() => setModalVisible(true)}>
                打開動畫模態框
              </Button>
            </Space>
            
            <AnimatedModal
              isOpen={modalVisible}
              onClose={() => setModalVisible(false)}
              animationType="scale"
              contentClassName="p-6 bg-white rounded-lg"
            >
              <div style={{ padding: 24, textAlign: 'center' }}>
                <Title level={4}>動畫模態框</Title>
                <Text>這是一個帶有縮放動畫的模態框</Text>
                <div style={{ marginTop: 16 }}>
                  <Button onClick={() => setModalVisible(false)}>
                    關閉
                  </Button>
                </div>
              </div>
            </AnimatedModal>
          </Card>
        </Space>
      </div>
    </MainLayout>
  );
};

export default MotionDemo;

