/**
 * 系統指令設置頁面 - 獨立頁面
 */

import React, { useEffect } from 'react';
import {
  // Card 暫時未使用
  Form,
  Input,
  Button,
  Space,
  Typography,
  Alert,
  Spin,
  Row,
  Col,
  Popconfirm
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  PlusOutlined,
  DeleteOutlined,
  ArrowLeftOutlined,
  RobotOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useSysInstructionStore } from '@/hooks/useSysInstructionStore';
import { useAppStore } from '@/hooks/useAppStore';
import {
  SpotlightCard,
  GlowText,
  FadeInText,
  AnimatedText
} from '@/ui/animations/react-bits';

const { TextArea } = Input;
const { Text } = Typography;

/**
 * 系統指令設置頁面
 */
const SystemInstructionsPage: React.FC = () => {
  const navigate = useNavigate();
  const { urlParams } = useAppStore();
  
  const {
    isLoading,
    isSaving,
    error,
    systemInstruction,
    fetchSystemInstruction,
    saveSystemInstruction,
    setGlobalInstruction,
    addServiceInstruction,
    updateServiceInstruction,
    removeServiceInstruction,
    resetSystemInstruction,
    hasChanges,
    clearError
  } = useSysInstructionStore();

  // 獲取當前租戶 ID
  const tenantId = urlParams.tenantId || '';

  // 頁面載入時獲取系統指令
  useEffect(() => {
    if (tenantId) {
      fetchSystemInstruction(tenantId);
    }
  }, [tenantId, fetchSystemInstruction]);

  // 處理保存
  const handleSave = async () => {
    if (tenantId) {
      await saveSystemInstruction(tenantId);
    }
  };

  // 處理重置
  const handleReset = () => {
    resetSystemInstruction();
  };

  // 處理重新載入
  const handleReload = () => {
    if (tenantId) {
      fetchSystemInstruction(tenantId);
    }
  };

  // 返回主頁
  const handleGoBack = () => {
    navigate('/');
  };

  // 如果沒有租戶 ID，顯示錯誤
  if (!tenantId) {
    return (
      <div
        className="min-h-screen p-6 transition-colors duration-300"
        style={{ background: 'var(--color-bg-primary)' }}
      >
        <div className="max-w-4xl mx-auto">
          <SpotlightCard
            className="transition-colors duration-300"
            backgroundColor="var(--color-surface-primary)"
            spotlightColor="rgba(239, 68, 68, 0.3)"
          >
            <Alert
              message="無法獲取租戶信息"
              description="請確保 URL 包含正確的 TenantID 參數"
              type="error"
              showIcon
              style={{
                background: 'var(--color-surface-secondary)',
                borderColor: 'var(--color-border-primary)',
                color: 'var(--color-text-primary)'
              }}
            />
            <div className="mt-4">
              <Button
                type="primary"
                icon={<ArrowLeftOutlined />}
                onClick={handleGoBack}
                style={{
                  background: 'var(--color-neon-blue)',
                  borderColor: 'var(--color-neon-blue)'
                }}
              >
                返回主頁
              </Button>
            </div>
          </SpotlightCard>
        </div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen p-6 transition-colors duration-300"
      style={{ background: 'var(--color-bg-primary)' }}
    >
      <div className="max-w-6xl mx-auto space-y-6">
        {/* 頁面標題 */}
        <SpotlightCard
          className="transition-colors duration-300"
          backgroundColor="var(--color-surface-primary)"
          spotlightColor="rgba(59, 130, 246, 0.3)"
          borderRadius={12}
        >
          <div className="flex items-center space-x-4">
            <div
              className="p-3 rounded-lg transition-colors duration-300"
              style={{ background: 'rgba(59, 130, 246, 0.2)' }}
            >
              <RobotOutlined
                className="text-2xl animate-pulse-slow"
                style={{ color: 'var(--color-neon-blue)' }}
              />
            </div>
            <div>
              <GlowText
                text="🤖 系統指令設置"
                duration={2}
                as="h2"
                className="text-2xl font-bold mb-2"
                style={{ color: 'var(--color-text-primary)' }}
              />
              <FadeInText
                text="配置 AI 助手的系統指令，包括全局指令和特定服務的指令"
                duration={1}
                delay={0.5}
                style={{ color: 'var(--color-text-secondary)' }}
              />
            </div>
          </div>

          <div
            className="mt-4 p-4 rounded-lg transition-colors duration-300"
            style={{
              background: 'rgba(59, 130, 246, 0.1)',
              border: '1px solid rgba(59, 130, 246, 0.3)'
            }}
          >
            <FadeInText
              text="💡 提示：系統指令是 AI 助手的「人格設定」，它會影響 AI 的回答風格、專業領域和行為準則。設置得當的系統指令可以讓 AI 助手更好地滿足您的需求。"
              duration={1}
              delay={0.8}
              style={{ color: 'var(--color-neon-blue)' }}
            />
          </div>
        </SpotlightCard>

        {/* 錯誤提示 */}
        {error && (
          <Alert
            message="操作失敗"
            description={error}
            type="error"
            showIcon
            closable
            onClose={clearError}
            style={{
              background: 'var(--color-surface-secondary)',
              borderColor: 'var(--color-border-primary)',
              color: 'var(--color-text-primary)'
            }}
          />
        )}

        {/* 載入狀態 */}
        <Spin spinning={isLoading} tip="載入中...">
          <div className="space-y-6">
            {/* 全局系統指令 */}
            <SpotlightCard
              className="transition-colors duration-300"
              backgroundColor="var(--color-surface-primary)"
              spotlightColor="rgba(16, 185, 129, 0.3)"
              borderRadius={12}
            >
              <div className="flex items-center justify-between mb-4">
                <AnimatedText
                  text="🌐 全局系統指令"
                  animation="slideUp"
                  duration={0.6}
                  as="h3"
                  className="text-lg font-semibold"
                  style={{ color: 'var(--color-text-primary)' }}
                />
                <Space>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleReload}
                    disabled={isLoading || isSaving}
                    className="transition-colors duration-300"
                    style={{
                      background: 'var(--color-surface-secondary)',
                      borderColor: 'var(--color-border-primary)',
                      color: 'var(--color-text-primary)'
                    }}
                  >
                    重新載入
                  </Button>
                </Space>
              </div>
              <Form layout="vertical">
                <Form.Item
                  label={
                    <span style={{ color: 'var(--color-text-primary)' }}>
                      系統指令內容
                    </span>
                  }
                  help={
                    <div style={{ color: 'var(--color-text-tertiary)' }}>
                      <div>這個指令將應用於所有服務，作為 AI 助手的基礎行為準則</div>
                      <div className="mt-1 text-xs" style={{ color: 'var(--color-text-quaternary)' }}>
                        示例：「你是一個專業的客服助手，請用友善、耐心的語氣回答用戶問題。回答要準確、簡潔，並提供實用的建議。」
                      </div>
                    </div>
                  }
                >
                  <TextArea
                    value={systemInstruction.system}
                    onChange={(e) => setGlobalInstruction(e.target.value)}
                    placeholder="例如：你是一個專業的 AI 助手，請用友善、準確的方式回答用戶問題..."
                    rows={6}
                    disabled={isSaving}
                    className="transition-colors duration-300"
                    style={{
                      background: 'var(--color-surface-secondary)',
                      borderColor: 'var(--color-border-primary)',
                      color: 'var(--color-text-primary)'
                    }}
                  />
                </Form.Item>
              </Form>
            </SpotlightCard>

            {/* 服務特定指令 */}
            <SpotlightCard
              className="transition-colors duration-300"
              backgroundColor="var(--color-surface-primary)"
              spotlightColor="rgba(139, 92, 246, 0.3)"
              borderRadius={12}
            >
              <div className="flex items-center justify-between mb-4">
                <AnimatedText
                  text="⚙️ 服務特定指令"
                  animation="slideUp"
                  duration={0.6}
                  delay={0.2}
                  as="h3"
                  className="text-lg font-semibold"
                  style={{ color: 'var(--color-text-primary)' }}
                />
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={addServiceInstruction}
                  disabled={isSaving}
                  className="transition-colors duration-300"
                  style={{
                    background: 'var(--color-neon-blue)',
                    borderColor: 'var(--color-neon-blue)'
                  }}
                >
                  添加服務指令
                </Button>
              </div>
              <div className="space-y-4">
                {(!systemInstruction.service_instructions || systemInstruction.service_instructions.length === 0) ? (
                  <div className="text-center py-8">
                    <FadeInText
                      text="暫無服務特定指令，點擊上方按鈕添加"
                      duration={1}
                      delay={0.5}
                      style={{ color: 'var(--color-text-tertiary)' }}
                    />
                  </div>
                ) : (
                  (systemInstruction.service_instructions || []).map((instruction, index) => (
                    <SpotlightCard
                      key={index}
                      className="transition-colors duration-300"
                      backgroundColor="var(--color-surface-secondary)"
                      spotlightColor="rgba(99, 102, 241, 0.2)"
                      borderRadius={8}
                      padding="16px"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <span style={{ color: 'var(--color-text-primary)', fontWeight: 500 }}>
                          服務指令 #{index + 1}
                        </span>
                        <Popconfirm
                          title="確定要刪除這個服務指令嗎？"
                          onConfirm={() => removeServiceInstruction(index)}
                          okText="確定"
                          cancelText="取消"
                        >
                          <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                            disabled={isSaving}
                            style={{ color: '#ef4444' }}
                          />
                        </Popconfirm>
                      </div>
                      <Form layout="vertical">
                        <Row gutter={16}>
                          <Col span={8}>
                            <Form.Item
                              label={
                                <span style={{ color: 'var(--color-text-primary)' }}>
                                  服務 ID
                                </span>
                              }
                            >
                              <Input
                                value={instruction.service_id}
                                onChange={(e) => updateServiceInstruction(index, {
                                  service_id: e.target.value
                                })}
                                placeholder="例如：service1"
                                disabled={isSaving}
                                className="transition-colors duration-300"
                                style={{
                                  background: 'var(--color-surface-tertiary)',
                                  borderColor: 'var(--color-border-secondary)',
                                  color: 'var(--color-text-primary)'
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col span={8}>
                            <Form.Item
                              label={
                                <span style={{ color: 'var(--color-text-primary)' }}>
                                  通道
                                </span>
                              }
                            >
                              <Input
                                value={instruction.channel}
                                onChange={(e) => updateServiceInstruction(index, {
                                  channel: e.target.value
                                })}
                                placeholder="例如：web, line"
                                disabled={isSaving}
                                className="transition-colors duration-300"
                                style={{
                                  background: 'var(--color-surface-tertiary)',
                                  borderColor: 'var(--color-border-secondary)',
                                  color: 'var(--color-text-primary)'
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col span={24}>
                            <Form.Item
                              label={
                                <span style={{ color: 'var(--color-text-primary)' }}>
                                  指令內容
                                </span>
                              }
                            >
                              <TextArea
                                value={instruction.sys_instruction}
                                onChange={(e) => updateServiceInstruction(index, {
                                  sys_instruction: e.target.value
                                })}
                                placeholder="請輸入該服務的特定指令..."
                                rows={3}
                                disabled={isSaving}
                                className="transition-colors duration-300"
                                style={{
                                  background: 'var(--color-surface-tertiary)',
                                  borderColor: 'var(--color-border-secondary)',
                                  color: 'var(--color-text-primary)'
                                }}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Form>
                    </SpotlightCard>
                  ))
                )}
              </div>
            </SpotlightCard>

            {/* 操作按鈕 */}
            <SpotlightCard
              className="transition-colors duration-300"
              backgroundColor="var(--color-surface-primary)"
              spotlightColor="rgba(245, 158, 11, 0.3)"
              borderRadius={12}
            >
              <div className="flex justify-between items-center">
                <div>
                  <Text style={{ color: 'var(--color-text-secondary)' }}>
                    租戶 ID: <span
                      className="font-mono"
                      style={{ color: 'var(--color-neon-blue)' }}
                    >
                      {tenantId}
                    </span>
                  </Text>
                  {hasChanges() && (
                    <Text
                      className="ml-4"
                      style={{ color: '#f59e0b' }}
                    >
                      • 有未保存的更改
                    </Text>
                  )}
                </div>

                <Space>
                  <Button
                    onClick={handleReset}
                    disabled={!hasChanges() || isSaving}
                    className="transition-colors duration-300"
                    style={{
                      background: 'var(--color-surface-secondary)',
                      borderColor: 'var(--color-border-primary)',
                      color: 'var(--color-text-primary)'
                    }}
                  >
                    重置更改
                  </Button>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={handleSave}
                    loading={isSaving}
                    disabled={!hasChanges()}
                    className="transition-all duration-300 hover:scale-105"
                    style={{
                      background: 'var(--color-neon-blue)',
                      borderColor: 'var(--color-neon-blue)',
                      boxShadow: hasChanges() ? '0 0 20px rgba(59, 130, 246, 0.4)' : 'none'
                    }}
                  >
                    保存設置
                  </Button>
                </Space>
              </div>
            </SpotlightCard>
          </div>
        </Spin>
      </div>
    </div>
  );
};

export default SystemInstructionsPage;

