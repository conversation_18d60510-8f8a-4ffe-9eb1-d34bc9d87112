/**
 * AI回復計時器Hook
 * 用於追蹤AI回復完成後的時間
 */

import { useState, useEffect, useCallback, useRef } from 'react';

interface UseResponseTimerReturn {
  elapsedTime: number;
  isRunning: boolean;
  currentMessageId: string | null;
  startWaitingTimer: () => void; // 用戶發送消息時開始計時
  stopTimer: (messageId: string) => void; // AI回復完成時停止計時
  resetTimer: () => void;
}

export const useResponseTimer = (): UseResponseTimerReturn => {
  const [elapsedTime, setElapsedTime] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [currentMessageId, setCurrentMessageId] = useState<string | null>(null);
  const intervalRef = useRef<number | null>(null);

  // 用戶發送消息時開始計時
  const startWaitingTimer = useCallback(() => {
    // 停止之前的計時器
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // 重置狀態並開始計時
    setElapsedTime(0);
    setIsRunning(true);
    setCurrentMessageId('waiting'); // 使用特殊ID表示等待狀態

    // 開始新的計時器
    intervalRef.current = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000) as unknown as number;
  }, []);

  // AI回復完成時停止計時器
  const stopTimer = useCallback((messageId: string) => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setIsRunning(false);
    setCurrentMessageId(messageId); // 設置為具體的消息ID
  }, []);

  // 重置計時器
  const resetTimer = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setElapsedTime(0);
    setIsRunning(false);
    setCurrentMessageId(null);
  }, []);

  // 組件卸載時清理計時器
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    elapsedTime,
    isRunning,
    currentMessageId,
    startWaitingTimer,
    stopTimer,
    resetTimer,
  };
};

export default useResponseTimer;
