/**
 * 配置管理 Hook
 * 提供配置載入、更新和熱重載功能
 */

import { useState, useEffect, useCallback } from 'react';
import { Config } from '@/types/config';
import { configService } from '@/services/configService';

// 配置 Hook 狀態
interface UseConfigState {
  config: Config | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: number | null;
}

// 配置 Hook 返回值
interface UseConfigReturn extends UseConfigState {
  loadConfig: () => Promise<void>;
  reloadConfig: () => Promise<void>;
  buildApiUrl: (serviceName: keyof Config['api']['services'], endpoint: string) => string;
  getApiConfig: () => Config['api'];
  getFeatureConfig: () => Config['features'];
  getUIConfig: () => Config['ui'];
}

/**
 * 配置管理 Hook
 */
export const useConfig = (): UseConfigReturn => {
  const [state, setState] = useState<UseConfigState>({
    config: null,
    isLoading: false,
    error: null,
    lastUpdated: null,
  });

  // 載入配置
  const loadConfig = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const config = await configService.loadConfig();
      setState({
        config,
        isLoading: false,
        error: null,
        lastUpdated: Date.now(),
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '載入配置失敗';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, []);

  // 重新載入配置
  const reloadConfig = useCallback(async () => {
    await loadConfig();
  }, [loadConfig]);

  // 構建 API URL
  const buildApiUrl = useCallback((serviceName: keyof Config['api']['services'], endpoint: string) => {
    return configService.buildApiUrl(serviceName, endpoint);
  }, []);

  // 獲取 API 配置
  const getApiConfig = useCallback(() => {
    return configService.getApiConfig();
  }, []);

  // 獲取功能配置
  const getFeatureConfig = useCallback(() => {
    return configService.getFeatureConfig();
  }, []);

  // 獲取 UI 配置
  const getUIConfig = useCallback(() => {
    return configService.getUIConfig();
  }, []);

  // 監聽配置更新
  useEffect(() => {
    const removeListener = configService.addListener((config) => {
      setState(prev => ({
        ...prev,
        config,
        lastUpdated: Date.now(),
        error: null,
      }));
    });

    return removeListener;
  }, []);

  // 初始載入配置
  useEffect(() => {
    loadConfig();
  }, [loadConfig]);

  return {
    ...state,
    loadConfig,
    reloadConfig,
    buildApiUrl,
    getApiConfig,
    getFeatureConfig,
    getUIConfig,
  };
};

/**
 * 配置熱重載 Hook
 * 專門用於管理熱重載功能
 */
export const useConfigHotReload = () => {
  const [isEnabled, setIsEnabled] = useState(false);
  const [lastReload, setLastReload] = useState<number | null>(null);

  // 啟用/禁用熱重載
  const toggleHotReload = useCallback((enabled: boolean) => {
    setIsEnabled(enabled);
    
    if (enabled) {
      // 啟動熱重載
      const config = configService.getConfig();
      if (config.features.hotReload.enabled) {
        console.log('Hot reload enabled');
      }
    } else {
      // 停止熱重載
      configService.stopHotReload();
      console.log('Hot reload disabled');
    }
  }, []);

  // 監聽配置更新
  useEffect(() => {
    const removeListener = configService.addListener((config) => {
      setLastReload(Date.now());
      
      // 檢查熱重載設置
      if (config.features.hotReload.enabled !== isEnabled) {
        setIsEnabled(config.features.hotReload.enabled);
      }
    });

    return removeListener;
  }, [isEnabled]);

  return {
    isEnabled,
    lastReload,
    toggleHotReload,
  };
};

/**
 * API 配置 Hook
 * 專門用於 API 相關配置
 */
export const useApiConfig = () => {
  const { config, isLoading, error } = useConfig();

  const buildUrl = useCallback((serviceName: keyof Config['api']['services'], endpoint: string) => {
    if (!config) {
      throw new Error('Config not loaded');
    }
    return configService.buildApiUrl(serviceName, endpoint);
  }, [config]);

  const getServiceConfig = useCallback((serviceName: keyof Config['api']['services']) => {
    if (!config) {
      return null;
    }
    return config.api.services[serviceName];
  }, [config]);

  const getGatewayConfig = useCallback(() => {
    if (!config) {
      return null;
    }
    return config.api.gateway;
  }, [config]);

  return {
    apiConfig: config?.api || null,
    isLoading,
    error,
    buildUrl,
    getServiceConfig,
    getGatewayConfig,
  };
};
