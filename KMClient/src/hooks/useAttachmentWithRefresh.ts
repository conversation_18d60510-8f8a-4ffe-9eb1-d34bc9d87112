/**
 * 帶自動刷新功能的附件服務 Hook
 */

import { useCallback } from 'react';
import { message } from 'antd';
import { useAttachmentService } from '@/services/attachmentService';
import { useAppStore } from '@/hooks/useAppStore';
import {
  UploadFileRequest,
  DeleteFileRequest,
  DownloadFileRequest,
  SetWebsiteRequest,
  DeleteWebsiteRequest,
  SetYoutubeRequest,
  DeleteYoutubeRequest,
  SetPlainTextRequest,
  DeletePlainTextRequest,
} from '@/types/attachment';

/**
 * 帶自動刷新功能的附件服務 Hook
 * 在每個操作成功後延遲刷新附件資料，確保後端有足夠時間處理
 */
export const useAttachmentWithRefresh = () => {
  const attachmentService = useAttachmentService();
  const { refreshAttachments } = useAppStore();

  // 延遲刷新配置
  const REFRESH_DELAY = 1500; // 1.5 秒延遲，確保後端處理完成

  /**
   * 延遲刷新附件列表
   * @param delay 延遲時間（毫秒），默認 1.5 秒
   */
  const delayedRefresh = useCallback((delay: number = REFRESH_DELAY) => {
    console.log(`⏰ 將在 ${delay}ms 後刷新附件列表...`);
    setTimeout(async () => {
      console.log('🔄 開始延遲刷新附件列表...');
      await refreshAttachments();
    }, delay);
  }, [refreshAttachments]);

  // 上傳檔案（帶延遲刷新）
  const uploadFiles = useCallback(async (request: UploadFileRequest) => {
    try {
      const response = await attachmentService.uploadFiles(request);

      if (response.code === 0) {
        // 顯示成功訊息
        message.success(`成功上傳 ${request.file.length} 個文件，正在更新列表...`);

        // 延遲刷新資料，確保後端有足夠時間處理
        delayedRefresh();
      } else {
        throw new Error(response.message || '上傳失敗');
      }

      return response;
    } catch (error) {
      console.error('上傳文件失敗:', error);
      message.error(error instanceof Error ? error.message : '上傳文件失敗');
      throw error;
    }
  }, [attachmentService, delayedRefresh]);

  // 刪除檔案（帶延遲刷新）
  const deleteFiles = useCallback(async (request: DeleteFileRequest) => {
    try {
      const response = await attachmentService.deleteFiles(request);

      if (response.code === 0) {
        // 顯示成功訊息
        message.success(`成功刪除 ${request.file_names.length} 個文件，正在更新列表...`);

        // 延遲刷新資料
        delayedRefresh();
      } else {
        throw new Error(response.message || '刪除失敗');
      }

      return response;
    } catch (error) {
      console.error('刪除文件失敗:', error);
      message.error(error instanceof Error ? error.message : '刪除文件失敗');
      throw error;
    }
  }, [attachmentService, delayedRefresh]);

  // 下載檔案
  const downloadFile = useCallback(async (request: DownloadFileRequest, fileName?: string) => {
    try {
      console.log('📥 準備下載文件:', { file_path: request.file_path, fileName });

      const blob = await attachmentService.downloadFile(request);

      // 使用提供的文件名，如果沒有則從 file_path 中提取
      const downloadFileName = fileName || request.file_path.split('/').pop() || 'download';

      // 檢查瀏覽器是否支持 File System Access API（用於打開保存對話框）
      if ('showSaveFilePicker' in window) {
        try {
          // 使用現代瀏覽器的文件保存對話框
          const fileHandle = await (window as any).showSaveFilePicker({
            suggestedName: downloadFileName,
            types: [{
              description: '文件',
              accept: {
                '*/*': [`.${downloadFileName.split('.').pop()}`]
              }
            }]
          });

          const writable = await fileHandle.createWritable();
          await writable.write(blob);
          await writable.close();

          console.log('✅ 文件保存成功:', downloadFileName);
          message.success('文件保存成功');
        } catch (saveError: any) {
          if (saveError.name === 'AbortError') {
            console.log('🚫 用戶取消了保存操作');
            message.info('保存操作已取消');
            return;
          }
          throw saveError;
        }
      } else {
        // 回退到傳統的下載方式（會打開瀏覽器的下載對話框）
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // 不設置 download 屬性，讓瀏覽器決定是否打開保存對話框
        // 或者設置為空字符串來觸發保存對話框
        link.download = downloadFileName;

        // 設置 target="_blank" 可能會在某些瀏覽器中觸發保存對話框
        link.target = '_blank';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        console.log('✅ 文件下載觸發成功:', downloadFileName);
        message.success('文件下載已開始');
      }

      return blob;
    } catch (error) {
      console.error('❌ 下載文件失敗:', error);

      // 根據錯誤類型提供不同的用戶提示
      let errorMessage = '下載文件失敗';

      if (error instanceof Error) {
        if (error.message.includes('CORS')) {
          errorMessage = '下載失敗：跨域請求被阻止，請檢查服務器配置';
        } else if (error.message.includes('Network Error') || error.message.includes('ERR_FAILED')) {
          errorMessage = '下載失敗：網絡連接錯誤，請檢查網絡連接';
        } else if (error.message.includes('timeout')) {
          errorMessage = '下載失敗：請求超時，請稍後重試';
        } else {
          errorMessage = `下載失敗：${error.message}`;
        }
      }

      message.error(errorMessage);
      throw error;
    }
  }, [attachmentService]);

  // 設置網站（帶延遲刷新）
  const setWebsite = useCallback(async (request: SetWebsiteRequest) => {
    try {
      const response = await attachmentService.setWebsite(request);

      if (response.code === 0) {
        // 顯示成功訊息
        message.success('網站添加成功！');

        // 延遲刷新資料
        delayedRefresh();
      } else {
        throw new Error(response.message || '添加網站失敗');
      }

      return response;
    } catch (error) {
      console.error('設置網站失敗:', error);
      message.error(error instanceof Error ? error.message : '添加網站失敗');
      throw error;
    }
  }, [attachmentService, delayedRefresh]);

  // 刪除網站（帶延遲刷新）
  const deleteWebsite = useCallback(async (request: DeleteWebsiteRequest) => {
    try {
      const response = await attachmentService.deleteWebsite(request);

      if (response.code === 0) {
        // 顯示成功訊息
        message.success('網站刪除成功，正在更新列表...');

        // 延遲刷新資料
        delayedRefresh();
      } else {
        throw new Error(response.message || '刪除網站失敗');
      }

      return response;
    } catch (error) {
      console.error('刪除網站失敗:', error);
      message.error(error instanceof Error ? error.message : '刪除網站失敗');
      throw error;
    }
  }, [attachmentService, delayedRefresh]);

  // 設置 YouTube（帶延遲刷新）
  const setYoutube = useCallback(async (request: SetYoutubeRequest) => {
    try {
      const response = await attachmentService.setYoutube(request);

      if (response.code === 0) {
        // 顯示成功訊息
        message.success('YouTube 影片添加成功，正在更新列表...');

        // 延遲刷新資料
        delayedRefresh();
      } else {
        throw new Error(response.message || '添加 YouTube 影片失敗');
      }

      return response;
    } catch (error) {
      console.error('設置 YouTube 失敗:', error);
      message.error(error instanceof Error ? error.message : '添加 YouTube 影片失敗');
      throw error;
    }
  }, [attachmentService, delayedRefresh]);

  // 刪除 YouTube（帶延遲刷新）
  const deleteYoutube = useCallback(async (request: DeleteYoutubeRequest) => {
    try {
      const response = await attachmentService.deleteYoutube(request);

      if (response.code === 0) {
        // 顯示成功訊息
        message.success('YouTube 影片刪除成功，正在更新列表...');

        // 延遲刷新資料
        delayedRefresh();
      } else {
        throw new Error(response.message || '刪除 YouTube 影片失敗');
      }

      return response;
    } catch (error) {
      console.error('刪除 YouTube 失敗:', error);
      message.error(error instanceof Error ? error.message : '刪除 YouTube 影片失敗');
      throw error;
    }
  }, [attachmentService, delayedRefresh]);

  // 設置純文本（帶延遲刷新）
  const setPlainText = useCallback(async (request: SetPlainTextRequest) => {
    try {
      const response = await attachmentService.setPlainText(request);

      if (response.code === 0) {
        // 顯示成功訊息
        message.success('純文本添加成功，正在更新列表...');

        // 延遲刷新資料
        delayedRefresh();
      } else {
        throw new Error(response.message || '添加純文本失敗');
      }

      return response;
    } catch (error) {
      console.error('設置純文本失敗:', error);
      message.error(error instanceof Error ? error.message : '添加純文本失敗');
      throw error;
    }
  }, [attachmentService, delayedRefresh]);

  // 刪除純文本（帶延遲刷新）
  const deletePlainText = useCallback(async (request: DeletePlainTextRequest) => {
    try {
      const response = await attachmentService.deletePlainText(request);

      if (response.code === 0) {
        // 顯示成功訊息
        message.success('純文本刪除成功，正在更新列表...');

        // 延遲刷新資料
        delayedRefresh();
      } else {
        throw new Error(response.message || '刪除純文本失敗');
      }

      return response;
    } catch (error) {
      console.error('刪除純文本失敗:', error);
      message.error(error instanceof Error ? error.message : '刪除純文本失敗');
      throw error;
    }
  }, [attachmentService, delayedRefresh]);

  return {
    uploadFiles,
    deleteFiles,
    downloadFile,
    setWebsite,
    deleteWebsite,
    setYoutube,
    deleteYoutube,
    setPlainText,
    deletePlainText,
  };
};
