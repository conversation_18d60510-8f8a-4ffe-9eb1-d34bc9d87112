/**
 * 系統指令狀態管理
 */

import { create } from 'zustand';
import { message } from 'antd';
import { TenantsService } from '@/services/tenantsService';
import { SystemInstruction, ServiceInstruction } from '@/types/systemInstruction';

// 系統指令狀態接口
interface SysInstructionState {
  // 狀態
  isLoading: boolean;
  isSaving: boolean;
  error: string | null;
  
  // 數據
  systemInstruction: SystemInstruction;
  originalSystemInstruction: SystemInstruction; // 用於比較是否有更改
  
  // 操作
  fetchSystemInstruction: (tenantId: string) => Promise<void>;
  saveSystemInstruction: (tenantId: string) => Promise<boolean>;
  
  // 編輯操作
  setGlobalInstruction: (instruction: string) => void;
  addServiceInstruction: () => void;
  updateServiceInstruction: (index: number, instruction: Partial<ServiceInstruction>) => void;
  removeServiceInstruction: (index: number) => void;
  resetSystemInstruction: () => void;
  
  // 輔助方法
  hasChanges: () => boolean;
  clearError: () => void;
}

// 創建系統指令 store
export const useSysInstructionStore = create<SysInstructionState>((set, get) => ({
  // 初始狀態
  isLoading: false,
  isSaving: false,
  error: null,
  systemInstruction: TenantsService.createEmptySystemInstruction(),
  originalSystemInstruction: TenantsService.createEmptySystemInstruction(),
  
  // 獲取系統指令
  fetchSystemInstruction: async (tenantId: string) => {
    set({ isLoading: true, error: null });

    try {
      const response = await TenantsService.getSysInstruction({ tenant_id: tenantId });

      if (response.code === 0) {
        // 如果成功但沒有數據，使用空的系統指令對象
        const systemInstruction = response.data || TenantsService.createEmptySystemInstruction();

        // 確保 service_instructions 是數組
        if (systemInstruction.service_instructions === null || systemInstruction.service_instructions === undefined) {
          systemInstruction.service_instructions = [];
        }
        set({
          systemInstruction: systemInstruction,
          originalSystemInstruction: JSON.parse(JSON.stringify(systemInstruction)), // 深拷貝
          error: null // 清除錯誤狀態
        });

        // 如果沒有數據，顯示提示信息而不是錯誤
        if (!response.data) {
          message.info('暫無系統指令設置，您可以開始配置');
        }
      } else {
        set({ error: response.message || '獲取系統指令失敗' });
        message.error('獲取系統指令失敗: ' + response.message);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '獲取系統指令時發生錯誤';
      set({ error: errorMessage });
      message.error(errorMessage);
    } finally {
      set({ isLoading: false });
    }
  },
  
  // 保存系統指令
  saveSystemInstruction: async (tenantId: string) => {
    set({ isSaving: true, error: null });
    
    try {
      const { systemInstruction } = get();
      
      const response = await TenantsService.setSysInstruction({
        tenant_id: tenantId,
        system_instruction: systemInstruction
      });
      
      if (response.code === 0) {
        // 更新原始數據
        set({ 
          originalSystemInstruction: JSON.parse(JSON.stringify(systemInstruction)) // 深拷貝
        });
        message.success('系統指令保存成功');
        return true;
      } else {
        set({ error: response.message || '保存系統指令失敗' });
        message.error('保存系統指令失敗: ' + response.message);
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '保存系統指令時發生錯誤';
      set({ error: errorMessage });
      message.error(errorMessage);
      return false;
    } finally {
      set({ isSaving: false });
    }
  },
  
  // 設置全局指令
  setGlobalInstruction: (instruction: string) => {
    set(state => ({
      systemInstruction: {
        ...state.systemInstruction,
        system: instruction
      }
    }));
  },
  
  // 添加服務指令
  addServiceInstruction: () => {
    set(state => ({
      systemInstruction: {
        ...state.systemInstruction,
        service_instructions: [
          ...(state.systemInstruction.service_instructions || []),
          TenantsService.createEmptyServiceInstruction()
        ]
      }
    }));
  },
  
  // 更新服務指令
  updateServiceInstruction: (index: number, instruction: Partial<ServiceInstruction>) => {
    set(state => {
      const newInstructions = [...(state.systemInstruction.service_instructions || [])];
      
      if (index >= 0 && index < newInstructions.length) {
        newInstructions[index] = {
          ...newInstructions[index],
          ...instruction
        };
      }
      
      return {
        systemInstruction: {
          ...state.systemInstruction,
          service_instructions: newInstructions
        }
      };
    });
  },
  
  // 刪除服務指令
  removeServiceInstruction: (index: number) => {
    set(state => {
      const newInstructions = [...(state.systemInstruction.service_instructions || [])];
      
      if (index >= 0 && index < newInstructions.length) {
        newInstructions.splice(index, 1);
      }
      
      return {
        systemInstruction: {
          ...state.systemInstruction,
          service_instructions: newInstructions
        }
      };
    });
  },
  
  // 重置系統指令
  resetSystemInstruction: () => {
    set(state => ({
      systemInstruction: JSON.parse(JSON.stringify(state.originalSystemInstruction)) // 深拷貝
    }));
  },
  
  // 檢查是否有更改
  hasChanges: () => {
    const { systemInstruction, originalSystemInstruction } = get();
    return JSON.stringify(systemInstruction) !== JSON.stringify(originalSystemInstruction);
  },

  // 清除錯誤
  clearError: () => {
    set({ error: null });
  }
}));
