/**
 * 全局應用狀態管理
 * 使用 Zustand 管理應用程式的全局狀態
 */

import { create } from 'zustand';
import { URLParams } from '@/utils/urlParams';
import { Attachment, AttachmentType, UserInfo, WebsiteAttachment } from '@/types/attachment';
import { ChatSession, ChatMessage } from '@/types/chat';
import { AttachmentService } from '@/services/attachmentService';
import { ChatService } from '@/services/chatService';

// 數據轉換函數
const transformBackendDataToAttachments = (assets: any[]): Attachment[] => {
  const attachments: Attachment[] = [];

  assets.forEach((asset) => {
    const baseAttachment = {
      remark: asset.remark || '',
      created_at: asset.create_at || asset.created_at || new Date().toISOString(),
      user_info: asset.user_info || {
        tenant_id: asset.tenant_id || '',
        service_id: asset.service_id || '',
        user_id: asset.upload_user_id || asset.user_id || '',
      },
    };

    // 處理每種類型的內容，一個 asset 可能包含多種類型
    let hasProcessedContent = false;

    // 處理檔案內容
    if (asset.upload_files && asset.upload_files.length > 0) {
      asset.upload_files.forEach((uploadFile: any, index: number) => {
        const fileName = uploadFile.file_name || uploadFile.name || '未知檔案';
        attachments.push({
          ...baseAttachment,
          id: `${asset.id || asset.create_at}_file_${index}`,
          name: fileName,
          type: AttachmentType.FILE,
          file_path: uploadFile.access_path || '',
          file_size: uploadFile.size || 0,
          mime_type: uploadFile.content_type || '',
        });
      });
      hasProcessedContent = true;
    }

    // 處理網站內容
    if (asset.url_contents && Array.isArray(asset.url_contents) && asset.url_contents.length > 0) {
      asset.url_contents.forEach((urlContent: any, index: number) => {
        const websiteUrl = urlContent.website_url || urlContent.url || '';

        if (websiteUrl) {
          const websiteAttachment: WebsiteAttachment = {
            ...baseAttachment,
            id: `${asset.id || asset.create_at}_website_${index}`,
            name: urlContent.remark || websiteUrl,
            type: AttachmentType.WEBSITE,
            url: websiteUrl,
            remark: urlContent.remark || baseAttachment.remark || '',
          };
          attachments.push(websiteAttachment);
        }
      });
      hasProcessedContent = true;
    }

    // 處理 YouTube 內容
    if (asset.youtube_contents) {
      // 如果 youtube_contents 是數組，處理每個元素
      if (Array.isArray(asset.youtube_contents)) {
        asset.youtube_contents.forEach((youtubeContent: any, index: number) => {
          const youtubeLink = youtubeContent.youtube_link || youtubeContent.url || '';
          const remark = youtubeContent.remark || '';

          if (youtubeLink) {
            attachments.push({
              ...baseAttachment,
              id: `${asset.id || asset.create_at}_youtube_${index}`,
              name: remark || youtubeLink || 'YouTube 影片',
              type: AttachmentType.YOUTUBE,
              youtube_link: youtubeLink,
              remark: remark,
            });
          }
        });
      } else if (typeof asset.youtube_contents === 'string') {
        // 如果是字符串，直接使用
        attachments.push({
          ...baseAttachment,
          id: asset.id || asset.create_at || `asset_${Date.now()}`,
          name: asset.name || 'YouTube 影片',
          type: AttachmentType.YOUTUBE,
          youtube_link: asset.youtube_contents,
        });
      } else if (typeof asset.youtube_contents === 'object') {
        // 如果是對象，提取相關字段
        const youtubeLink = asset.youtube_contents.youtube_link || asset.youtube_contents.url || '';
        const remark = asset.youtube_contents.remark || '';

        if (youtubeLink) {
          attachments.push({
            ...baseAttachment,
            id: asset.id || asset.create_at || `asset_${Date.now()}`,
            name: remark || youtubeLink || 'YouTube 影片',
            type: AttachmentType.YOUTUBE,
            youtube_link: youtubeLink,
            remark: remark,
          });
        }
      }
      hasProcessedContent = true;
    }

    // 處理純文本內容
    if (asset.plain_text_contents) {
      // 如果 plain_text_contents 是數組，處理每個元素
      if (Array.isArray(asset.plain_text_contents)) {
        asset.plain_text_contents.forEach((textContent: any, index: number) => {
          const content = textContent.plain_text || textContent.content || textContent.text || '';
          const remark = textContent.remark || '';

          if (content) {
            attachments.push({
              ...baseAttachment,
              id: `${asset.id || asset.create_at}_text_${index}`,
              name: remark || content.substring(0, 50) + (content.length > 50 ? '...' : '') || '純文本',
              type: AttachmentType.PLAIN_TEXT,
              content: content,
              remark: remark,
            });
          }
        });
      } else if (typeof asset.plain_text_contents === 'string') {
        // 如果是字符串，直接使用
        attachments.push({
          ...baseAttachment,
          id: asset.id || asset.create_at || `asset_${Date.now()}`,
          name: asset.name || asset.plain_text_contents.substring(0, 50) + (asset.plain_text_contents.length > 50 ? '...' : '') || '純文本',
          type: AttachmentType.PLAIN_TEXT,
          content: asset.plain_text_contents,
        });
      } else if (typeof asset.plain_text_contents === 'object') {
        // 如果是對象，提取相關字段
        const content = asset.plain_text_contents.plain_text || asset.plain_text_contents.content || asset.plain_text_contents.text || '';
        const remark = asset.plain_text_contents.remark || '';

        if (content) {
          attachments.push({
            ...baseAttachment,
            id: asset.id || asset.create_at || `asset_${Date.now()}`,
            name: remark || content.substring(0, 50) + (content.length > 50 ? '...' : '') || '純文本',
            type: AttachmentType.PLAIN_TEXT,
            content: content,
            remark: remark,
          });
        }
      }
      hasProcessedContent = true;
    }

    // 如果沒有處理任何內容，記錄警告
    if (!hasProcessedContent) {
      console.warn('Asset 沒有有效內容，跳過:', asset);
    }
  });

  return attachments;
};

// 應用狀態介面
interface AppState {
  // URL 參數相關
  urlParams: URLParams;
  isParamsValid: boolean;
  paramErrors: string[];

  // 應用狀態
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;

  // 主題和 UI 狀態
  isDarkMode: boolean;
  sidebarCollapsed: boolean;

  // 附件相關
  attachments: Attachment[];
  attachmentsLoading: boolean;

  // 聊天相關
  currentSession: ChatSession | null;
  sessions: ChatSession[];
  chatLoading: boolean;

  // 用戶信息
  userInfo: UserInfo;
  
  // 操作方法
  setURLParams: (params: URLParams) => void;
  setParamValidation: (isValid: boolean, errors: string[]) => void;
  setInitialized: (initialized: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  toggleDarkMode: () => void;
  toggleSidebar: () => void;
  reset: () => void;

  // 附件相關操作
  setAttachments: (attachments: Attachment[]) => void;
  addAttachment: (attachment: Attachment) => void;
  removeAttachment: (id: string) => void;
  setAttachmentsLoading: (loading: boolean) => void;
  refreshAttachments: () => Promise<void>;

  // 聊天相關操作
  setCurrentSession: (session: ChatSession | null) => void;
  addMessageToCurrentSession: (message: ChatMessage) => void;
  updateLastMessage: (message: ChatMessage) => void;
  createNewSession: () => ChatSession;
  setChatLoading: (loading: boolean) => void;

  // 用戶信息操作
  setUserInfo: (userInfo: UserInfo) => void;
}

// 初始狀態
const initialState = {
  urlParams: {},
  isParamsValid: false,
  paramErrors: [],
  isInitialized: false,
  isLoading: false,
  error: null,
  isDarkMode: true, // 默認使用深色主題
  sidebarCollapsed: false,
  attachments: [],
  attachmentsLoading: false,
  currentSession: null,
  sessions: [],
  chatLoading: false,
  userInfo: {
    tenant_id: '',
    service_id: '',
    user_id: '',
  },
};

// 創建 Zustand store
export const useAppStore = create<AppState>((set, get) => ({
  ...initialState,

  // 設置 URL 參數
  setURLParams: (params: URLParams) => {
    set({ urlParams: params });
    
    // 同時更新 localStorage
    try {
      localStorage.setItem('kmClient_params', JSON.stringify(params));
      localStorage.setItem('kmClient_params_timestamp', Date.now().toString());
    } catch (error) {
      console.error('Failed to store params:', error);
    }
  },

  // 設置參數驗證結果
  setParamValidation: (isValid: boolean, errors: string[]) => {
    set({ 
      isParamsValid: isValid, 
      paramErrors: errors,
      error: isValid ? null : '參數驗證失敗'
    });
  },

  // 設置初始化狀態
  setInitialized: (initialized: boolean) => {
    set({ isInitialized: initialized });
  },

  // 設置載入狀態
  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  // 設置錯誤狀態
  setError: (error: string | null) => {
    set({ error });
  },

  // 切換深色模式
  toggleDarkMode: () => {
    const newDarkMode = !get().isDarkMode;
    set({ isDarkMode: newDarkMode });
    
    // 更新 HTML class
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    
    // 存儲到 localStorage
    try {
      localStorage.setItem('kmClient_darkMode', newDarkMode.toString());
    } catch (error) {
      console.error('Failed to store dark mode preference:', error);
    }
  },

  // 切換側邊欄
  toggleSidebar: () => {
    const newCollapsed = !get().sidebarCollapsed;
    set({ sidebarCollapsed: newCollapsed });

    // 存儲到 localStorage
    try {
      localStorage.setItem('kmClient_sidebarCollapsed', newCollapsed.toString());
    } catch (error) {
      console.error('Failed to store sidebar state:', error);
    }
  },

  // 重置狀態
  reset: () => {
    set(initialState);

    // 清除 localStorage
    try {
      localStorage.removeItem('kmClient_params');
      localStorage.removeItem('kmClient_params_timestamp');
    } catch (error) {
      console.error('Failed to clear localStorage:', error);
    }
  },

  // 附件相關操作
  setAttachments: (attachments: Attachment[]) => {
    set({ attachments });
  },

  addAttachment: (attachment: Attachment) => {
    set(state => ({
      attachments: [...state.attachments, attachment]
    }));
  },

  removeAttachment: (id: string) => {
    set(state => ({
      attachments: state.attachments.filter(att => att.id !== id)
    }));
  },

  setAttachmentsLoading: (loading: boolean) => {
    set({ attachmentsLoading: loading });
  },

  refreshAttachments: async () => {
    const state = get();
    if (!state.userInfo.tenant_id) {
      console.warn('無法刷新附件：缺少用戶信息');
      return;
    }

    // 防止重複調用：如果正在載入中，直接返回
    if (state.attachmentsLoading) {
      console.log('⏳ 附件正在載入中，跳過重複調用');
      return;
    }

    set({ attachmentsLoading: true });

    try {
      const request = {
        tenant_id: state.userInfo.tenant_id,
        service_id: state.userInfo.service_id || '',
        user_id: state.userInfo.user_id || '',
      };

      const response = await AttachmentService.getAssets(request);

      if (response.code === 0) {
        const assets = response.data?.assets || (response as any).assets;

        if (assets && Array.isArray(assets)) {
          const attachments = transformBackendDataToAttachments(assets);
          set({ attachments });
          console.log(`✅ 成功載入 ${attachments.length} 個附件`);
        } else {
          set({ attachments: [] });
          console.log('✅ API 調用成功，但沒有附件資料');
        }
      } else {
        console.warn('刷新附件資料失敗:', response.message);
      }
    } catch (error) {
      console.error('刷新附件資料時發生錯誤:', error);
    } finally {
      set({ attachmentsLoading: false });
    }
  },

  // 聊天相關操作
  setCurrentSession: (session: ChatSession | null) => {
    set({ currentSession: session });
  },

  addMessageToCurrentSession: (message: ChatMessage) => {
    set(state => {
      if (!state.currentSession) return state;

      return {
        currentSession: {
          ...state.currentSession,
          messages: [...state.currentSession.messages, message],
          updated_at: new Date().toISOString(),
        }
      };
    });
  },

  updateLastMessage: (message: ChatMessage) => {
    set(state => {
      if (!state.currentSession || state.currentSession.messages.length === 0) return state;

      const messages = [...state.currentSession.messages];
      messages[messages.length - 1] = message;

      return {
        currentSession: {
          ...state.currentSession,
          messages,
          updated_at: new Date().toISOString(),
        }
      };
    });
  },

  createNewSession: () => {
    const newSession: ChatSession = {
      id: ChatService.generateSessionId(),
      messages: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      name: `會話 ${new Date().toLocaleString()}`,
    };

    set(state => ({
      currentSession: newSession,
      sessions: [newSession, ...state.sessions],
    }));

    return newSession;
  },

  setChatLoading: (loading: boolean) => {
    set({ chatLoading: loading });
  },

  // 用戶信息操作
  setUserInfo: (userInfo: UserInfo) => {
    set({ userInfo });
  },
}));

// 初始化應用狀態的 Hook
export const useAppInitialization = () => {
  const store = useAppStore();

  // 從 localStorage 恢復狀態
  const restoreFromStorage = () => {
    try {
      // 恢復深色模式設置（只更新狀態，不修改 DOM，因為 main.tsx 已經處理了）
      const darkMode = localStorage.getItem('kmClient_darkMode');
      if (darkMode !== null) {
        const isDark = darkMode === 'true';
        useAppStore.setState({ isDarkMode: isDark });
        console.log('🔄 從存儲恢復主題狀態:', isDark ? '深色模式' : '淺色模式');
      }

      // 恢復側邊欄設置
      const sidebarCollapsed = localStorage.getItem('kmClient_sidebarCollapsed');
      if (sidebarCollapsed !== null) {
        const isCollapsed = sidebarCollapsed === 'true';
        useAppStore.setState({ sidebarCollapsed: isCollapsed });
      }

      // 恢復參數（如果存在且未過期）
      const storedParams = localStorage.getItem('kmClient_params');
      const timestamp = localStorage.getItem('kmClient_params_timestamp');
      
      if (storedParams && timestamp) {
        const now = Date.now();
        const storedTime = parseInt(timestamp, 10);
        const maxAge = 24 * 60 * 60 * 1000; // 24小時

        if (now - storedTime <= maxAge) {
          const params = JSON.parse(storedParams);
          store.setURLParams(params);
        } else {
          // 參數已過期，清除
          localStorage.removeItem('kmClient_params');
          localStorage.removeItem('kmClient_params_timestamp');
        }
      }
    } catch (error) {
      console.error('Failed to restore state from storage:', error);
    }
  };

  return {
    restoreFromStorage,
    ...store,
  };
};
