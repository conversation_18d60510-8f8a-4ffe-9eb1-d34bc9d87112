/**
 * 語音錄製 Hook
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { message } from 'antd';
import {
  VoiceRecordState,
  VoiceRecordStatus,
  MediaError,
  MediaErrorType,
  DEFAULT_MEDIA_CONFIG
} from '@/types/media';
import { requestMicrophonePermission, createMediaError } from '@/utils/mediaUtils';

interface UseVoiceRecorderOptions {
  maxDuration?: number;
  onRecordComplete?: (audioBlob: Blob) => void;
  onError?: (error: MediaError) => void;
}

export const useVoiceRecorder = (options: UseVoiceRecorderOptions = {}) => {
  const {
    maxDuration = DEFAULT_MEDIA_CONFIG.voice.maxDuration,
    onRecordComplete,
    onError
  } = options;

  // 錄製狀態
  const [recordState, setRecordState] = useState<VoiceRecordState>({
    status: VoiceRecordStatus.IDLE,
    duration: 0,
    volume: 0
  });

  // 引用
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  /**
   * 清理資源
   */
  const cleanup = useCallback(() => {
    // 停止錄製
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }

    // 關閉媒體流
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    // 關閉音頻上下文
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    // 清理動畫和計時器
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    // 清理音頻塊
    audioChunksRef.current = [];
  }, []);

  /**
   * 監控音量
   */
  const monitorVolume = useCallback(() => {
    if (!analyserRef.current) return;

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
    analyserRef.current.getByteFrequencyData(dataArray);

    // 計算平均音量
    const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
    const volume = Math.round((average / 255) * 100);

    setRecordState(prev => ({ ...prev, volume }));

    // 繼續監控
    animationFrameRef.current = requestAnimationFrame(monitorVolume);
  }, []);

  /**
   * 開始錄製
   */
  const startRecording = useCallback(async () => {
    try {
      console.log('🎤 開始錄製語音...');

      // 檢查瀏覽器支援
      if (!navigator.mediaDevices || !window.MediaRecorder) {
        throw createMediaError(
          MediaErrorType.RECORDING_FAILED,
          '瀏覽器不支援錄音功能'
        );
      }

      // 請求麥克風權限
      const stream = await requestMicrophonePermission();
      streamRef.current = stream;

      // 設置音頻上下文用於音量監控
      const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
      audioContextRef.current = new AudioContext();
      const source = audioContextRef.current.createMediaStreamSource(stream);
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;
      source.connect(analyserRef.current);

      // 檢查支援的音頻格式並選擇最佳格式（移除 codecs 避免後端解析問題）
      const getSupportedMimeType = (): string => {
        const types = [
          'audio/webm',
          'audio/mp4',
          'audio/ogg',
          'audio/wav'
        ];

        for (const type of types) {
          if (MediaRecorder.isTypeSupported(type)) {
            console.log('🎵 使用音頻格式:', type);
            return type;
          }
        }

        console.warn('⚠️ 沒有找到支援的音頻格式，使用默認格式');
        return 'audio/webm'; // 默認格式
      };

      const mimeType = getSupportedMimeType();

      // 創建 MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: mimeType
      });
      mediaRecorderRef.current = mediaRecorder;

      // 清空音頻塊
      audioChunksRef.current = [];

      // 設置事件處理器
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
          console.log('📦 收到音頻數據塊:', event.data.size, 'bytes');
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: mimeType });
        const audioUrl = URL.createObjectURL(audioBlob);

        console.log('🎵 音頻 Blob 創建完成:', {
          size: audioBlob.size,
          type: audioBlob.type,
          url: audioUrl
        });

        setRecordState(prev => ({
          ...prev,
          status: VoiceRecordStatus.STOPPED,
          audioBlob,
          audioUrl
        }));

        if (onRecordComplete) {
          onRecordComplete(audioBlob);
        }

        console.log('✅ 錄製完成');
      };

      mediaRecorder.onerror = (event) => {
        console.error('❌ 錄製錯誤:', event);
        const error = createMediaError(
          MediaErrorType.RECORDING_FAILED,
          '錄製過程中發生錯誤'
        );
        
        if (onError) {
          onError(error);
        }
        
        cleanup();
      };

      // 開始錄製
      mediaRecorder.start(100); // 每100ms收集一次數據

      // 設置狀態
      setRecordState({
        status: VoiceRecordStatus.RECORDING,
        duration: 0,
        volume: 0
      });

      // 開始音量監控
      monitorVolume();

      // 開始計時
      timerRef.current = setInterval(() => {
        setRecordState(prev => {
          const newDuration = prev.duration + 0.1;
          
          // 檢查是否達到最大時長
          if (newDuration >= maxDuration) {
            stopRecording();
            return prev;
          }
          
          return { ...prev, duration: newDuration };
        });
      }, 100);

      console.log('🎤 錄製已開始');

    } catch (error) {
      console.error('❌ 開始錄製失敗:', error);
      
      const mediaError = error as MediaError;
      message.error(mediaError.message);
      
      if (onError) {
        onError(mediaError);
      }
      
      cleanup();
      
      setRecordState({
        status: VoiceRecordStatus.IDLE,
        duration: 0,
        volume: 0
      });
    }
  }, [maxDuration, onRecordComplete, onError, monitorVolume, cleanup]);

  /**
   * 停止錄製
   */
  const stopRecording = useCallback(() => {
    console.log('⏹️ 停止錄製');

    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
    }

    // 清理計時器和動畫
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    // 關閉媒體流
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }

    setRecordState(prev => ({
      ...prev,
      status: VoiceRecordStatus.PROCESSING,
      volume: 0
    }));
  }, []);

  /**
   * 暫停錄製
   */
  const pauseRecording = useCallback(() => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.pause();
      setRecordState(prev => ({ ...prev, status: VoiceRecordStatus.PAUSED }));
    }
  }, []);

  /**
   * 恢復錄製
   */
  const resumeRecording = useCallback(() => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'paused') {
      mediaRecorderRef.current.resume();
      setRecordState(prev => ({ ...prev, status: VoiceRecordStatus.RECORDING }));
    }
  }, []);

  /**
   * 重置錄製
   */
  const resetRecording = useCallback(() => {
    cleanup();
    
    // 清理音頻 URL
    if (recordState.audioUrl) {
      URL.revokeObjectURL(recordState.audioUrl);
    }
    
    setRecordState({
      status: VoiceRecordStatus.IDLE,
      duration: 0,
      volume: 0
    });
  }, [cleanup, recordState.audioUrl]);

  // 組件卸載時清理資源
  useEffect(() => {
    return () => {
      cleanup();
      if (recordState.audioUrl) {
        URL.revokeObjectURL(recordState.audioUrl);
      }
    };
  }, [cleanup, recordState.audioUrl]);

  return {
    // 狀態
    recordState,
    isRecording: recordState.status === VoiceRecordStatus.RECORDING,
    isPaused: recordState.status === VoiceRecordStatus.PAUSED,
    isStopped: recordState.status === VoiceRecordStatus.STOPPED,
    isProcessing: recordState.status === VoiceRecordStatus.PROCESSING,
    canRecord: recordState.status === VoiceRecordStatus.IDLE,

    // 音頻分析器（用於波形顯示）
    analyser: analyserRef.current,

    // 方法
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    resetRecording
  };
};
