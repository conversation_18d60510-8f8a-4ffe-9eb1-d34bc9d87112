/**
 * 媒體輸入 Hook
 * 統一管理圖片和語音輸入狀態
 */

import { useState, useCallback, useRef } from 'react';
import { message } from 'antd';
import {
  MediaType,
  Base64MediaData,
  MediaUploadState,
  MediaUploadStatus,
  MediaError,
  MediaErrorType
} from '@/types/media';
import { MediaService } from '@/services/mediaService';

interface UseMediaInputOptions {
  onSuccess?: (data: Base64MediaData, type: MediaType, fileName?: string) => void;
  onError?: (error: MediaError) => void;
}

export const useMediaInput = (options: UseMediaInputOptions = {}) => {
  const { onSuccess, onError } = options;
  
  // 上傳狀態
  const [uploadState, setUploadState] = useState<MediaUploadState>({
    status: MediaUploadStatus.IDLE,
    progress: 0
  });

  // 預覽數據
  const [previewData, setPreviewData] = useState<{
    type: MediaType | null;
    url: string | null;
    file: File | null;
  }>({
    type: null,
    url: null,
    file: null
  });

  // 文件輸入引用
  const fileInputRef = useRef<HTMLInputElement>(null);

  /**
   * 重置狀態
   */
  const resetState = useCallback(() => {
    setUploadState({
      status: MediaUploadStatus.IDLE,
      progress: 0
    });
    setPreviewData({
      type: null,
      url: null,
      file: null
    });
    
    // 清理預覽 URL
    if (previewData.url) {
      URL.revokeObjectURL(previewData.url);
    }
  }, [previewData.url]);

  /**
   * 處理文件選擇
   */
  const handleFileSelect = useCallback(async (file: File) => {
    try {
      console.log('📁 選擇文件:', file.name);
      
      setUploadState({
        status: MediaUploadStatus.PROCESSING,
        progress: 0
      });

      // 驗證文件
      const validation = MediaService.validateFile(file);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // 獲取媒體類型
      const mediaType = file.type.startsWith('image/') ? 'image' : 'voice';
      
      // 創建預覽 URL
      const previewUrl = URL.createObjectURL(file);
      setPreviewData({
        type: mediaType,
        url: previewUrl,
        file
      });

      setUploadState({
        status: MediaUploadStatus.SUCCESS,
        progress: 100
      });

      console.log('✅ 文件選擇成功');

    } catch (error) {
      console.error('❌ 文件選擇失敗:', error);
      
      const errorMessage = error instanceof Error ? error.message : '文件處理失敗';
      
      setUploadState({
        status: MediaUploadStatus.ERROR,
        progress: 0,
        error: errorMessage
      });

      message.error(errorMessage);
      
      if (onError) {
        onError({
          type: MediaErrorType.PROCESSING_FAILED,
          message: errorMessage
        });
      }
    }
  }, [onError]);

  /**
   * 處理圖片上傳
   */
  const handleImageUpload = useCallback(async (file: File) => {
    try {
      setUploadState({
        status: MediaUploadStatus.UPLOADING,
        progress: 50
      });

      const result = await MediaService.processImage(file);

      setUploadState({
        status: MediaUploadStatus.SUCCESS,
        progress: 100,
        result
      });

      if (onSuccess) {
        onSuccess(result, 'image', file.name);
      }

      console.log('✅ 圖片上傳成功');

      // 成功後重置狀態，關閉預覽窗口
      resetState();

    } catch (error) {
      console.error('❌ 圖片上傳失敗:', error);

      const mediaError = error as MediaError;

      setUploadState({
        status: MediaUploadStatus.ERROR,
        progress: 0,
        error: mediaError.message
      });

      message.error(mediaError.message);

      if (onError) {
        onError(mediaError);
      }
    }
  }, [onSuccess, onError, resetState]);

  /**
   * 處理語音上傳
   */
  const handleVoiceUpload = useCallback(async (audioBlob: Blob) => {
    try {
      setUploadState({
        status: MediaUploadStatus.UPLOADING,
        progress: 50
      });

      const result = await MediaService.processRecordedAudio(audioBlob);

      setUploadState({
        status: MediaUploadStatus.SUCCESS,
        progress: 100,
        result
      });

      if (onSuccess) {
        onSuccess(result, 'voice', `voice-${Date.now()}.wav`);
      }

      console.log('✅ 語音上傳成功');

      // 成功後重置狀態，關閉預覽窗口
      resetState();

    } catch (error) {
      console.error('❌ 語音上傳失敗:', error);

      const mediaError = error as MediaError;

      setUploadState({
        status: MediaUploadStatus.ERROR,
        progress: 0,
        error: mediaError.message
      });

      message.error(mediaError.message);

      if (onError) {
        onError(mediaError);
      }
    }
  }, [onSuccess, onError, resetState]);

  /**
   * 觸發文件選擇
   */
  const triggerFileSelect = useCallback((accept?: string) => {
    if (fileInputRef.current) {
      fileInputRef.current.accept = accept || 'image/*,audio/*';
      fileInputRef.current.click();
    }
  }, []);

  /**
   * 確認發送
   */
  const confirmSend = useCallback(async () => {
    if (!previewData.file) {
      message.error('沒有選擇文件');
      return;
    }

    // 防止重複點擊 - 如果已經在上傳中，直接返回
    if (uploadState.status === MediaUploadStatus.UPLOADING) {
      console.log('⚠️ 正在上傳中，忽略重複點擊');
      return;
    }

    const mediaType = previewData.type;

    try {
      if (mediaType === 'image') {
        await handleImageUpload(previewData.file);
      } else if (mediaType === 'voice') {
        // 對於語音文件，需要轉換為 Blob
        const audioBlob = new Blob([previewData.file], { type: previewData.file.type });
        await handleVoiceUpload(audioBlob);
      }
    } catch (error) {
      console.error('❌ 媒體發送失敗:', error);
      // 錯誤處理已在各自的 handle 函數中完成
    }
  }, [previewData.file, previewData.type, handleImageUpload, handleVoiceUpload, uploadState.status]);

  /**
   * 取消操作
   */
  const cancelOperation = useCallback(() => {
    resetState();
    message.info('操作已取消');
  }, [resetState]);

  return {
    // 狀態
    uploadState,
    previewData,
    isProcessing: uploadState.status === MediaUploadStatus.PROCESSING,
    isUploading: uploadState.status === MediaUploadStatus.UPLOADING,
    hasPreview: !!previewData.file,
    
    // 方法
    handleFileSelect,
    handleImageUpload,
    handleVoiceUpload,
    triggerFileSelect,
    confirmSend,
    cancelOperation,
    resetState,
    
    // 引用
    fileInputRef
  };
};
