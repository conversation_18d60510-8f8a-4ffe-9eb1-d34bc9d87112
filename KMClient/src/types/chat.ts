/**
 * 聊天相關類型定義
 */

import { Attachment } from './attachment';

// 快速回復項目類型
export interface QuickReplyItem {
  type: 'action';
  action: {
    type: 'message' | 'postback' | 'uri';
    label: string;
    text?: string;
    data?: string;
    uri?: string;
  };
}

// 新的快速回復項目類型
export interface NewQuickReplyItem {
  title: string;
  displayText: string;
}

export interface QuickReply {
  items: QuickReplyItem[];
}

// 新的快速回復格式
export interface NewQuickReply {
  items: NewQuickReplyItem[];
}

// 基礎消息內容接口
export interface BaseMessageContent {
  type: string;
  quickReply?: QuickReply | NewQuickReply | null;
}

// 文本消息內容
export interface TextMessageContent extends BaseMessageContent {
  type: 'Text';
  text: string;
}

// 圖片消息內容
export interface ImageMessageContent extends BaseMessageContent {
  type: 'Image';
  originalContentUrl: string;
  previewImageUrl: string;
}

// 按鈕消息內容
export interface ButtonMessageContent extends BaseMessageContent {
  type: 'Template';
  altText: string;
  template: {
    type: 'buttons';
    text: string;
    actions: Array<{
      type: 'message' | 'postback' | 'uri';
      label: string;
      text?: string;
      data?: string;
      uri?: string;
    }>;
  };
}

// Flex 消息內容
export interface FlexMessageContent extends BaseMessageContent {
  type: 'Flex';
  altText: string;
  contents: any; // Flex Message 的完整結構
}

// 聯合消息內容類型
export type MessageContent = TextMessageContent | ImageMessageContent | ButtonMessageContent | FlexMessageContent;

// 解析後的消息結構
export interface ParsedMessageContent {
  contents: MessageContent[];
  hasQuickReply: boolean;
  quickReplyItems: QuickReplyItem[];
  newQuickReplyItems?: NewQuickReplyItem[];
}

// 媒體數據類型
export interface MediaData {
  type: 'image' | 'voice';
  data: string; // Base64 數據或 Data URI
  fileName?: string;
  fileSize?: number;
  mimeType?: string;
  thumbnailUrl?: string; // 縮略圖 URL（用於顯示）
}

// 聊天消息類型
export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string; // 原始內容（可能是 JSON 字符串或純文本）
  parsedContent?: ParsedMessageContent; // 解析後的結構化內容
  timestamp: string;
  attachments?: Attachment[];
  mediaData?: MediaData; // 媒體數據（圖片、語音等）
}

// 聊天會話類型
export interface ChatSession {
  id: string;
  messages: ChatMessage[];
  created_at: string;
  updated_at: string;
  name?: string;
}

// 聊天 API 請求類型 - 統一使用 omnichannel chat API
export interface OmnichannelChatRequest {
  tenant_id: string;
  question: string; // 文字內容或 Base64 字符串
  message_type?: 'text' | 'b64_image' | 'b64_voice'; // 新增：消息類型
  source: string[];
  version: string[];
  tags: string[];
  service_id: string;
  user_id: string;
  session_id: string;
  // 新增字段支持不同聊天模式
  chat_mode?: 'knowledge_base' | 'general' | 'attachment';
  attachment?: File; // 支持附件聊天
  channel?: string; // 渠道信息，默認為 'web'
}

// 會話管理 API 請求類型
export interface CreateSessionRequest {
  tenant_id: string;
  service_id: string;
  user_id: string;
  session_name?: string;
}

export interface GetSessionHistoryRequest {
  tenant_id: string;
  service_id: string;
  user_id: string;
  session_id: string;
  limit?: number;
  offset?: number;
}

export interface GetSessionsRequest {
  tenant_id: string;
  service_id: string;
  user_id: string;
  limit?: number;
  offset?: number;
}

// API 響應類型
export interface ChatResponse {
  code: number;
  message: string;
  answer?: string;
  hit?: boolean;
  rel_id?: string[];
}

export interface CreateSessionResponse {
  code: number;
  message: string;
  session_id?: string;
}

export interface GetSessionHistoryResponse {
  code: number;
  message: string;
  data?: {
    messages: ChatMessage[];
    total: number;
  };
}

export interface GetSessionsResponse {
  code: number;
  message: string;
  data?: {
    sessions: ChatSession[];
    total: number;
  };
}

// Line 聊天請求類型（保留向後兼容）
export interface LineChatRequest {
  tenant_id: string;
  question: string;
  service_id: string;
  user_id: string;
  session_id: string;
}

export interface LineChatAttachmentRequest {
  tenant_id: string;
  question: string;
  service_id: string;
  user_id: string;
  session_id: string;
  attachment: File;
}
