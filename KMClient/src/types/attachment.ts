/**
 * 附件相關類型定義
 */

// 基礎 API 響應類型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
}

// 用戶信息類型
export interface UserInfo {
  tenant_id: string;
  service_id: string;
  user_id: string;
}

// 附件類型枚舉
export enum AttachmentType {
  FILE = 'file',
  WEBSITE = 'website',
  YOUTUBE = 'youtube',
  PLAIN_TEXT = 'plain_text',
}

// 基礎附件接口
export interface BaseAttachment {
  id: string;
  type: AttachmentType;
  name: string;
  remark?: string;
  created_at: string;
  user_info: UserInfo;
}

// 檔案附件
export interface FileAttachment extends BaseAttachment {
  type: AttachmentType.FILE;
  file_path: string;
  file_size: number;
  mime_type: string;
  access_path?: string;  // 後端返回的訪問路徑，優先使用
  access_url?: string;   // 後端返回的訪問 URL，作為備用
}

// 網站附件
export interface WebsiteAttachment extends BaseAttachment {
  type: AttachmentType.WEBSITE;
  url: string;
  // 可選的處理狀態（若後端未提供，前端將不顯示狀態）
  status?: 'completed' | 'processing' | 'failed' | string;
}

// YouTube 附件
export interface YoutubeAttachment extends BaseAttachment {
  type: AttachmentType.YOUTUBE;
  youtube_link: string;
}

// 純文本附件
export interface PlainTextAttachment extends BaseAttachment {
  type: AttachmentType.PLAIN_TEXT;
  content: string;
}

// 聯合附件類型
export type Attachment = FileAttachment | WebsiteAttachment | YoutubeAttachment | PlainTextAttachment;

// 上傳檔案請求
export interface UploadFileRequest {
  file: File[];
  tenant_id: string;
  service_id: string;
  user_id?: string; // 改為可選參數
  content_type: string;
}

// 刪除檔案請求
export interface DeleteFileRequest {
  tenant_id: string;
  service_id: string;
  user_id?: string; // 改為可選參數
  file_names: string[];
}

// 下載檔案請求
export interface DownloadFileRequest {
  file_path: string;  // 只需要文件路徑，優先使用 access_path，如果為空則使用 access_url
}

// 設置網站請求
export interface SetWebsiteRequest {
  tenant_id: string;
  service_id: string;
  user_id?: string; // 改為可選參數
  web_sites: Array<{
    url: string;
    remark: string;
  }>;
}

// 刪除網站請求
export interface DeleteWebsiteRequest {
  tenant_id: string;
  service_id: string;
  user_id?: string; // 改為可選參數
  web_site: string[]; // 修正：使用 web_site 並直接傳遞 URL 字符串數組
}

// 設置 YouTube 請求
export interface SetYoutubeRequest {
  tenant_id: string;
  service_id: string;
  user_id?: string; // 改為可選參數
  YoutubeContents: Array<{
    youtube_link: string;
    remark: string;
  }>; // 修正：根據 API 錯誤信息調整
}

// 刪除 YouTube 請求
export interface DeleteYoutubeRequest {
  tenant_id: string;
  service_id: string;
  user_id?: string; // 改為可選參數
  youtube_links: string[]; // 保持原有格式，等待進一步測試
}

// 設置純文本請求
export interface SetPlainTextRequest {
  tenant_id: string;
  service_id: string;
  user_id?: string; // 改為可選參數
  content: string; // 修正：直接使用字符串
  remark?: string; // 修正：可選的備註字段
}

// 刪除純文本請求
export interface DeletePlainTextRequest {
  tenant_id: string;
  service_id: string;
  user_id?: string; // 改為可選參數
  content: string; // 修正：直接使用字符串
}

// 獲取附件資料請求
export interface GetAssetsRequest {
  tenant_id: string;
  service_id: string;
  user_id?: string; // 改為可選參數
}

// 獲取附件資料響應
export interface GetAssetsResponse {
  code: number;
  message: string;
  data?: {
    assets: any[];
  };
  assets?: any[]; // 備用格式
}

// 後端資產數據類型
export interface BackendAssetData {
  id: string;
  type: string;
  name: string;
  remark?: string;
  created_at: string;
  user_info: UserInfo;
  file_path?: string;
  file_size?: number;
  mime_type?: string;
  url?: string;
  youtube_link?: string;
  content?: string;
}
