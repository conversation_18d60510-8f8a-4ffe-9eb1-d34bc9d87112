/**
 * 配置文件類型定義
 */

// API 服務端點配置
export interface ServiceEndpoints {
  [key: string]: string;
}

// API 服務配置
export interface APIService {
  service: string;
  path: string;
  endpoints: ServiceEndpoints;
}

// API Gateway 配置
export interface APIGateway {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
}

// API 配置
export interface APIConfig {
  gateway: APIGateway;
  services: {
    tenants: APIService;
    ams: APIService;
    omnichannel: APIService;
    line: APIService;
    km: APIService;
  };
}

// 功能配置
export interface FeatureConfig {
  systemInstructions: {
    enabled: boolean;
    directAccess: boolean;
    autoSave: boolean;
  };
  chat: {
    enabled: boolean;
    maxHistory: number;
    autoSave: boolean;
  };
  attachments: {
    enabled: boolean;
    maxFileSize: string;
    allowedTypes: string[];
  };
  hotReload: {
    enabled: boolean;
    interval: number;
  };
}

// UI 配置
export interface UIConfig {
  theme: {
    default: 'light' | 'dark';
    allowToggle: boolean;
  };
  layout: {
    sidebarWidth: number;
    sidebarCollapsible: boolean;
  };
  animations: {
    enabled: boolean;
    duration: number;
  };
}

// 安全配置
export interface SecurityConfig {
  validateParams: boolean;
  paramExpiry: number;
  allowedOrigins: string[];
}

// 日誌配置
export interface LoggingConfig {
  level: 'debug' | 'info' | 'warn' | 'error';
  enableConsole: boolean;
  enableRemote: boolean;
}

// 應用程式配置
export interface AppConfig {
  name: string;
  version: string;
  description: string;
  environment: 'development' | 'staging' | 'production';
}

// 頭像配置
export interface AvatarConfig {
  appId: string;
  appSecret: string;
  baseUrl: string;
  endpoint: string;
  allowedFormats: string[];
  maxFileSize: string;
}

// 完整配置結構
export interface Config {
  app: AppConfig;
  api: APIConfig;
  features: FeatureConfig;
  ui: UIConfig;
  security: SecurityConfig;
  logging: LoggingConfig;
  avatar?: AvatarConfig;
}

// 配置載入狀態
export interface ConfigState {
  config: Config | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: number | null;
}

// 配置更新事件
export interface ConfigUpdateEvent {
  type: 'config-updated';
  config: Config;
  timestamp: number;
}
