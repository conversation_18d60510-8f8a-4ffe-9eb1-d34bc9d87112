/**
 * 系統指令相關類型定義
 */

// 服務指令類型
export interface ServiceInstruction {
  service_id: string;
  channel: string;
  sys_instruction: string;
}

// 系統指令類型
export interface SystemInstruction {
  system: string;
  service_instructions: ServiceInstruction[] | null;
}

// 獲取系統指令請求
export interface GetSysInstructionRequest {
  tenant_id: string;
}

// 獲取系統指令響應
export interface GetSysInstructionResponse {
  code: number;
  message: string;
  data?: SystemInstruction;
}

// 設置系統指令請求
export interface SetSysInstructionRequest {
  tenant_id: string;
  system_instruction: SystemInstruction;
}

// 設置系統指令響應
export interface SetSysInstructionResponse {
  code: number;
  message: string;
}
