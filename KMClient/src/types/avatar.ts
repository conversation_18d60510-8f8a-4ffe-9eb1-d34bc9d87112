/**
 * 頭像相關類型定義
 */

/**
 * 頭像配置介面
 */
export interface AvatarConfig {
  appId: string;
  appSecret: string;
  baseUrl: string;
  endpoint: string;
  allowedFormats: string[];
  maxFileSize: string;
}

/**
 * 更新頭像請求參數
 */
export interface UpdateAvatarRequest {
  serviceNumberId: string;
  file?: File;
}

/**
 * 更新頭像響應
 */
export interface UpdateAvatarResponse {
  success: boolean;
  message: string;
  data?: {
    serviceNumberId: string;
    name?: string;
    avatarUrl?: string;
    updatedAt: string;
  };
}

/**
 * API 響應基礎結構
 */
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

/**
 * 加密參數介面
 */
export interface EncryptionParams {
  appId: string;
  appSecret: string;
  nonce: string;
  data: Record<string, any>;
}

/**
 * 頭像上傳狀態
 */
export enum AvatarUploadStatus {
  IDLE = 'idle',
  UPLOADING = 'uploading',
  SUCCESS = 'success',
  ERROR = 'error'
}

/**
 * 頭像上傳狀態介面
 */
export interface AvatarUploadState {
  status: AvatarUploadStatus;
  progress: number;
  error?: string;
  avatarUrl?: string;
}

/**
 * 檔案驗證結果
 */
export interface FileValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * 頭像設置組件 Props
 */
export interface AvatarSettingsPanelProps {
  className?: string;
  onAvatarUpdate?: (avatarUrl: string) => void;
  onError?: (error: string) => void;
}
