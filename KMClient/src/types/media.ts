/**
 * 媒體處理相關類型定義
 */

// 支援的媒體類型
export type MediaType = 'image' | 'voice';

// 支援的圖片格式
export type ImageFormat = 'png' | 'jpg' | 'jpeg' | 'gif' | 'webp';

// 支援的音頻格式
export type AudioFormat = 'mp3' | 'wav' | 'webm' | 'ogg' | 'm4a';

// 媒體文件信息
export interface MediaFileInfo {
  file: File;
  type: MediaType;
  format: string;
  size: number;
  duration?: number; // 音頻時長（秒）
  width?: number; // 圖片寬度
  height?: number; // 圖片高度
}

// Base64 媒體數據
export interface Base64MediaData {
  data: string; // 完整的 Data URI 格式 (data:mime/type;base64,...)
  rawBase64: string; // 純 Base64 字符串（不含前綴）
  mimeType: string; // MIME 類型
  size: number; // 原始文件大小
  type: MediaType;
}

// 圖片處理選項
export interface ImageProcessOptions {
  maxWidth?: number; // 最大寬度
  maxHeight?: number; // 最大高度
  quality?: number; // 壓縮品質 (0-1)
  format?: ImageFormat; // 輸出格式
}

// 音頻錄製選項
export interface AudioRecordOptions {
  maxDuration?: number; // 最大錄製時長（秒）
  sampleRate?: number; // 採樣率
  channels?: number; // 聲道數
  format?: AudioFormat; // 錄製格式
}

// 媒體上傳狀態
export enum MediaUploadStatus {
  IDLE = 'idle',
  PROCESSING = 'processing',
  UPLOADING = 'uploading',
  SUCCESS = 'success',
  ERROR = 'error'
}

// 媒體上傳狀態信息
export interface MediaUploadState {
  status: MediaUploadStatus;
  progress: number; // 0-100
  error?: string;
  result?: Base64MediaData;
}

// 語音錄製狀態
export enum VoiceRecordStatus {
  IDLE = 'idle',
  RECORDING = 'recording',
  PAUSED = 'paused',
  STOPPED = 'stopped',
  PROCESSING = 'processing'
}

// 語音錄製狀態信息
export interface VoiceRecordState {
  status: VoiceRecordStatus;
  duration: number; // 當前錄製時長（秒）
  volume: number; // 當前音量 (0-100)
  audioBlob?: Blob; // 錄製的音頻數據
  audioUrl?: string; // 音頻播放 URL
}

// 媒體驗證結果
export interface MediaValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
}

// 媒體處理錯誤類型
export enum MediaErrorType {
  PERMISSION_DENIED = 'permission_denied',
  FILE_TOO_LARGE = 'file_too_large',
  INVALID_FORMAT = 'invalid_format',
  PROCESSING_FAILED = 'processing_failed',
  UPLOAD_FAILED = 'upload_failed',
  RECORDING_FAILED = 'recording_failed'
}

// 媒體處理錯誤
export interface MediaError {
  type: MediaErrorType;
  message: string;
  details?: any;
}

// 媒體配置
export interface MediaConfig {
  image: {
    maxSize: number; // 最大文件大小（字節）
    maxWidth: number; // 最大寬度
    maxHeight: number; // 最大高度
    allowedFormats: ImageFormat[];
    defaultQuality: number; // 默認壓縮品質
  };
  voice: {
    maxSize: number; // 最大文件大小（字節）
    maxDuration: number; // 最大錄製時長（秒）
    allowedFormats: AudioFormat[];
    defaultSampleRate: number; // 默認採樣率
  };
}

// 默認媒體配置
export const DEFAULT_MEDIA_CONFIG: MediaConfig = {
  image: {
    maxSize: 5 * 1024 * 1024, // 5MB
    maxWidth: 1920,
    maxHeight: 1080,
    allowedFormats: ['png', 'jpg', 'jpeg', 'gif', 'webp'] as ImageFormat[],
    defaultQuality: 0.8
  },
  voice: {
    maxSize: 10 * 1024 * 1024, // 10MB
    maxDuration: 60, // 60秒
    allowedFormats: ['mp3', 'wav', 'webm', 'ogg'] as AudioFormat[],
    defaultSampleRate: 44100
  }
};
