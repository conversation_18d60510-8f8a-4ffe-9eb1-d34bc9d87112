/**
 * 媒體處理服務
 */

import {
  MediaFileInfo,
  Base64MediaData,
  ImageProcessOptions,
  AudioRecordOptions,
  MediaValidationResult,
  MediaError,
  MediaErrorType,
  MediaConfig,
  DEFAULT_MEDIA_CONFIG
} from '@/types/media';

import {
  validateMediaFile,
  fileToBase64,
  blobToBase64,
  compressImage,
  getImageDimensions,
  getAudioDuration,
  getMediaType,
  getFileFormat,
  createMediaError,
  createDataURI,
  requestMicrophonePermission
} from '@/utils/mediaUtils';

/**
 * 媒體服務類
 */
export class MediaService {
  private static config: MediaConfig = DEFAULT_MEDIA_CONFIG;

  /**
   * 設定媒體配置
   */
  static setConfig(config: Partial<MediaConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 獲取當前配置
   */
  static getConfig(): MediaConfig {
    return this.config;
  }

  /**
   * 處理圖片文件
   */
  static async processImage(
    file: File,
    options: ImageProcessOptions = {}
  ): Promise<Base64MediaData> {
    try {
      console.log('🖼️ 開始處理圖片:', file.name);

      // 驗證文件
      const validation = validateMediaFile(file, this.config);
      if (!validation.isValid) {
        throw createMediaError(
          MediaErrorType.INVALID_FORMAT,
          validation.error || '圖片驗證失敗'
        );
      }

      // 獲取圖片尺寸
      const dimensions = await getImageDimensions(file);
      console.log('📐 圖片尺寸:', dimensions);

      // 判斷是否需要壓縮
      const needCompress = 
        dimensions.width > this.config.image.maxWidth ||
        dimensions.height > this.config.image.maxHeight ||
        file.size > this.config.image.maxSize * 0.8; // 80% 閾值

      let processedFile: File | Blob = file;

      if (needCompress) {
        console.log('🗜️ 開始壓縮圖片...');
        processedFile = await compressImage(file, {
          maxWidth: this.config.image.maxWidth,
          maxHeight: this.config.image.maxHeight,
          quality: this.config.image.defaultQuality,
          ...options
        });
        console.log('✅ 圖片壓縮完成，大小:', processedFile.size);
      }

      // 轉換為 Base64 和 Data URI
      const rawBase64 = await (processedFile instanceof File
        ? fileToBase64(processedFile)
        : blobToBase64(processedFile));

      // 創建完整的 Data URI
      const dataURI = createDataURI(rawBase64, file.type);

      const result: Base64MediaData = {
        data: dataURI, // 完整的 Data URI 格式
        rawBase64: rawBase64, // 純 Base64 字符串
        mimeType: file.type,
        size: processedFile.size,
        type: 'image'
      };

      console.log('✅ 圖片處理完成');
      return result;

    } catch (error) {
      console.error('❌ 圖片處理失敗:', error);
      
      if (error instanceof Error && 'type' in error) {
        throw error as MediaError;
      }
      
      throw createMediaError(
        MediaErrorType.PROCESSING_FAILED,
        '圖片處理失敗',
        error
      );
    }
  }

  /**
   * 處理音頻文件
   */
  static async processAudio(file: File): Promise<Base64MediaData> {
    try {
      console.log('🎵 開始處理音頻:', file.name);

      // 驗證文件
      const validation = validateMediaFile(file, this.config);
      if (!validation.isValid) {
        throw createMediaError(
          MediaErrorType.INVALID_FORMAT,
          validation.error || '音頻驗證失敗'
        );
      }

      // 獲取音頻時長
      const duration = await getAudioDuration(file);
      console.log('⏱️ 音頻時長:', duration, '秒');

      // 檢查時長限制
      if (duration > this.config.voice.maxDuration) {
        throw createMediaError(
          MediaErrorType.INVALID_FORMAT,
          `音頻時長超過限制（最大 ${this.config.voice.maxDuration} 秒）`
        );
      }

      // 轉換為 Base64 和 Data URI
      const rawBase64 = await fileToBase64(file);

      // 創建完整的 Data URI
      const dataURI = createDataURI(rawBase64, file.type);

      const result: Base64MediaData = {
        data: dataURI, // 完整的 Data URI 格式
        rawBase64: rawBase64, // 純 Base64 字符串
        mimeType: file.type,
        size: file.size,
        type: 'voice'
      };

      console.log('✅ 音頻處理完成');
      return result;

    } catch (error) {
      console.error('❌ 音頻處理失敗:', error);
      
      if (error instanceof Error && 'type' in error) {
        throw error as MediaError;
      }
      
      throw createMediaError(
        MediaErrorType.PROCESSING_FAILED,
        '音頻處理失敗',
        error
      );
    }
  }

  /**
   * 處理錄製的音頻 Blob
   */
  static async processRecordedAudio(
    audioBlob: Blob,
    _options: AudioRecordOptions = {}
  ): Promise<Base64MediaData> {
    try {
      console.log('🎤 開始處理錄製音頻');

      // 檢查文件大小
      if (audioBlob.size > this.config.voice.maxSize) {
        throw createMediaError(
          MediaErrorType.FILE_TOO_LARGE,
          `錄音文件過大（最大 ${this.config.voice.maxSize / 1024 / 1024}MB）`
        );
      }

      // 轉換為 Base64 和 Data URI
      const rawBase64 = await blobToBase64(audioBlob);
      const mimeType = audioBlob.type || 'audio/webm';

      // 創建完整的 Data URI
      const dataURI = createDataURI(rawBase64, mimeType);

      const result: Base64MediaData = {
        data: dataURI, // 完整的 Data URI 格式
        rawBase64: rawBase64, // 純 Base64 字符串
        mimeType: mimeType,
        size: audioBlob.size,
        type: 'voice'
      };

      console.log('✅ 錄製音頻處理完成');
      return result;

    } catch (error) {
      console.error('❌ 錄製音頻處理失敗:', error);
      
      if (error instanceof Error && 'type' in error) {
        throw error as MediaError;
      }
      
      throw createMediaError(
        MediaErrorType.PROCESSING_FAILED,
        '錄製音頻處理失敗',
        error
      );
    }
  }

  /**
   * 獲取媒體文件信息
   */
  static async getMediaFileInfo(file: File): Promise<MediaFileInfo> {
    const mediaType = getMediaType(file);
    if (!mediaType) {
      throw createMediaError(
        MediaErrorType.INVALID_FORMAT,
        '不支援的媒體類型'
      );
    }

    const info: MediaFileInfo = {
      file,
      type: mediaType,
      format: getFileFormat(file),
      size: file.size
    };

    try {
      if (mediaType === 'image') {
        const dimensions = await getImageDimensions(file);
        info.width = dimensions.width;
        info.height = dimensions.height;
      } else if (mediaType === 'voice') {
        info.duration = await getAudioDuration(file);
      }
    } catch (error) {
      console.warn('獲取媒體文件詳細信息失敗:', error);
    }

    return info;
  }

  /**
   * 驗證媒體文件
   */
  static validateFile(file: File): MediaValidationResult {
    return validateMediaFile(file, this.config);
  }

  /**
   * 檢查瀏覽器媒體支援
   */
  static checkBrowserSupport() {
    const support = {
      getUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
      mediaRecorder: !!(window.MediaRecorder),
      audioContext: !!(window.AudioContext || (window as any).webkitAudioContext),
      fileReader: !!(window.FileReader),
      canvas: !!(document.createElement('canvas').getContext)
    };

    console.log('🔍 瀏覽器媒體支援檢查:', support);
    return support;
  }

  /**
   * 請求媒體權限
   */
  static async requestPermissions(): Promise<{ audio: boolean; camera?: boolean }> {
    const permissions = { audio: false, camera: false };

    try {
      // 請求麥克風權限
      const stream = await requestMicrophonePermission();
      permissions.audio = true;
      stream.getTracks().forEach(track => track.stop());
    } catch (error) {
      console.warn('麥克風權限請求失敗:', error);
    }

    return permissions;
  }
}
