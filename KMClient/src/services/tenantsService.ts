/**
 * 租戶服務 - 系統指令管理
 */

import { apiService } from './apiService';
import { 
  GetSysInstructionRequest, 
  GetSysInstructionResponse, 
  SetSysInstructionRequest, 
  SetSysInstructionResponse,
  SystemInstruction,
  ServiceInstruction
} from '@/types/systemInstruction';

// 系統指令服務類
export class TenantsService {
  /**
   * 獲取系統指令
   */
  static async getSysInstruction(request: GetSysInstructionRequest): Promise<GetSysInstructionResponse> {
    try {
      // 添加管理員標頭
      const headers = {
        'tenant_id': 'admin',
        'app_key': 'a~1d@3m$',
        'Content-Type': 'application/json'
      };
      
      const response = await apiService.post<SystemInstruction>(
        'tenants',
        'getSysInstruction',
        request,
        { headers }
      );
      
      return {
        code: response.code,
        message: response.message,
        data: response.data
      };
    } catch (error) {
      console.error('Get system instruction error:', error);
      return {
        code: -1,
        message: error instanceof Error ? error.message : '獲取系統指令失敗'
      };
    }
  }

  /**
   * 設置系統指令
   */
  static async setSysInstruction(request: SetSysInstructionRequest): Promise<SetSysInstructionResponse> {
    try {
      // 添加管理員標頭
      const headers = {
        'tenant_id': 'admin',
        'app_key': 'a~1d@3m$',
        'Content-Type': 'application/json'
      };
      
      const response = await apiService.post<any>(
        'tenants',
        'setSysInstruction',
        request,
        { headers }
      );
      
      return {
        code: response.code,
        message: response.message
      };
    } catch (error) {
      console.error('Set system instruction error:', error);
      return {
        code: -1,
        message: error instanceof Error ? error.message : '設置系統指令失敗'
      };
    }
  }

  /**
   * 創建空的系統指令對象
   */
  static createEmptySystemInstruction(): SystemInstruction {
    return {
      system: '',
      service_instructions: []
    };
  }

  /**
   * 創建新的服務指令
   */
  static createEmptyServiceInstruction(serviceId: string = '', channel: string = 'web'): ServiceInstruction {
    return {
      service_id: serviceId,
      channel: channel,
      sys_instruction: ''
    };
  }
}

/**
 * 系統指令服務 Hook
 */
export const useTenantsService = () => {
  return {
    getSysInstruction: TenantsService.getSysInstruction,
    setSysInstruction: TenantsService.setSysInstruction,
    createEmptySystemInstruction: TenantsService.createEmptySystemInstruction,
    createEmptyServiceInstruction: TenantsService.createEmptyServiceInstruction
  };
};
