/**
 * API 服務層
 * 統一管理所有 API 請求，使用配置文件中的設置
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { configService } from './configService';
import { Config } from '@/types/config';

// API 響應介面
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
  timestamp?: number;
}

// API 錯誤介面
export interface ApiError {
  code: number;
  message: string;
  details?: any;
}

// API 服務類
class ApiService {
  private axiosInstance: AxiosInstance;
  private config: Config;

  constructor() {
    // 始終使用當前配置（可能是默認配置或已載入的配置）
    this.config = configService.getConfig();
    console.log('🔄 ApiService 初始化，當前 baseURL:', this.config.api.gateway.baseUrl);
    console.log('📋 配置是否已載入:', configService.isConfigLoaded());

    this.axiosInstance = this.createAxiosInstance();

    // 嘗試確保配置已載入，如果載入了新配置則更新
    configService.ensureConfigLoaded().then((loadedConfig) => {
      if (loadedConfig !== this.config) {
        console.log('🔄 ApiService 使用載入的配置更新:', {
          oldBaseURL: this.config.api.gateway.baseUrl,
          newBaseURL: loadedConfig.api.gateway.baseUrl
        });
        this.config = loadedConfig;
        this.updateAxiosInstance();
      }
    }).catch(error => {
      console.error('❌ ApiService 配置載入失敗:', error);
    });

    // 監聽配置更新
    configService.addListener((newConfig) => {
      console.log('🔄 ApiService 收到配置更新:', {
        oldBaseURL: this.config.api.gateway.baseUrl,
        newBaseURL: newConfig.api.gateway.baseUrl
      });
      this.config = newConfig;
      this.updateAxiosInstance();
    });
  }

  /**
   * 確保配置已載入後再執行請求
   */
  private async ensureConfigLoaded(): Promise<void> {
    if (!configService.isConfigLoaded()) {
      console.log('⏳ 等待配置載入完成...');
      const loadedConfig = await configService.ensureConfigLoaded();
      if (loadedConfig !== this.config) {
        console.log('🔄 ApiService 在請求前更新配置:', {
          oldBaseURL: this.config.api.gateway.baseUrl,
          newBaseURL: loadedConfig.api.gateway.baseUrl
        });
        this.config = loadedConfig;
        this.updateAxiosInstance();
      }
    }
  }



  /**
   * 創建 Axios 實例
   */
  private createAxiosInstance(): AxiosInstance {
    const instance = axios.create({
      baseURL: this.config.api.gateway.baseUrl,
      timeout: this.config.api.gateway.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 請求攔截器
    instance.interceptors.request.use(
      (config) => {
        // 只對 GET 請求添加時間戳防止緩存，POST 請求不需要
        if (config.method?.toLowerCase() === 'get') {
          if (config.params) {
            config.params._t = Date.now();
          } else {
            config.params = { _t: Date.now() };
          }
        }

        const fullUrl = config.baseURL ? `${config.baseURL}${config.url}` : config.url;
        console.log(`🌐 API Request: ${config.method?.toUpperCase()} ${fullUrl}`, {
          baseURL: config.baseURL,
          url: config.url,
          data: config.data
        });
        return config;
      },
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // 響應攔截器
    instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        console.log(`✅ API Response: ${response.config.url}`, {
          status: response.status,
          statusText: response.statusText,
          data: response.data
        });
        return response;
      },
      async (error) => {
        const errorInfo = {
          url: error.config?.url,
          method: error.config?.method,
          status: error.response?.status,
          statusText: error.response?.statusText,
          code: error.code,
          message: error.message
        };

        console.error('❌ API Response Error:', errorInfo);

        // 特殊處理 CORS 和 503 錯誤
        if (error.response?.status === 503) {
          console.warn('⚠️ 檢測到 503 服務不可用錯誤，可能是服務器過載');
        }

        if (error.message && error.message.includes('CORS')) {
          console.error('🚫 CORS 錯誤：可能是代理配置問題或服務器 CORS 設置問題');
        }

        // 重試邏輯
        if (this.shouldRetry(error)) {
          return this.retryRequest(error);
        }

        return Promise.reject(this.formatError(error));
      }
    );

    return instance;
  }

  /**
   * 更新 Axios 實例配置
   */
  private updateAxiosInstance(): void {
    const oldBaseURL = this.axiosInstance.defaults.baseURL;
    const newBaseURL = this.config.api.gateway.baseUrl;

    this.axiosInstance.defaults.baseURL = newBaseURL;
    this.axiosInstance.defaults.timeout = this.config.api.gateway.timeout;

    console.log('🔄 API service updated with new config:', {
      oldBaseURL,
      newBaseURL,
      timeout: this.config.api.gateway.timeout
    });
  }

  /**
   * 判斷是否應該重試
   */
  private shouldRetry(error: any): boolean {
    const retryAttempts = this.config.api.gateway.retryAttempts;
    const currentAttempt = error.config?.__retryCount || 0;

    // 檢查是否為下載請求 - 下載請求不應該重試
    if (error.config?.__isDownloadRequest) {
      console.log('🚫 下載請求不進行重試');
      return false;
    }

    // 檢查是否為 CORS 錯誤 - CORS 錯誤重試沒有意義
    if (error.message && error.message.includes('CORS')) {
      console.log('🚫 CORS 錯誤不進行重試');
      return false;
    }

    // 檢查是否為 503 服務不可用錯誤 - 應該重試
    const is503Error = error.response?.status === 503;

    // 檢查是否為網絡錯誤或服務器錯誤
    const isRetryableError = (
      error.code === 'ECONNABORTED' ||
      error.response?.status >= 500 ||
      !error.response ||
      is503Error
    );

    const shouldRetry = currentAttempt < retryAttempts && isRetryableError;

    if (shouldRetry) {
      console.log(`🔄 準備重試請求 (${currentAttempt + 1}/${retryAttempts})，錯誤類型:`, {
        status: error.response?.status,
        code: error.code,
        message: error.message
      });
    }

    return shouldRetry;
  }

  /**
   * 重試請求
   */
  private async retryRequest(error: any): Promise<any> {
    const config = error.config;
    config.__retryCount = (config.__retryCount || 0) + 1;

    // 使用指數退避算法計算延遲時間
    const baseDelay = this.config.api.gateway.retryDelay;
    const delay = baseDelay * Math.pow(2, config.__retryCount - 1);

    console.log(`🔄 重試請求 (第 ${config.__retryCount}/${this.config.api.gateway.retryAttempts} 次) 延遲 ${delay}ms`, {
      url: config.url,
      method: config.method,
      previousError: error.response?.status || error.code
    });

    await new Promise(resolve => setTimeout(resolve, delay));
    return this.axiosInstance(config);
  }

  /**
   * 格式化錯誤
   */
  private formatError(error: any): ApiError {
    if (error.response) {
      const status = error.response.status;
      let message = error.response.data?.message || error.message;

      // 針對特定狀態碼提供更友好的錯誤信息
      switch (status) {
        case 503:
          message = '服務暫時不可用，請稍後重試';
          break;
        case 502:
          message = '網關錯誤，請檢查網絡連接';
          break;
        case 504:
          message = '請求超時，請稍後重試';
          break;
        case 429:
          message = '請求過於頻繁，請稍後重試';
          break;
      }

      return {
        code: status,
        message,
        details: error.response.data,
      };
    } else if (error.request) {
      // 檢查是否為 CORS 錯誤
      if (error.message && error.message.includes('CORS')) {
        return {
          code: 0,
          message: 'CORS 錯誤：無法訪問服務器，請檢查代理配置',
          details: error.request,
        };
      }

      return {
        code: 0,
        message: '網路連接失敗，請檢查網絡連接',
        details: error.request,
      };
    } else {
      return {
        code: -1,
        message: error.message || '未知錯誤',
        details: error,
      };
    }
  }

  /**
   * 構建服務 URL
   */
  private buildServiceUrl(serviceName: keyof Config['api']['services'], endpoint: string): string {
    const service = this.config.api.services[serviceName];

    // 如果端點在配置的 endpoints 中定義，使用配置的路徑
    if (service.endpoints && service.endpoints[endpoint]) {
      return `/svc/${service.service}/${service.path}${service.endpoints[endpoint]}`;
    }

    // 否則，假設端點名稱就是路徑（向後兼容）
    const endpointPath = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `/svc/${service.service}/${service.path}${endpointPath}`;
  }

  /**
   * GET 請求
   */
  async get<T = any>(
    serviceName: keyof Config['api']['services'],
    endpoint: string,
    params?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    // 確保配置已載入
    await this.ensureConfigLoaded();

    const url = this.buildServiceUrl(serviceName, endpoint);
    console.log('📡 GET 請求:', url, '使用 baseURL:', this.config.api.gateway.baseUrl);

    const response = await this.axiosInstance.get<ApiResponse<T>>(url, {
      params,
      ...config,
    });
    return response.data;
  }

  /**
   * POST 請求
   */
  async post<T = any>(
    serviceName: keyof Config['api']['services'],
    endpoint: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    // 確保配置已載入
    await this.ensureConfigLoaded();

    const url = this.buildServiceUrl(serviceName, endpoint);
    console.log('📡 POST 請求:', url, '使用 baseURL:', this.config.api.gateway.baseUrl);

    const response = await this.axiosInstance.post<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  /**
   * PUT 請求
   */
  async put<T = any>(
    serviceName: keyof Config['api']['services'],
    endpoint: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const url = this.buildServiceUrl(serviceName, endpoint);
    const response = await this.axiosInstance.put<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  /**
   * DELETE 請求
   */
  async delete<T = any>(
    serviceName: keyof Config['api']['services'],
    endpoint: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const url = this.buildServiceUrl(serviceName, endpoint);
    const response = await this.axiosInstance.delete<ApiResponse<T>>(url, config);
    return response.data;
  }

  /**
   * 上傳文件
   */
  async upload<T = any>(
    serviceName: keyof Config['api']['services'],
    endpoint: string,
    formData: FormData,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<T>> {
    // 確保配置已載入
    await this.ensureConfigLoaded();

    const url = this.buildServiceUrl(serviceName, endpoint);

    console.log('🌐 API Upload 請求詳情:', {
      url,
      serviceName,
      endpoint,
      baseURL: this.config.api.gateway.baseUrl,
      formDataEntries: Array.from(formData.entries()).map(([key, value]) => ({
        key,
        valueType: value instanceof File ? 'File' : typeof value,
        fileName: value instanceof File ? value.name : undefined,
        fileSize: value instanceof File ? value.size : undefined,
        value: value instanceof File ? `File(${value.name})` : value
      }))
    });

    try {
      const response = await this.axiosInstance.post<ApiResponse<T>>(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            onProgress(progress);
            console.log(`📊 上傳進度: ${progress}%`);
          }
        },
        // 增加超時時間以處理大文件
        timeout: 60000, // 60 秒
      });

      console.log('✅ API Upload 響應成功:', {
        status: response.status,
        statusText: response.statusText,
        data: response.data
      });

      return response.data;
    } catch (error: any) {
      console.error('❌ API Upload 請求失敗:', {
        url,
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        requestConfig: {
          method: error.config?.method,
          url: error.config?.url,
          headers: error.config?.headers
        }
      });

      // 重新拋出錯誤以便上層處理
      throw error;
    }
  }

  /**
   * 下載文件 - 使用 POST 方法，使用配置的 axios 實例確保配置熱重載生效
   */
  async download(
    serviceName: keyof Config['api']['services'],
    endpoint: string,
    data?: any
  ): Promise<Blob> {
    // 確保配置已載入
    await this.ensureConfigLoaded();

    const url = this.buildServiceUrl(serviceName, endpoint);
    console.log('📥 下載文件 API 調用:', url);
    console.log('📋 請求參數:', JSON.stringify(data, null, 2));
    console.log('🌐 當前 baseURL:', this.config.api.gateway.baseUrl);

    try {
      // 使用配置的 axios 實例，確保配置熱重載生效
      const response = await this.axiosInstance.post(url, data, {
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 120000, // 2 分鐘超時
      });

      console.log('📦 下載響應:', response.status, response.statusText);
      console.log('📊 文件大小:', response.data.size, 'bytes');
      console.log('📄 Content-Type:', response.headers['content-type']);

      // 檢查響應的 Content-Type 來判斷是成功還是失敗
      const contentType = response.headers['content-type'] || '';

      // 如果收到 JSON 響應，可能是錯誤信息
      if (contentType.includes('application/json')) {
        try {
          const text = await response.data.text();
          const errorData = JSON.parse(text);
          console.error('📄 收到 JSON 錯誤響應:', errorData);
          throw new Error(errorData.message || '下載失敗');
        } catch (parseError) {
          console.error('📄 無法解析 JSON 錯誤響應:', parseError);
          throw new Error('服務器返回了無效的響應');
        }
      }

      // Content-Type 不是 JSON，表示這是成功的文件響應
      console.log('✅ 收到文件數據，準備觸發下載');
      console.log('📄 文件類型:', contentType);
      console.log('📊 文件大小:', response.data.size, 'bytes');

      // 檢查是否為有效的文件響應
      if (response.data.size === 0) {
        throw new Error('下載的檔案為空');
      }

      // 直接返回 Blob 數據，觸發下載
      return response.data;
    } catch (error: any) {
      console.error('❌ 下載文件失敗:', error);

      // 如果已經是我們拋出的錯誤，直接重新拋出
      if (error.message && !error.response) {
        throw error;
      }

      // 處理 axios 網路錯誤
      if (error.response) {
        const status = error.response.status;

        // 如果是 200 但有錯誤，可能是 JSON 錯誤響應
        if (status === 200 && error.response.data) {
          try {
            const text = await error.response.data.text();
            const errorData = JSON.parse(text);
            throw new Error(errorData.message || '下載失敗');
          } catch (parseError) {
            // 解析失敗，按照 HTTP 錯誤處理
          }
        }

        switch (status) {
          case 404:
            throw new Error('文件不存在，可能已被刪除');
          case 500:
            throw new Error('服務器內部錯誤，請稍後重試');
          case 403:
            throw new Error('沒有下載權限');
          default:
            throw new Error(`服務器錯誤 (${status})`);
        }
      }

      // 網路連接錯誤
      if (error.code === 'ECONNABORTED') {
        throw new Error('下載超時，請檢查網路連接');
      }

      throw new Error(`下載失敗: ${error.message || '網路連接錯誤'}`);
    }
  }

  /**
   * 獲取當前配置
   */
  getConfig(): Config {
    return this.config;
  }
}

// 延遲創建單例實例，確保配置已載入
let apiServiceInstance: ApiService | null = null;

export const getApiService = (): ApiService => {
  if (!apiServiceInstance) {
    // 等待配置載入完成後再創建 apiService
    console.log('🚀 創建 ApiService 實例...');
    apiServiceInstance = new ApiService();
  }
  return apiServiceInstance;
};

// 為了向後兼容，保留原有的導出方式
export const apiService = new Proxy({} as ApiService, {
  get(_target, prop) {
    return getApiService()[prop as keyof ApiService];
  }
});

// 導出類型和服務
export { ApiService };
