/**
 * 配置文件服務
 * 負責載入、解析和管理應用程式配置
 */

import { Config, ConfigState, ConfigUpdateEvent } from '@/types/config';

// 默認配置（作為後備）
const DEFAULT_CONFIG: Config = {
  app: {
    name: 'KM Client',
    version: '1.0.0',
    description: '知識管理客戶端',
    environment: 'development',
  },
  api: {
    gateway: {
      baseUrl: import.meta.env.VITE_API_BASE_URL || (import.meta.env.DEV ? '' : 'https://ai.aile.cloud/v2'),
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
    },
    services: {
      tenants: {
        service: 'tenants.svc',
        path: 'tenants',
        endpoints: {
          getSysInstruction: '/sys-instruction',
          updateSysInstruction: '/sys-instruction',
        },
      },
      ams: {
        service: 'ams.svc',
        path: 'attachments',
        endpoints: {
          upload: '/upload',
          list: '/list',
          delete: '/delete',
          download: '/download',
        },
      },
      omnichannel: {
        service: 'brainHub.svc',
        path: 'omnichannel',
        endpoints: {
          chat: '/chat',
          'session/create': '/session/create',
          'session/history': '/session/history',
          'session/list': '/session/list',
        },
      },
      line: {
        service: 'channelHub.svc',
        path: 'line',
        endpoints: {
          chat: '/chat',
          webhook: '/webhook',
        },
      },
      km: {
        service: 'quizto.svc',
        path: 'km',
        endpoints: {
          search: '/search',
          index: '/index',
        },
      },
    },
  },
  features: {
    systemInstructions: {
      enabled: true,
      directAccess: true,
      autoSave: true,
    },
    chat: {
      enabled: true,
      maxHistory: 100,
      autoSave: true,
    },
    attachments: {
      enabled: true,
      maxFileSize: '50MB',
      allowedTypes: ['pdf', 'doc', 'docx', 'txt', 'jpg', 'png'],
    },
    hotReload: {
      enabled: true,
      interval: 5000,
    },
  },
  ui: {
    theme: {
      default: 'dark',
      allowToggle: true,
    },
    layout: {
      sidebarWidth: 400,
      sidebarCollapsible: true,
    },
    animations: {
      enabled: true,
      duration: 300,
    },
  },
  security: {
    validateParams: true,
    paramExpiry: 86400000,
    allowedOrigins: [
      import.meta.env.VITE_APP_URL || 'http://localhost:3001',
      'https://km-client.example.com'
    ],
  },
  logging: {
    level: 'info',
    enableConsole: true,
    enableRemote: false,
  },
  avatar: {
    appId: '68c370da9674a86521491f60',
    appSecret: '6b063656066d474d',
    baseUrl: 'https://newaile.prod.aile.cloud',
    endpoint: '/job/api/platform/servicenumber/update',
    allowedFormats: ['png', 'jpg', 'jpeg', 'gif'],
    maxFileSize: '5MB'
  },
};

// 配置服務類
class ConfigService {
  private config: Config | null = null;
  private listeners: ((config: Config) => void)[] = [];
  private lastModified: number = 0;
  private hotReloadInterval: number | null = null;
  private configLoadPromise: Promise<Config> | null = null;

  /**
   * 確保配置已載入
   */
  async ensureConfigLoaded(): Promise<Config> {
    if (this.configLoadPromise) {
      return this.configLoadPromise;
    }

    if (this.config) {
      return this.config;
    }

    this.configLoadPromise = this.loadConfig();
    return this.configLoadPromise;
  }

  /**
   * 載入配置文件
   */
  async loadConfig(): Promise<Config> {
    console.log('🔄 開始載入配置文件...');

    try {
      // 檢查當前 URL 和基礎路徑
      const currentUrl = window.location.href;
      const configUrl = '/config.json';
      console.log('📍 當前頁面 URL:', currentUrl);
      console.log('📄 嘗試載入配置文件:', configUrl);

      const response = await fetch(configUrl, {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      console.log('📡 配置文件請求狀態:', response.status, response.statusText);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const configData = await response.json();
      console.log('📋 原始配置數據:', configData);

      const config = this.validateAndMergeConfig(configData);
      console.log('✅ 合併後的配置:', config);

      this.config = config;
      this.lastModified = Date.now();
      this.configLoadPromise = null; // 清除 Promise

      // 通知監聽器
      this.notifyListeners(config);

      // 啟動熱重載（如果啟用）
      if (config.features.hotReload.enabled) {
        this.startHotReload(config.features.hotReload.interval);
      }

      console.log('🎉 配置載入成功! Base URL:', config.api.gateway.baseUrl);
      return config;
    } catch (error) {
      console.error('❌ 配置載入失敗，使用預設配置:', error);
      console.error('🔧 預設配置 Base URL:', DEFAULT_CONFIG.api.gateway.baseUrl);
      console.log('💡 請檢查：');
      console.log('   1. config.json 文件是否存在於 public 目錄');
      console.log('   2. Web 服務器是否正確提供靜態文件');
      console.log('   3. 瀏覽器網路請求是否被阻擋');

      this.config = DEFAULT_CONFIG;
      this.configLoadPromise = null; // 清除 Promise
      this.notifyListeners(DEFAULT_CONFIG);
      return DEFAULT_CONFIG;
    }
  }

  /**
   * 獲取當前配置
   */
  getConfig(): Config {
    return this.config || DEFAULT_CONFIG;
  }

  /**
   * 檢查配置是否已載入（非預設配置）
   */
  isConfigLoaded(): boolean {
    return this.config !== null;
  }

  /**
   * 驗證並合併配置
   */
  private validateAndMergeConfig(configData: any): Config {
    // 深度合併配置，確保所有必要的字段都存在
    return this.deepMerge(DEFAULT_CONFIG, configData);
  }

  /**
   * 深度合併對象
   */
  private deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  /**
   * 添加配置更新監聽器
   */
  addListener(listener: (config: Config) => void): () => void {
    this.listeners.push(listener);
    
    // 返回移除監聽器的函數
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * 通知所有監聽器
   */
  private notifyListeners(config: Config): void {
    this.listeners.forEach(listener => {
      try {
        listener(config);
      } catch (error) {
        console.error('Error in config listener:', error);
      }
    });
  }

  /**
   * 啟動熱重載
   */
  private startHotReload(interval: number): void {
    if (this.hotReloadInterval) {
      clearInterval(this.hotReloadInterval);
    }

    this.hotReloadInterval = window.setInterval(async () => {
      try {
        await this.checkForConfigUpdates();
      } catch (error) {
        console.error('Hot reload check failed:', error);
      }
    }, interval);
  }

  /**
   * 檢查配置更新
   */
  private async checkForConfigUpdates(): Promise<void> {
    try {
      const response = await fetch('/config.json', {
        method: 'HEAD',
        cache: 'no-cache',
      });

      if (response.ok) {
        // const lastModified = response.headers.get('Last-Modified'); // 暫時未使用
        const currentTime = Date.now();
        
        // 簡單的時間檢查（實際應用中可能需要更精確的檢查）
        if (currentTime - this.lastModified > 1000) {
          console.log('Config file may have changed, reloading...');
          await this.loadConfig();
        }
      }
    } catch (error) {
      console.error('Failed to check config updates:', error);
    }
  }

  /**
   * 停止熱重載
   */
  stopHotReload(): void {
    if (this.hotReloadInterval) {
      clearInterval(this.hotReloadInterval);
      this.hotReloadInterval = null;
    }
  }

  /**
   * 構建 API URL
   */
  buildApiUrl(serviceName: keyof Config['api']['services'], endpoint: string): string {
    const config = this.getConfig();
    const service = config.api.services[serviceName];
    
    if (!service) {
      throw new Error(`Service ${serviceName} not found in config`);
    }

    const baseUrl = config.api.gateway.baseUrl;
    return `${baseUrl}/svc/${service.service}/${service.path}${endpoint}`;
  }

  /**
   * 獲取 API 配置
   */
  getApiConfig() {
    return this.getConfig().api;
  }

  /**
   * 獲取功能配置
   */
  getFeatureConfig() {
    return this.getConfig().features;
  }

  /**
   * 獲取 UI 配置
   */
  getUIConfig() {
    return this.getConfig().ui;
  }

  /**
   * 診斷配置載入狀態
   */
  async diagnoseConfig(): Promise<void> {
    console.log('🔍 開始配置診斷...');

    try {
      // 測試配置文件訪問
      const response = await fetch('/config.json', {
        method: 'HEAD',
        cache: 'no-cache'
      });

      if (response.ok) {
        console.log('✅ config.json 文件可訪問');
        console.log('📊 響應標頭:', Object.fromEntries(response.headers.entries()));
      } else {
        console.error('❌ config.json 文件無法訪問:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('❌ 配置文件訪問測試失敗:', error);
    }

    // 顯示當前配置狀態
    const currentConfig = this.getConfig();
    console.log('📋 當前使用的配置:');
    console.log('   Base URL:', currentConfig.api.gateway.baseUrl);
    console.log('   是否為預設配置:', currentConfig === DEFAULT_CONFIG);
    console.log('   配置載入時間:', new Date(this.lastModified || 0).toLocaleString());
  }
}

// 創建單例實例
export const configService = new ConfigService();

// 立即開始載入配置，確保在其他服務初始化前完成
configService.ensureConfigLoaded().catch(error => {
  console.error('❌ 初始配置載入失敗:', error);
});

// 全域診斷函數（可在瀏覽器控制台中使用）
if (typeof window !== 'undefined') {
  (window as any).diagnoseConfig = () => configService.diagnoseConfig();
  (window as any).reloadConfig = () => configService.loadConfig();
  (window as any).getCurrentConfig = () => configService.getConfig();
}

// 導出類型和服務
export { ConfigService };
export type { Config, ConfigState, ConfigUpdateEvent };
