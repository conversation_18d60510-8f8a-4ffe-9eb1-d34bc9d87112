/**
 * 聊天服務
 */

import { apiService } from './apiService';
import { UserInfo } from '@/types/attachment';
import {
  OmnichannelChatRequest,
  LineChatRequest,
  LineChatAttachmentRequest,
  ChatResponse,
  CreateSessionRequest,
  CreateSessionResponse,
  GetSessionHistoryRequest,
  GetSessionHistoryResponse,
  GetSessionsRequest,
  GetSessionsResponse,
} from '@/types/chat';

// 聊天服務
export class ChatService {
  /**
   * 統一聊天接口 - 支持所有聊天模式
   */
  static async omnichannelChat(request: OmnichannelChatRequest): Promise<ChatResponse> {
    try {
      if (request.attachment) {
        // 附件聊天 - 使用 FormData
        const formData = new FormData();
        formData.append('tenant_id', request.tenant_id);
        formData.append('question', request.question);
        formData.append('source', JSON.stringify(request.source));
        formData.append('version', JSON.stringify(request.version));
        formData.append('tags', JSON.stringify(request.tags));
        formData.append('service_id', request.service_id);
        formData.append('user_id', request.user_id);
        formData.append('session_id', request.session_id);
        formData.append('chat_mode', request.chat_mode || 'attachment');
        formData.append('channel', request.channel || 'web');
        formData.append('attachment', request.attachment);

        const response = await apiService.upload('omnichannel', 'chat', formData);
        return response;
      } else {
        // 純文字聊天
        const response = await apiService.post<ChatResponse>('omnichannel', 'chat', request);
        return response;
      }
    } catch (error) {
      console.error('Omnichannel chat error:', error);
      throw error;
    }
  }

  /**
   * 會話管理 - 創建新會話
   */
  static async createSession(request: CreateSessionRequest): Promise<CreateSessionResponse> {
    try {
      const response = await apiService.post<CreateSessionResponse>('omnichannel', 'session/create', request);
      return response;
    } catch (error) {
      console.error('Create session error:', error);
      throw error;
    }
  }

  /**
   * 獲取會話歷史
   */
  static async getSessionHistory(request: GetSessionHistoryRequest): Promise<GetSessionHistoryResponse> {
    try {
      const response = await apiService.post('omnichannel', 'session/history', request);
      // 直接返回 response，因為 apiService.post 已經返回正確的格式
      return response as GetSessionHistoryResponse;
    } catch (error) {
      console.error('Get session history error:', error);
      throw error;
    }
  }

  /**
   * 獲取會話列表
   */
  static async getSessions(request: GetSessionsRequest): Promise<GetSessionsResponse> {
    try {
      const response = await apiService.post('omnichannel', 'session/list', request);
      // 直接返回 response，因為 apiService.post 已經返回正確的格式
      return response as GetSessionsResponse;
    } catch (error) {
      console.error('Get sessions error:', error);
      throw error;
    }
  }

  /**
   * 保留舊方法以保持向後兼容 - Line 聊天
   */
  static async lineChat(request: LineChatRequest): Promise<ChatResponse> {
    try {
      const response = await apiService.post<ChatResponse>('line', 'chat', request);
      return response;
    } catch (error) {
      console.error('Line chat error:', error);
      throw error;
    }
  }

  /**
   * Line 聊天附件
   */
  static async lineChatWithAttachment(request: LineChatAttachmentRequest): Promise<ChatResponse> {
    try {
      const formData = new FormData();
      formData.append('tenant_id', request.tenant_id);
      formData.append('question', request.question);
      formData.append('service_id', request.service_id);
      formData.append('user_id', request.user_id);
      formData.append('session_id', request.session_id);
      formData.append('attachment', request.attachment);

      const response = await apiService.upload('line', 'chat/attachment', formData);
      return response;
    } catch (error) {
      console.error('Line chat with attachment error:', error);
      throw error;
    }
  }

  /**
   * 構建統一聊天請求 - 基於知識庫的聊天
   */
  static buildOmnichannelRequest(
    question: string,
    userInfo: UserInfo,
    sessionId: string,
    hasAttachments: boolean = false,
    messageType: 'text' | 'b64_image' | 'b64_voice' = 'text'
  ): OmnichannelChatRequest {
    return {
      tenant_id: userInfo.tenant_id,
      question,
      message_type: messageType,
      source: ['*'], // 使用所有可用的知識來源
      version: ['*'], // 使用所有版本
      tags: ['*'], // 使用所有標籤
      service_id: userInfo.service_id,
      user_id: userInfo.user_id,
      session_id: sessionId,
      chat_mode: hasAttachments ? 'knowledge_base' : 'general',
      channel: 'web',
    };
  }

  /**
   * 構建附件聊天請求
   */
  static buildOmnichannelAttachmentRequest(
    question: string,
    attachment: File,
    userInfo: UserInfo,
    sessionId: string
  ): OmnichannelChatRequest {
    return {
      tenant_id: userInfo.tenant_id,
      question,
      message_type: 'text', // 附件聊天仍使用 text 類型
      source: ['*'],
      version: ['*'],
      tags: ['*'],
      service_id: userInfo.service_id,
      user_id: userInfo.user_id,
      session_id: sessionId,
      chat_mode: 'attachment',
      attachment,
      channel: 'web',
    };
  }

  /**
   * 構建媒體聊天請求 - 圖片或語音
   */
  static buildMediaChatRequest(
    dataURI: string,
    messageType: 'b64_image' | 'b64_voice',
    userInfo: UserInfo,
    sessionId: string
  ): OmnichannelChatRequest {
    return {
      tenant_id: userInfo.tenant_id,
      question: dataURI, // 完整的 Data URI 格式作為 question
      message_type: messageType,
      source: ['*'],
      version: ['*'],
      tags: ['*'],
      service_id: userInfo.service_id,
      user_id: userInfo.user_id,
      session_id: sessionId,
      chat_mode: 'general', // 媒體聊天使用 general 模式
      channel: 'web',
    };
  }

  /**
   * 構建創建會話請求
   */
  static buildCreateSessionRequest(
    userInfo: UserInfo,
    sessionName?: string
  ): CreateSessionRequest {
    return {
      tenant_id: userInfo.tenant_id,
      service_id: userInfo.service_id,
      user_id: userInfo.user_id,
      session_name: sessionName,
    };
  }

  /**
   * 構建 Line 聊天請求（向後兼容）
   */
  static buildLineChatRequest(
    question: string,
    userInfo: UserInfo,
    sessionId: string
  ): LineChatRequest {
    return {
      tenant_id: userInfo.tenant_id,
      question,
      service_id: userInfo.service_id,
      user_id: userInfo.user_id,
      session_id: sessionId,
    };
  }

  /**
   * 構建 Line 附件聊天請求（向後兼容）
   */
  static buildLineChatAttachmentRequest(
    question: string,
    attachment: File,
    userInfo: UserInfo,
    sessionId: string
  ): LineChatAttachmentRequest {
    return {
      tenant_id: userInfo.tenant_id,
      question,
      attachment,
      service_id: userInfo.service_id,
      user_id: userInfo.user_id,
      session_id: sessionId,
    };
  }

  /**
   * 生成會話 ID
   */
  static generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成消息 ID
   */
  static generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 聊天服務 hooks
export const useChatService = () => {
  return {
    // 統一聊天接口
    omnichannelChat: ChatService.omnichannelChat,

    // 會話管理
    createSession: ChatService.createSession,
    getSessionHistory: ChatService.getSessionHistory,
    getSessions: ChatService.getSessions,

    // 請求構建器
    buildOmnichannelRequest: ChatService.buildOmnichannelRequest,
    buildOmnichannelAttachmentRequest: ChatService.buildOmnichannelAttachmentRequest,
    buildMediaChatRequest: ChatService.buildMediaChatRequest,
    buildCreateSessionRequest: ChatService.buildCreateSessionRequest,

    // 保留舊方法以保持向後兼容
    lineChat: ChatService.lineChat,
    lineChatWithAttachment: ChatService.lineChatWithAttachment,
    buildLineChatRequest: ChatService.buildLineChatRequest,
    buildLineChatAttachmentRequest: ChatService.buildLineChatAttachmentRequest,

    // 工具方法
    generateSessionId: ChatService.generateSessionId,
    generateMessageId: ChatService.generateMessageId,
  };
};
