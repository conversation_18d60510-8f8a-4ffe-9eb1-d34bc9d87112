/**
 * 頭像 API 服務
 * 處理頭像上傳和更新請求
 */

import { 
  AvatarConfig, 
  UpdateAvatarRequest, 
  UpdateAvatarResponse, 
  ApiResponse,
  FileValidationResult 
} from '@/types/avatar';
import { 
  generateHmacAuthorization, 
  generateNonce, 
  extractNonFileParams,
  validateEncryptionParams 
} from '@/utils/avatarCrypto';

/**
 * 頭像服務類
 */
export class AvatarService {
  private static config: AvatarConfig | null = null;

  /**
   * 初始化配置
   * @param config 頭像配置
   */
  static initialize(config: AvatarConfig): void {
    this.config = config;
    console.log('🎭 頭像服務已初始化', config);
  }

  /**
   * 獲取配置
   * @returns 頭像配置
   */
  private static getConfig(): AvatarConfig {
    if (!this.config) {
      throw new Error('頭像服務未初始化，請先調用 initialize 方法');
    }
    return this.config;
  }

  /**
   * 驗證檔案格式和大小
   * @param file 要驗證的檔案
   * @returns 驗證結果
   */
  static validateFile(file: File): FileValidationResult {
    const config = this.getConfig();
    
    // 檢查檔案格式
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    if (!fileExtension || !config.allowedFormats.includes(fileExtension)) {
      return {
        isValid: false,
        error: `不支援的檔案格式。僅支援：${config.allowedFormats.join(', ')}`
      };
    }

    // 檢查檔案大小
    const maxSizeInBytes = this.parseFileSize(config.maxFileSize);
    if (file.size > maxSizeInBytes) {
      return {
        isValid: false,
        error: `檔案大小超過限制（${config.maxFileSize}）`
      };
    }

    return { isValid: true };
  }

  /**
   * 解析檔案大小字符串為字節數
   * @param sizeStr 大小字符串（如 "5MB"）
   * @returns 字節數
   */
  private static parseFileSize(sizeStr: string): number {
    const units: Record<string, number> = {
      'B': 1,
      'KB': 1024,
      'MB': 1024 * 1024,
      'GB': 1024 * 1024 * 1024
    };

    const match = sizeStr.match(/^(\d+(?:\.\d+)?)\s*([A-Z]+)$/i);
    if (!match) {
      throw new Error(`無效的檔案大小格式: ${sizeStr}`);
    }

    const [, size, unit] = match;
    const multiplier = units[unit.toUpperCase()];
    if (!multiplier) {
      throw new Error(`不支援的檔案大小單位: ${unit}`);
    }

    return parseFloat(size) * multiplier;
  }

  /**
   * 更新頭像
   * @param request 更新請求參數
   * @returns 更新結果
   */
  static async updateAvatar(request: UpdateAvatarRequest): Promise<UpdateAvatarResponse> {
    const config = this.getConfig();
    
    try {
      console.log('🎭 開始更新頭像', request);

      // 驗證檔案（如果有提供）
      if (request.file) {
        const validation = this.validateFile(request.file);
        if (!validation.isValid) {
          throw new Error(validation.error);
        }
      }

      // 構建 FormData
      const formData = new FormData();
      formData.append('serviceNumberId', request.serviceNumberId);

      if (request.file) {
        formData.append('file', request.file);
      }

      // 生成 nonce
      const nonce = generateNonce();

      // 提取非文件參數用於加密
      const nonFileParams = extractNonFileParams(formData);
      
      // 生成授權字符串
      const encryptionParams = {
        appId: config.appId,
        appSecret: config.appSecret,
        nonce,
        data: nonFileParams
      };

      // 驗證加密參數
      if (!validateEncryptionParams(encryptionParams)) {
        throw new Error('加密參數驗證失敗');
      }

      const authorization = generateHmacAuthorization(encryptionParams);

      // 構建請求 URL
      const url = `${config.baseUrl}${config.endpoint}`;

      // 發送請求
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'aileMode': 'develop',
          'Authorization': authorization,
          'nonce': nonce
        },
        body: formData
      });

      // 處理響應
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ 頭像更新請求失敗:', response.status, errorText);
        throw new Error(`請求失敗: ${response.status} ${response.statusText}`);
      }

      const result: ApiResponse = await response.json();
      console.log('✅ 頭像更新響應:', result);

      if (!result.success) {
        throw new Error(result.message || '頭像更新失敗');
      }

      return {
        success: true,
        message: result.message || '頭像更新成功',
        data: result.data
      };

    } catch (error) {
      console.error('❌ 頭像更新失敗:', error);
      
      const errorMessage = error instanceof Error ? error.message : '未知錯誤';
      
      return {
        success: false,
        message: errorMessage
      };
    }
  }

  /**
   * 獲取當前頭像信息
   * @param serviceNumberId 服務號ID
   * @returns 頭像信息
   */
  static async getAvatarInfo(serviceNumberId: string): Promise<ApiResponse> {
    // 這個方法可以在後續需要時實作
    // 目前規格文件中沒有提供獲取頭像的 API
    console.log('📋 獲取頭像信息功能待實作', serviceNumberId);
    
    return {
      success: false,
      message: '獲取頭像信息功能尚未實作'
    };
  }
}
