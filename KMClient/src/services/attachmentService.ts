/**
 * 附件管理服務
 */

import { apiService } from './apiService';
import {
  UploadFileRequest,
  DeleteFileRequest,
  DownloadFileRequest,
  SetWebsiteRequest,
  DeleteWebsiteRequest,
  SetYoutubeRequest,
  DeleteYoutubeRequest,
  SetPlainTextRequest,
  DeletePlainTextRequest,
  GetAssetsRequest,
  GetAssetsResponse,
  ApiResponse,
} from '@/types/attachment';

// 附件管理服務
export class AttachmentService {
  /**
   * 上傳檔案
   */
  /**
   * 驗證上傳請求的數據完整性
   */
  static validateUploadRequest(request: UploadFileRequest): void {
    if (!request.file || request.file.length === 0) {
      throw new Error('文件數組不能為空');
    }

    if (!request.tenant_id) {
      throw new Error('tenant_id 不能為空');
    }

    if (!request.service_id) {
      throw new Error('service_id 不能為空');
    }

    // user_id 現在是可選參數，不再強制驗證
    // if (!request.user_id) {
    //   throw new Error('user_id 不能為空');
    // }

    if (!request.content_type) {
      throw new Error('content_type 不能為空');
    }

    // 驗證 content_type 格式
    const contentTypes = request.content_type.split(',');
    if (contentTypes.length !== request.file.length) {
      throw new Error(`content_type 中的 MIME 類型數量 (${contentTypes.length}) 與文件數量 (${request.file.length}) 不匹配`);
    }

    // 驗證每個文件對象
    request.file.forEach((file, index) => {
      if (!(file instanceof File)) {
        throw new Error(`第 ${index + 1} 個文件不是有效的 File 對象`);
      }

      if (!file.name) {
        throw new Error(`第 ${index + 1} 個文件沒有名稱`);
      }
    });
  }

  static async uploadFiles(request: UploadFileRequest): Promise<ApiResponse<{ file_names: string[] }>> {
    try {
      // 驗證請求數據
      AttachmentService.validateUploadRequest(request);

      console.log('📤 AttachmentService.uploadFiles 開始處理:', {
        fileCount: request.file.length,
        fileNames: request.file.map(f => f.name),
        fileSizes: request.file.map(f => f.size),
        fileTypes: request.file.map(f => f.type),
        contentType: request.content_type,
        tenant_id: request.tenant_id,
        service_id: request.service_id,
        user_id: request.user_id
      });

      const formData = new FormData();

      // 添加檔案 - 嘗試不同的字段名稱以確保兼容性
      request.file.forEach((file, index) => {
        console.log(`📁 添加文件 ${index + 1}:`, {
          name: file.name,
          type: file.type,
          size: file.size
        });

        // 使用標準的 'file' 字段名稱
        formData.append('file', file);

        // 如果是多文件上傳，也可以嘗試使用索引
        if (request.file.length > 1) {
          console.log(`  -> 多文件上傳，文件索引: ${index}`);
        }
      });

      // 添加其他參數
      formData.append('tenant_id', request.tenant_id);
      formData.append('service_id', request.service_id);
      // 只在 user_id 存在時才添加
      if (request.user_id) {
        formData.append('user_id', request.user_id);
      }
      formData.append('content_type', request.content_type);

      // 調試 FormData 內容
      console.log('📋 FormData 內容:');
      for (const [key, value] of formData.entries()) {
        if (value instanceof File) {
          console.log(`  ${key}: File(${value.name}, ${value.type}, ${value.size} bytes)`);
        } else {
          console.log(`  ${key}: ${value}`);
        }
      }

      console.log('🚀 發送上傳請求...');

      // 嘗試上傳，如果失敗則提供詳細的錯誤信息
      try {
        const response = await apiService.upload('ams', 'upload', formData);
        console.log('✅ 上傳請求成功:', response);
        return response;
      } catch (uploadError: any) {
        console.error('🔥 上傳請求失敗，分析錯誤原因...');

        // 分析具體的錯誤類型
        if (uploadError.response) {
          // 服務器響應了錯誤狀態碼
          const { status, statusText, data } = uploadError.response;
          console.error('服務器錯誤響應:', { status, statusText, data });

          if (status === 500) {
            console.error('🚨 服務器內部錯誤 (500)，可能的原因:');
            console.error('1. 後端處理 multipart/form-data 時出錯');
            console.error('2. content_type 字段格式不被後端接受');
            console.error('3. 文件大小超出限制');
            console.error('4. 後端服務異常');

            // 嘗試提供更有用的錯誤信息
            throw new Error(`服務器內部錯誤 (500): ${data?.message || statusText || '未知錯誤'}`);
          } else if (status === 413) {
            throw new Error('文件太大，超出服務器限制');
          } else if (status === 400) {
            throw new Error(`請求格式錯誤 (400): ${data?.message || '請檢查上傳參數'}`);
          } else {
            throw new Error(`HTTP 錯誤 ${status}: ${data?.message || statusText}`);
          }
        } else if (uploadError.request) {
          // 請求已發送但沒有收到響應
          console.error('網絡錯誤: 沒有收到服務器響應');
          throw new Error('網絡錯誤: 無法連接到服務器，請檢查網絡連接');
        } else {
          // 請求配置錯誤
          console.error('請求配置錯誤:', uploadError.message);
          throw new Error(`請求配置錯誤: ${uploadError.message}`);
        }
      }
    } catch (error) {
      console.error('❌ Upload files error:', error);

      // 詳細錯誤信息
      if (error instanceof Error) {
        console.error('錯誤詳情:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });
      }

      // 如果是 Axios 錯誤，提供更多信息
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any;
        console.error('Axios 錯誤詳情:', {
          status: axiosError.response?.status,
          statusText: axiosError.response?.statusText,
          data: axiosError.response?.data,
          headers: axiosError.response?.headers
        });
      }

      throw error;
    }
  }

  /**
   * 下載檔案 - 包含完整的錯誤處理和文件驗證
   */
  static async downloadFile(request: DownloadFileRequest): Promise<Blob> {
    try {
      console.log('📥 開始下載文件，file_path:', request.file_path);

      // 驗證 file_path 參數
      if (!request.file_path) {
        throw new Error('file_path 參數不能為空');
      }

      const requestData = {
        file_path: request.file_path,
      };

      console.log('📋 下載請求數據:', requestData);

      const blob = await apiService.download('ams', 'download', requestData);

      console.log('✅ 文件下載成功，大小:', blob.size, 'bytes');
      console.log('📄 文件類型:', blob.type);

      // 額外的文件完整性檢查
      if (blob.size === 0) {
        throw new Error('下載的文件為空，請檢查文件是否存在');
      }

      // 檢查文件類型是否合理
      if (blob.type && blob.type.includes('text/html')) {
        console.warn('⚠️ 警告：收到 HTML 響應，可能是錯誤頁面');
        throw new Error('服務器返回了錯誤頁面，請檢查文件路徑或聯繫管理員');
      }

      return blob;
    } catch (error: any) {
      console.error('❌ 下載文件失敗:', error);

      // 如果是我們的 apiService 拋出的錯誤，直接重新拋出（已經包含詳細信息）
      if (error.message && typeof error.message === 'string') {
        throw error;
      }

      // 處理其他類型的錯誤
      throw new Error('下載文件時發生未知錯誤，請重試或聯繫管理員');
    }
  }

  /**
   * 刪除檔案
   */
  static async deleteFiles(request: DeleteFileRequest): Promise<ApiResponse> {
    try {
      const response = await apiService.post('ams', 'deleteFile', request);
      return response;
    } catch (error) {
      console.error('Delete files error:', error);
      throw error;
    }
  }

  /**
   * 設置網站
   */
  static async setWebsite(request: SetWebsiteRequest): Promise<ApiResponse> {
    try {
      const response = await apiService.post('ams', 'setWebSite', request);
      return response;
    } catch (error) {
      console.error('Set website error:', error);
      throw error;
    }
  }

  /**
   * 刪除網站
   */
  static async deleteWebsite(request: DeleteWebsiteRequest): Promise<ApiResponse> {
    try {
      const response = await apiService.post('ams', 'deleteWebSite', request);
      return response;
    } catch (error) {
      console.error('Delete website error:', error);
      throw error;
    }
  }

  /**
   * 設置 YouTube
   */
  static async setYoutube(request: SetYoutubeRequest): Promise<ApiResponse> {
    try {
      const response = await apiService.post('ams', 'setYoutubeLink', request);
      return response;
    } catch (error) {
      console.error('Set youtube error:', error);
      throw error;
    }
  }

  /**
   * 刪除 YouTube
   */
  static async deleteYoutube(request: DeleteYoutubeRequest): Promise<ApiResponse> {
    try {
      const response = await apiService.post('ams', 'deleteYoutubeLink', request);
      return response;
    } catch (error) {
      console.error('Delete youtube error:', error);
      throw error;
    }
  }

  /**
   * 設置純文本
   */
  static async setPlainText(request: SetPlainTextRequest): Promise<ApiResponse> {
    try {
      const response = await apiService.post('ams', 'setPlainText', request);
      return response;
    } catch (error) {
      console.error('Set plain text error:', error);
      throw error;
    }
  }

  /**
   * 刪除純文本
   */
  static async deletePlainText(request: DeletePlainTextRequest): Promise<ApiResponse> {
    try {
      const response = await apiService.post('ams', 'deletePlainText', request);
      return response;
    } catch (error) {
      console.error('Delete plain text error:', error);
      throw error;
    }
  }

  /**
   * 獲取附件資料
   */
  static async getAssets(request: GetAssetsRequest): Promise<GetAssetsResponse> {
    try {
      const response = await apiService.post('ams', 'getAssets', request);
      return response;
    } catch (error) {
      console.error('Get assets error:', error);
      throw error;
    }
  }
}

// 附件服務 hooks
export const useAttachmentService = () => {
  return {
    uploadFiles: AttachmentService.uploadFiles,
    downloadFile: AttachmentService.downloadFile,
    deleteFiles: AttachmentService.deleteFiles,
    setWebsite: AttachmentService.setWebsite,
    deleteWebsite: AttachmentService.deleteWebsite,
    setYoutube: AttachmentService.setYoutube,
    deleteYoutube: AttachmentService.deleteYoutube,
    setPlainText: AttachmentService.setPlainText,
    deletePlainText: AttachmentService.deletePlainText,
    getAssets: AttachmentService.getAssets,
  };
};
