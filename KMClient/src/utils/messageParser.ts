/**
 * 消息解析工具
 * 用於解析 AI 回復的複雜消息格式
 */

import {
  MessageContent,
  ParsedMessageContent,
  TextMessageContent,
  ImageMessageContent,
  ButtonMessageContent,
  FlexMessageContent,
  // QuickReply, NewQuickReply 暫時未使用
  QuickReplyItem,
  NewQuickReplyItem,
} from '@/types/chat';

/**
 * 消息解析器 - 處理 LINE Bot 風格的 JSON 消息格式
 */
export class MessageParser {
  /**
   * 解析消息內容
   * @param content 原始消息內容（可能是 JSON 字符串或純文本）
   * @returns 解析後的消息結構
   */
  static parseMessage(content: string): ParsedMessageContent {
    try {
      // 嘗試解析 JSON 格式
      const parsed = JSON.parse(content);

      // 如果是數組格式（LINE Bot 標準格式）
      if (Array.isArray(parsed)) {
        return this.parseMessageArray(parsed);
      }

      // 如果是單個對象
      if (typeof parsed === 'object' && parsed !== null) {
        // 檢查是否為 AI 圖片分析回應格式（包含 quickReply 和 text 在同一層級）
        if (parsed.quickReply && parsed.text && parsed.type === 'Text') {
          return this.parseAIImageResponse(parsed);
        }

        // 其他單個對象，包裝成數組處理
        return this.parseMessageArray([parsed]);
      }

      // 如果解析成功但不是預期格式，當作純文本處理
      return this.createTextMessage(content);

    } catch (error) {
      // JSON 解析失敗，當作純文本處理
      console.log('Message is not JSON format, treating as plain text:', content);
      return this.createTextMessage(content);
    }
  }

  /**
   * 解析消息數組
   * @param messages 消息對象數組
   * @returns 解析後的消息結構
   */
  static parseMessageArray(messages: any[]): ParsedMessageContent {
    const contents: MessageContent[] = [];
    const quickReplyItems: QuickReplyItem[] = [];
    const newQuickReplyItems: NewQuickReplyItem[] = [];
    let hasQuickReply = false;

    for (const msg of messages) {
      try {
        const parsedMsg = this.parseMessageObject(msg);
        if (parsedMsg) {
          contents.push(parsedMsg);

          // 收集快速回復項目
          if (parsedMsg.quickReply && parsedMsg.quickReply.items) {
            // 檢查是否為新格式（包含 title 和 displayText）
            if (this.isNewQuickReplyFormat(parsedMsg.quickReply.items)) {
              newQuickReplyItems.push(...(parsedMsg.quickReply.items as NewQuickReplyItem[]));
            } else {
              quickReplyItems.push(...(parsedMsg.quickReply.items as QuickReplyItem[]));
            }
            hasQuickReply = true;
          }
        }
      } catch (error) {
        console.warn('Failed to parse message object:', msg, error);
        // 解析失敗的消息當作文本處理
        contents.push({
          type: 'Text',
          text: JSON.stringify(msg),
          quickReply: null
        });
      }
    }

    return {
      contents,
      hasQuickReply,
      quickReplyItems,
      newQuickReplyItems
    };
  }

  /**
   * 解析單個消息對象
   * @param msg 消息對象
   * @returns 解析後的消息內容
   */
  private static parseMessageObject(msg: any): MessageContent | null {
    if (!msg || typeof msg !== 'object') {
      return null;
    }

    const { type, quickReply } = msg;

    switch (type) {
      case 'Text':
      case 'text':
        return this.parseTextMessage(msg, quickReply);

      case 'Image':
      case 'image':
        return this.parseImageMessage(msg, quickReply);

      case 'Template':
      case 'template':
        return this.parseButtonMessage(msg, quickReply);

      case 'Flex':
      case 'flex':
        return this.parseFlexMessage(msg, quickReply);

      default:
        // 未知類型，嘗試當作文本處理
        console.warn('Unknown message type:', type);
        return {
          type: 'Text',
          text: msg.text || JSON.stringify(msg),
          quickReply: quickReply || null
        };
    }
  }

  /**
   * 解析文本消息
   */
  private static parseTextMessage(msg: any, quickReply: any): TextMessageContent {
    return {
      type: 'Text',
      text: msg.text || '',
      quickReply: quickReply || null
    };
  }

  /**
   * 解析圖片消息
   */
  private static parseImageMessage(msg: any, quickReply: any): ImageMessageContent {
    return {
      type: 'Image',
      originalContentUrl: msg.originalContentUrl || msg.url || '',
      previewImageUrl: msg.previewImageUrl || msg.previewUrl || msg.url || '',
      quickReply: quickReply || null
    };
  }

  /**
   * 解析按鈕消息
   */
  private static parseButtonMessage(msg: any, quickReply: any): ButtonMessageContent {
    return {
      type: 'Template',
      altText: msg.altText || '按鈕消息',
      template: {
        type: 'buttons',
        text: msg.template?.text || msg.text || '',
        actions: msg.template?.actions || msg.actions || []
      },
      quickReply: quickReply || null
    };
  }

  /**
   * 解析 Flex 消息
   */
  private static parseFlexMessage(msg: any, quickReply: any): FlexMessageContent {
    return {
      type: 'Flex',
      altText: msg.altText || 'Flex 消息',
      contents: msg.contents || {},
      quickReply: quickReply || null
    };
  }

  /**
   * 解析 AI 圖片分析回應格式
   * @param response AI 回應對象，包含 quickReply、text 和 type
   * @returns 解析後的消息結構
   */
  private static parseAIImageResponse(response: any): ParsedMessageContent {
    const contents: MessageContent[] = [];
    const quickReplyItems: QuickReplyItem[] = [];
    const newQuickReplyItems: NewQuickReplyItem[] = [];

    // 處理主要文本內容
    if (response.text) {
      contents.push({
        type: 'Text',
        text: response.text,
        quickReply: response.quickReply
      });
    }

    // 處理快速回復項目
    let hasQuickReply = false;
    if (response.quickReply && response.quickReply.items && Array.isArray(response.quickReply.items)) {
      hasQuickReply = true;

      for (const item of response.quickReply.items) {
        if (item.type === 'Action' && item.action) {
          // 轉換為標準的 QuickReplyItem 格式
          const quickReplyItem: QuickReplyItem = {
            type: 'action',
            action: {
              type: (item.action.type === 'Postback' ? 'postback' : item.action.type) || 'postback',
              label: item.action.displayText || item.action.data || '選項',
              data: item.action.data || item.action.displayText || '',
              text: item.action.displayText || item.action.data || ''
            }
          };
          quickReplyItems.push(quickReplyItem);
        }
      }
    }

    console.log('🔍 AI 圖片分析回應解析結果:', {
      hasText: !!response.text,
      textLength: response.text?.length || 0,
      hasQuickReply,
      quickReplyCount: quickReplyItems.length,
      quickReplyItems
    });

    return {
      contents,
      hasQuickReply,
      quickReplyItems,
      newQuickReplyItems
    };
  }

  /**
   * 創建純文本消息結構
   * @param text 文本內容
   * @returns 文本消息結構
   */
  private static createTextMessage(text: string): ParsedMessageContent {
    return {
      contents: [{
        type: 'Text',
        text: text,
        quickReply: null
      }],
      hasQuickReply: false,
      quickReplyItems: [],
      newQuickReplyItems: []
    };
  }

  /**
   * 檢查是否為新的快速回復格式（包含 title 和 displayText）
   * @param items 快速回復項目數組
   * @returns 是否為新格式
   */
  private static isNewQuickReplyFormat(items: any[]): boolean {
    if (!Array.isArray(items) || items.length === 0) {
      return false;
    }

    // 檢查第一個項目是否包含 title 和 displayText 字段（直接在項目層級）
    const firstItem = items[0];

    // 新格式：直接包含 title 和 displayText
    if (firstItem &&
        typeof firstItem.title === 'string' &&
        typeof firstItem.displayText === 'string' &&
        !firstItem.type && !firstItem.action) {
      return true;
    }

    return false;
  }

  /**
   * 驗證快速回復項目格式
   * @param quickReply 快速回復對象
   * @returns 是否有效
   */
  static validateQuickReply(quickReply: any): boolean {
    if (!quickReply || !quickReply.items || !Array.isArray(quickReply.items)) {
      return false;
    }

    return quickReply.items.every((item: any) =>
      item &&
      (item.type === 'action' || item.type === 'Action') &&
      item.action &&
      item.action.type &&
      (item.action.label || item.action.title)
    );
  }

  /**
   * 提取純文本內容（用於搜索、預覽等）
   * @param parsedContent 解析後的消息內容
   * @returns 純文本字符串
   */
  static extractPlainText(parsedContent: ParsedMessageContent): string {
    return parsedContent.contents
      .map(content => {
        switch (content.type) {
          case 'Text':
            return content.text;
          case 'Template':
            return content.template.text;
          case 'Flex':
            return content.altText;
          case 'Image':
            return '[圖片]';
          default:
            return '[未知內容]';
        }
      })
      .join(' ');
  }
}

// 向後兼容的導出函數
/**
 * 解析消息內容（向後兼容）
 * @deprecated 請使用 MessageParser.parseMessage() 代替
 */
export const parseMessageContent = (content: string): ParsedMessageContent => {
  return MessageParser.parseMessage(content);
};

/**
 * 檢查消息是否包含附件引用
 */
export const hasAttachmentReference = (content: string): boolean => {
  // 檢查是否包含附件相關的關鍵詞
  const attachmentKeywords = [
    '根據您上傳的',
    '根據附件',
    '從文件中',
    '文檔內容',
    '檔案內容',
    '上傳的文件',
  ];

  return attachmentKeywords.some(keyword => content.includes(keyword));
};

/**
 * 格式化英文文字，在適當位置添加空格
 * 用於處理後端返回的黏在一起的英文文字
 * @param text 原始文字
 * @returns 格式化後的文字
 */
export const formatEnglishText = (text: string): string => {
  if (!text || typeof text !== 'string') {
    return text;
  }

  // 檢查是否為純英文（不包含中文、日文、韓文等）
  const hasNonEnglish = /[\u4e00-\u9fff\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af]/.test(text);

  // 如果包含非英文字符，直接返回原文
  if (hasNonEnglish) {
    return text;
  }

  // 檢查是否已經有適當的空格分隔
  const hasSpaces = /\s/.test(text);
  if (hasSpaces) {
    return text;
  }

  // 簡單而有效的方法：基於常見的英文單詞模式進行替換
  let result = text;

  // 定義常見的英文單詞和短語，按長度排序（長的先處理）
  const commonPatterns = [
    // 常見短語
    { pattern: /whattoconsiderwhenbuyingahouse/gi, replacement: 'What to consider when buying a house' },
    { pattern: /whatisrealestatetax/gi, replacement: 'What is real estate tax' },
    { pattern: /howtoevaluaterealestatvalue/gi, replacement: 'How to evaluate real estate value' },
    { pattern: /howtoevaluaterealestatevalue/gi, replacement: 'How to evaluate real estate value' },

    // 常見單詞組合
    { pattern: /realestate/gi, replacement: 'real estate' },
    { pattern: /whatto/gi, replacement: 'what to' },
    { pattern: /howto/gi, replacement: 'how to' },
    { pattern: /whenis/gi, replacement: 'when is' },
    { pattern: /whatis/gi, replacement: 'what is' },
    { pattern: /whereis/gi, replacement: 'where is' },
    { pattern: /whyis/gi, replacement: 'why is' },
    { pattern: /whois/gi, replacement: 'who is' },

    // 其他常見組合
    { pattern: /buyinga/gi, replacement: 'buying a' },
    { pattern: /considera/gi, replacement: 'consider a' },
    { pattern: /evaluatea/gi, replacement: 'evaluate a' },
    { pattern: /investin/gi, replacement: 'invest in' },
    { pattern: /lookingfor/gi, replacement: 'looking for' },
  ];

  // 應用模式替換
  commonPatterns.forEach(({ pattern, replacement }) => {
    if (pattern.test(result)) {
      result = result.replace(pattern, replacement);
    }
  });

  // 如果還是沒有空格，使用基本的大小寫分割
  if (!/\s/.test(result)) {
    result = result
      .replace(/([a-z])([A-Z])/g, '$1 $2') // 小寫字母後跟大寫字母
      .replace(/([A-Z])([A-Z][a-z])/g, '$1 $2') // 大寫字母後跟大寫字母+小寫字母
      .replace(/(\d)([A-Z])/g, '$1 $2') // 數字後跟大寫字母
      .replace(/([A-Z])(\d)/g, '$1 $2'); // 大寫字母後跟數字
  }

  return result.trim();
};

/**
 * 提取快速回復選項為純文本數組
 */
export const extractQuickReplyTexts = (parsedContent: ParsedMessageContent): string[] => {
  const texts: string[] = [];

  // 處理舊格式快速回復
  parsedContent.quickReplyItems.forEach(item => {
    if (item.action.label) {
      texts.push(formatEnglishText(item.action.label));
    }
  });

  // 處理新格式快速回復
  if (parsedContent.newQuickReplyItems) {
    parsedContent.newQuickReplyItems.forEach(item => {
      if (item.title) {
        texts.push(formatEnglishText(item.title));
      }
    });
  }

  return texts;
};
