/**
 * URL 參數處理工具
 * 負責解析、驗證和管理 URL 查詢參數
 */

// URL 參數介面定義
export interface URLParams {
  tenantId?: string;
  serviceId?: string;
  userId?: string;
}

// 參數驗證結果介面
export interface ParamValidationResult {
  isValid: boolean;
  missingParams: string[];
  params: URLParams;
}

/**
 * 解析 URL 查詢參數
 * 支援多種參數名稱格式：TenantID/tenantId, ServiceID/serviceId, UserID/userId
 */
export const parseURLParams = (): URLParams => {
  const urlParams = new URLSearchParams(window.location.search);
  
  // 支援多種參數名稱格式
  const getTenantId = () => {
    return urlParams.get('TenantID') || 
           urlParams.get('tenantId') || 
           urlParams.get('tenant_id') || 
           urlParams.get('TENANT_ID') || 
           undefined;
  };

  const getServiceId = () => {
    return urlParams.get('ServiceID') || 
           urlParams.get('serviceId') || 
           urlParams.get('service_id') || 
           urlParams.get('SERVICE_ID') || 
           undefined;
  };

  const getUserId = () => {
    return urlParams.get('UserID') || 
           urlParams.get('userId') || 
           urlParams.get('user_id') || 
           urlParams.get('USER_ID') || 
           undefined;
  };

  return {
    tenantId: getTenantId(),
    serviceId: getServiceId(),
    userId: getUserId(),
  };
};

/**
 * 驗證 URL 參數
 * 檢查必要參數是否存在並返回驗證結果
 */
export const validateURLParams = (params: URLParams): ParamValidationResult => {
  const missingParams: string[] = [];

  // 檢查必要參數 - 只有 TenantID 是必要的
  if (!params.tenantId) {
    missingParams.push('TenantID（租戶編碼）');
  }
  // ServiceID 現在是可選的，不再檢查

  // 驗證參數格式（可選）
  if (params.tenantId && !isValidId(params.tenantId)) {
    missingParams.push('TenantID 格式無效');
  }
  if (params.serviceId && !isValidId(params.serviceId)) {
    missingParams.push('ServiceID 格式無效');
  }
  if (params.userId && !isValidId(params.userId)) {
    missingParams.push('UserID 格式無效');
  }

  return {
    isValid: missingParams.length === 0,
    missingParams,
    params,
  };
};

/**
 * 檢查 ID 格式是否有效
 * 允許字母、數字、底線、連字符和點號（支援 UUID 和其他常見格式）
 */
const isValidId = (id: string): boolean => {
  // 更寬鬆的驗證，支援 UUID、日期格式等
  const idPattern = /^[a-zA-Z0-9_.-]+$/;
  return idPattern.test(id) && id.length >= 1 && id.length <= 200;
};

/**
 * 將參數存儲到 localStorage
 */
export const storeParams = (params: URLParams): void => {
  try {
    localStorage.setItem('kmClient_params', JSON.stringify(params));
    localStorage.setItem('kmClient_params_timestamp', Date.now().toString());
  } catch (error) {
    console.error('Failed to store params to localStorage:', error);
  }
};

/**
 * 從 localStorage 讀取參數
 */
export const getStoredParams = (): URLParams | null => {
  try {
    const stored = localStorage.getItem('kmClient_params');
    const timestamp = localStorage.getItem('kmClient_params_timestamp');
    
    if (!stored || !timestamp) {
      return null;
    }

    // 檢查參數是否過期（24小時）
    const now = Date.now();
    const storedTime = parseInt(timestamp, 10);
    const maxAge = 24 * 60 * 60 * 1000; // 24小時

    if (now - storedTime > maxAge) {
      clearStoredParams();
      return null;
    }

    return JSON.parse(stored);
  } catch (error) {
    console.error('Failed to get stored params:', error);
    return null;
  }
};

/**
 * 清除存儲的參數
 */
export const clearStoredParams = (): void => {
  try {
    localStorage.removeItem('kmClient_params');
    localStorage.removeItem('kmClient_params_timestamp');
  } catch (error) {
    console.error('Failed to clear stored params:', error);
  }
};

/**
 * 生成帶參數的 URL
 */
export const generateURLWithParams = (baseUrl: string, params: URLParams): string => {
  const url = new URL(baseUrl);
  
  if (params.tenantId) {
    url.searchParams.set('TenantID', params.tenantId);
  }
  if (params.serviceId) {
    url.searchParams.set('ServiceID', params.serviceId);
  }
  if (params.userId) {
    url.searchParams.set('UserID', params.userId);
  }

  return url.toString();
};

/**
 * 檢查當前 URL 是否包含有效參數
 */
export const hasValidURLParams = (): boolean => {
  const params = parseURLParams();
  const validation = validateURLParams(params);
  return validation.isValid;
};

/**
 * 獲取參數摘要信息（用於顯示）
 */
export const getParamsSummary = (params: URLParams): string => {
  const parts = [];
  if (params.tenantId) parts.push(`租戶: ${params.tenantId}`);
  if (params.serviceId) parts.push(`服務: ${params.serviceId}`);
  if (params.userId) parts.push(`用戶: ${params.userId}`);
  return parts.join(' | ');
};
