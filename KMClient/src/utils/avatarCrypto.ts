/**
 * 頭像 API 加密工具
 * 實作 HMAC-SHA256 加密邏輯，按照規格文件要求
 */

import CryptoJS from 'crypto-js';
import { EncryptionParams } from '@/types/avatar';

/**
 * 為保證請求方傳遞的原始數據參數順序與接收方一致，請求方做了一次參數排序
 * @param obj 要排序的對象
 * @returns 排序後的 JSON 字符串
 */
export function canonicalJson(obj: Record<string, any>): string {
  const sortedKeys = Object.keys(obj).sort();
  const result: Record<string, any> = {};
  
  for (const key of sortedKeys) {
    result[key] = obj[key];
  }
  
  return JSON.stringify(result);
}

/**
 * 生成 HMAC-SHA256 授權字符串
 * @param params 加密參數
 * @returns 授權字符串，格式為 "AILE appId:signature"
 */
export function generateHmacAuthorization(params: EncryptionParams): string {
  const { appId, appSecret, nonce, data } = params;
  
  console.log('🔐 開始生成 HMAC 授權');
  console.log('appId =', appId);
  console.log('appSecret =', appSecret);
  console.log('nonce =', nonce);
  console.log('data =', JSON.stringify(data));
  
  let source = '';
  
  if (typeof data === 'string') {
    console.log('data is string');
    source = data;
  } else if (typeof data === 'object') {
    source = canonicalJson(data);
  }
  
  // 組合原始數據：appId + nonce + source
  const fullData = appId + nonce + source;
  console.log('fullData =', fullData);
  
  // HMAC-SHA256 加密，key 直接用字符串
  const encrypted = CryptoJS.HmacSHA256(fullData, appSecret);
  
  // Base64 編碼
  const signature = CryptoJS.enc.Base64.stringify(encrypted);
  console.log('signature =', signature);
  
  // 返回格式化的授權字符串
  const authorization = `AILE ${appId}:${signature}`;
  console.log('authorization =', authorization);
  
  return authorization;
}

/**
 * 生成當前時間戳（毫秒）作為 nonce
 * @returns 時間戳字符串
 */
export function generateNonce(): string {
  return Date.now().toString();
}

/**
 * 從 FormData 中提取非文件參數
 * @param formData FormData 對象
 * @returns 非文件參數對象
 */
export function extractNonFileParams(formData: FormData): Record<string, any> {
  const params: Record<string, any> = {};
  
  formData.forEach((value, key) => {
    // 只處理非文件類型的參數
    if (!(value instanceof File)) {
      params[key] = value;
    }
  });
  
  return params;
}

/**
 * 驗證加密參數
 * @param params 加密參數
 * @returns 是否有效
 */
export function validateEncryptionParams(params: EncryptionParams): boolean {
  const { appId, appSecret, nonce, data } = params;
  
  if (!appId || typeof appId !== 'string') {
    console.error('❌ appId 無效');
    return false;
  }
  
  if (!appSecret || typeof appSecret !== 'string') {
    console.error('❌ appSecret 無效');
    return false;
  }
  
  if (!nonce || typeof nonce !== 'string') {
    console.error('❌ nonce 無效');
    return false;
  }
  
  if (!data || typeof data !== 'object') {
    console.error('❌ data 無效');
    return false;
  }
  
  return true;
}
