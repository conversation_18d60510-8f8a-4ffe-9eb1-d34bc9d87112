/**
 * 媒體處理工具函數
 */

import {
  MediaType,
  ImageFormat,
  AudioFormat,
  ImageProcessOptions,
  MediaValidationResult,
  MediaError,
  MediaErrorType,
  MediaConfig,
  DEFAULT_MEDIA_CONFIG
} from '@/types/media';

/**
 * 檢查瀏覽器是否支援媒體錄製
 */
export const checkMediaSupport = () => {
  return {
    getUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
    mediaRecorder: !!(window.MediaRecorder),
    audioContext: !!(window.AudioContext || (window as any).webkitAudioContext)
  };
};

/**
 * 獲取文件的媒體類型
 */
export const getMediaType = (file: File): MediaType | null => {
  const mimeType = file.type.toLowerCase();
  
  if (mimeType.startsWith('image/')) {
    return 'image';
  } else if (mimeType.startsWith('audio/')) {
    return 'voice';
  }
  
  return null;
};

/**
 * 獲取文件格式
 */
export const getFileFormat = (file: File): string => {
  const extension = file.name.split('.').pop()?.toLowerCase() || '';
  return extension;
};

/**
 * 驗證媒體文件
 */
export const validateMediaFile = (
  file: File,
  config: MediaConfig = DEFAULT_MEDIA_CONFIG
): MediaValidationResult => {
  const mediaType = getMediaType(file);
  
  if (!mediaType) {
    return {
      isValid: false,
      error: '不支援的文件類型'
    };
  }

  const format = getFileFormat(file);
  const typeConfig = config[mediaType];

  // 檢查文件大小
  if (file.size > typeConfig.maxSize) {
    return {
      isValid: false,
      error: `文件大小超過限制（最大 ${formatFileSize(typeConfig.maxSize)}）`
    };
  }

  // 檢查格式
  if (mediaType === 'image') {
    const imageConfig = config.image;
    if (!imageConfig.allowedFormats.includes(format as ImageFormat)) {
      return {
        isValid: false,
        error: `不支援的圖片格式，支援格式：${imageConfig.allowedFormats.join(', ')}`
      };
    }
  }

  if (mediaType === 'voice') {
    const voiceConfig = config.voice;
    if (!voiceConfig.allowedFormats.includes(format as AudioFormat)) {
      return {
        isValid: false,
        error: `不支援的音頻格式，支援格式：${voiceConfig.allowedFormats.join(', ')}`
      };
    }
  }

  return { isValid: true };
};

/**
 * 將文件轉換為純 Base64 字符串（不含前綴）
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => {
      const result = reader.result as string;
      // 移除 data URL 前綴，只保留 Base64 數據
      const base64 = result.split(',')[1];
      resolve(base64);
    };

    reader.onerror = () => {
      reject(new Error('文件讀取失敗'));
    };

    reader.readAsDataURL(file);
  });
};

/**
 * 將文件轉換為完整的 Data URI 格式
 */
export const fileToDataURI = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => {
      const result = reader.result as string;
      // 返回完整的 Data URI
      resolve(result);
    };

    reader.onerror = () => {
      reject(new Error('文件讀取失敗'));
    };

    reader.readAsDataURL(file);
  });
};

/**
 * 將 Blob 轉換為純 Base64 字符串（不含前綴）
 */
export const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => {
      const result = reader.result as string;
      const base64 = result.split(',')[1];
      resolve(base64);
    };

    reader.onerror = () => {
      reject(new Error('Blob 轉換失敗'));
    };

    reader.readAsDataURL(blob);
  });
};

/**
 * 將 Blob 轉換為完整的 Data URI 格式
 */
export const blobToDataURI = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => {
      const result = reader.result as string;
      // 返回完整的 Data URI
      resolve(result);
    };

    reader.onerror = () => {
      reject(new Error('Blob 轉換失敗'));
    };

    reader.readAsDataURL(blob);
  });
};

/**
 * 壓縮圖片
 */
export const compressImage = (
  file: File,
  options: ImageProcessOptions = {}
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const {
      maxWidth = 1920,
      maxHeight = 1080,
      quality = 0.8,
      format = 'jpeg'
    } = options;

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 計算新的尺寸
      let { width, height } = img;
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      canvas.width = width;
      canvas.height = height;

      // 繪製圖片
      ctx?.drawImage(img, 0, 0, width, height);

      // 轉換為 Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('圖片壓縮失敗'));
          }
        },
        `image/${format}`,
        quality
      );
    };

    img.onerror = () => {
      reject(new Error('圖片載入失敗'));
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * 獲取圖片尺寸
 */
export const getImageDimensions = (file: File): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
      URL.revokeObjectURL(img.src);
    };
    
    img.onerror = () => {
      reject(new Error('無法獲取圖片尺寸'));
      URL.revokeObjectURL(img.src);
    };
    
    img.src = URL.createObjectURL(file);
  });
};

/**
 * 獲取音頻時長
 */
export const getAudioDuration = (file: File): Promise<number> => {
  return new Promise((resolve, reject) => {
    const audio = new Audio();
    
    audio.onloadedmetadata = () => {
      resolve(audio.duration);
      URL.revokeObjectURL(audio.src);
    };
    
    audio.onerror = () => {
      reject(new Error('無法獲取音頻時長'));
      URL.revokeObjectURL(audio.src);
    };
    
    audio.src = URL.createObjectURL(file);
  });
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 格式化時間（秒轉為 mm:ss 格式）
 */
export const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

/**
 * 創建媒體錯誤
 */
export const createMediaError = (
  type: MediaErrorType,
  message: string,
  details?: any
): MediaError => {
  return { type, message, details };
};

/**
 * 檢查麥克風權限
 */
export const checkMicrophonePermission = async (): Promise<boolean> => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    stream.getTracks().forEach(track => track.stop());
    return true;
  } catch (error) {
    console.warn('麥克風權限檢查失敗:', error);
    return false;
  }
};

/**
 * 請求麥克風權限
 */
export const requestMicrophonePermission = async (): Promise<MediaStream> => {
  try {
    return await navigator.mediaDevices.getUserMedia({ audio: true });
  } catch (error) {
    throw createMediaError(
      MediaErrorType.PERMISSION_DENIED,
      '無法獲取麥克風權限，請檢查瀏覽器設定',
      error
    );
  }
};

/**
 * 創建完整的 Data URI 格式
 */
export const createDataURI = (base64Data: string, mimeType: string): string => {
  // 如果已經是 Data URI 格式，直接返回
  if (base64Data.startsWith('data:')) {
    return base64Data;
  }

  // 創建完整的 Data URI
  return `data:${mimeType};base64,${base64Data}`;
};

/**
 * 從 Data URI 中提取純 Base64 數據
 */
export const extractBase64FromDataURI = (dataURI: string): string => {
  if (dataURI.startsWith('data:')) {
    return dataURI.split(',')[1];
  }
  return dataURI;
};

/**
 * 將 Base64 Data URI 轉換為 Blob URL 用於音頻播放
 */
export const dataURIToBlobURL = (dataURI: string): string => {
  try {
    // 如果不是 Data URI 格式，直接返回
    if (!dataURI.startsWith('data:')) {
      console.warn('⚠️ 不是有效的 Data URI 格式:', dataURI.substring(0, 50));
      return dataURI;
    }

    // 解析 Data URI
    const [header, base64Data] = dataURI.split(',');
    if (!header || !base64Data) {
      throw new Error('無效的 Data URI 格式');
    }

    // 提取 MIME 類型
    const mimeMatch = header.match(/data:([^;]+)/);
    const mimeType = mimeMatch ? mimeMatch[1] : 'audio/webm';

    // 驗證 Base64 數據格式
    if (!/^[A-Za-z0-9+/]*={0,2}$/.test(base64Data)) {
      console.warn('⚠️ Base64 數據格式無效，使用原始 Data URI');
      return dataURI;
    }

    // 將 Base64 轉換為 Uint8Array
    const binaryString = atob(base64Data);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    // 創建 Blob
    const blob = new Blob([bytes], { type: mimeType });

    // 創建 Blob URL
    const blobURL = URL.createObjectURL(blob);

    console.log('✅ Data URI 轉換為 Blob URL 成功:', {
      originalSize: dataURI.length,
      mimeType: mimeType,
      blobSize: blob.size,
      blobURL: blobURL.substring(0, 50) + '...'
    });

    return blobURL;
  } catch (error) {
    console.error('❌ Data URI 轉換失敗:', error);
    // 轉換失敗時返回原始數據，讓瀏覽器嘗試直接使用
    return dataURI;
  }
};

/**
 * 清理 Blob URL 資源
 */
export const revokeBlobURL = (blobURL: string): void => {
  if (blobURL && blobURL.startsWith('blob:')) {
    URL.revokeObjectURL(blobURL);
    console.log('🗑️ Blob URL 資源已清理:', blobURL.substring(0, 50) + '...');
  }
};

/**
 * 從 Data URI 中提取 MIME 類型
 */
export const extractMimeTypeFromDataURI = (dataURI: string): string => {
  if (dataURI.startsWith('data:')) {
    const mimeMatch = dataURI.match(/data:([^;]+)/);
    return mimeMatch ? mimeMatch[1] : '';
  }
  return '';
};

/**
 * 驗證 Data URI 格式
 */
export const isValidDataURI = (dataURI: string): boolean => {
  const dataURIPattern = /^data:([a-zA-Z0-9][a-zA-Z0-9\/+\-]*);base64,([A-Za-z0-9+\/=]+)$/;
  return dataURIPattern.test(dataURI);
};
