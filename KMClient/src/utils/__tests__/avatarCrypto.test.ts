/**
 * 頭像加密工具測試
 */

import { describe, it, expect } from 'vitest';
import {
  canonicalJson,
  generateHmacAuthorization,
  generateNonce,
  extractNonFileParams,
  validateEncryptionParams
} from '../avatarCrypto';

describe('avatarCrypto', () => {
  describe('canonicalJson', () => {
    it('應該正確排序對象鍵並返回 JSON 字符串', () => {
      const obj = {
        c: 3,
        a: 1,
        b: 2
      };
      
      const result = canonicalJson(obj);
      expect(result).toBe('{"a":1,"b":2,"c":3}');
    });

    it('應該處理空對象', () => {
      const result = canonicalJson({});
      expect(result).toBe('{}');
    });

    it('應該處理嵌套對象', () => {
      const obj = {
        z: { b: 2, a: 1 },
        a: 'test'
      };
      
      const result = canonicalJson(obj);
      expect(result).toBe('{"a":"test","z":{"b":2,"a":1}}');
    });
  });

  describe('generateHmacAuthorization', () => {
    it('應該生成正確格式的授權字符串', () => {
      const params = {
        appId: 'test-app-id',
        appSecret: 'test-secret',
        nonce: '1234567890',
        data: { serviceNumberId: 'test-service' }
      };

      const result = generateHmacAuthorization(params);
      
      // 檢查格式
      expect(result).toMatch(/^AILE test-app-id:.+$/);
      
      // 檢查包含 Base64 編碼的簽名
      const signature = result.split(':')[1];
      expect(signature).toMatch(/^[A-Za-z0-9+/]+=*$/);
    });

    it('應該對相同輸入產生相同的簽名', () => {
      const params = {
        appId: 'test-app-id',
        appSecret: 'test-secret',
        nonce: '1234567890',
        data: { serviceNumberId: 'test-service' }
      };

      const result1 = generateHmacAuthorization(params);
      const result2 = generateHmacAuthorization(params);
      
      expect(result1).toBe(result2);
    });

    it('應該對不同輸入產生不同的簽名', () => {
      const params1 = {
        appId: 'test-app-id-1',
        appSecret: 'test-secret',
        nonce: '1234567890',
        data: { serviceNumberId: 'test-service' }
      };

      const params2 = {
        appId: 'test-app-id-2',
        appSecret: 'test-secret',
        nonce: '1234567890',
        data: { serviceNumberId: 'test-service' }
      };

      const result1 = generateHmacAuthorization(params1);
      const result2 = generateHmacAuthorization(params2);
      
      expect(result1).not.toBe(result2);
    });
  });

  describe('generateNonce', () => {
    it('應該生成時間戳字符串', () => {
      const nonce = generateNonce();
      
      // 檢查是否為數字字符串
      expect(nonce).toMatch(/^\d+$/);
      
      // 檢查是否為合理的時間戳（13位數字）
      expect(nonce.length).toBeGreaterThanOrEqual(13);
    });

    it('應該生成不同的 nonce', async () => {
      const nonce1 = generateNonce();
      // 等待一毫秒確保時間戳不同
      await new Promise(resolve => setTimeout(resolve, 1));
      const nonce2 = generateNonce();

      expect(nonce1).not.toBe(nonce2);
    });
  });

  describe('extractNonFileParams', () => {
    it('應該提取非文件參數', () => {
      const formData = new FormData();
      formData.append('serviceNumberId', 'test-service');
      formData.append('name', 'test-name');
      formData.append('file', new File(['test'], 'test.png', { type: 'image/png' }));

      const result = extractNonFileParams(formData);
      
      expect(result).toEqual({
        serviceNumberId: 'test-service',
        name: 'test-name'
      });
      expect(result.file).toBeUndefined();
    });

    it('應該處理空的 FormData', () => {
      const formData = new FormData();
      const result = extractNonFileParams(formData);
      
      expect(result).toEqual({});
    });
  });

  describe('validateEncryptionParams', () => {
    it('應該驗證有效的參數', () => {
      const params = {
        appId: 'test-app-id',
        appSecret: 'test-secret',
        nonce: '1234567890',
        data: { serviceNumberId: 'test-service' }
      };

      const result = validateEncryptionParams(params);
      expect(result).toBe(true);
    });

    it('應該拒絕無效的 appId', () => {
      const params = {
        appId: '',
        appSecret: 'test-secret',
        nonce: '1234567890',
        data: { serviceNumberId: 'test-service' }
      };

      const result = validateEncryptionParams(params);
      expect(result).toBe(false);
    });

    it('應該拒絕無效的 appSecret', () => {
      const params = {
        appId: 'test-app-id',
        appSecret: '',
        nonce: '1234567890',
        data: { serviceNumberId: 'test-service' }
      };

      const result = validateEncryptionParams(params);
      expect(result).toBe(false);
    });

    it('應該拒絕無效的 nonce', () => {
      const params = {
        appId: 'test-app-id',
        appSecret: 'test-secret',
        nonce: '',
        data: { serviceNumberId: 'test-service' }
      };

      const result = validateEncryptionParams(params);
      expect(result).toBe(false);
    });

    it('應該拒絕無效的 data', () => {
      const params = {
        appId: 'test-app-id',
        appSecret: 'test-secret',
        nonce: '1234567890',
        data: null as any
      };

      const result = validateEncryptionParams(params);
      expect(result).toBe(false);
    });
  });
});
