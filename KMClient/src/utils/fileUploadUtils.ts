/**
 * 文件上傳相關的工具函數
 */

/**
 * 根據文件列表生成 content_type 參數
 * 單個文件：返回該文件的 MIME 類型
 * 多個文件：返回所有文件的 MIME 類型，用逗號分隔（不去重，每個文件對應一個類型）
 * @param files 文件列表
 * @returns content_type 字符串
 */
export const generateContentType = (files: File[]): string => {
  if (files.length === 0) {
    return 'application/octet-stream'; // 默認類型
  }

  // 獲取單個文件的 MIME 類型，支持 Markdown 文件的回退處理
  const getFileMimeType = (file: File): string => {
    if (file.type) {
      return file.type;
    }

    // 如果瀏覽器沒有設置 MIME 類型，根據文件擴展名推斷
    const fileName = file.name.toLowerCase();
    if (fileName.endsWith('.md') || fileName.endsWith('.markdown')) {
      return 'text/md';
    }

    return 'application/octet-stream';
  };

  if (files.length === 1) {
    return getFileMimeType(files[0]);
  }

  // 多個文件：返回所有文件的 MIME 類型，用逗號分隔
  return files.map(getFileMimeType).join(',');
};

/**
 * 生成多文件上傳的 content_type 字符串（保持向後兼容）
 * @deprecated 請使用 generateContentType 函數
 * @param files 文件數組
 * @returns 逗號分隔的 MIME 類型字符串
 */
export const generateContentTypeString = (files: File[]): string => {
  return generateContentType(files);
};

/**
 * 驗證文件上傳請求的 content_type 字段格式
 * @param files 文件數組
 * @param contentType content_type 字符串
 * @returns 驗證結果
 */
export const validateContentType = (files: File[], contentType: string): {
  isValid: boolean;
  error?: string;
} => {
  if (!files || files.length === 0) {
    return { isValid: false, error: '文件數組不能為空' };
  }

  if (!contentType) {
    return { isValid: false, error: 'content_type 不能為空' };
  }

  const contentTypes = contentType.split(',');
  
  if (contentTypes.length !== files.length) {
    return { 
      isValid: false, 
      error: `content_type 中的 MIME 類型數量 (${contentTypes.length}) 與文件數量 (${files.length}) 不匹配` 
    };
  }

  // 檢查每個 MIME 類型是否與對應文件匹配
  for (let i = 0; i < files.length; i++) {
    const expectedType = files[i].type || 'application/octet-stream';
    const actualType = contentTypes[i].trim();
    
    if (expectedType !== actualType) {
      return {
        isValid: false,
        error: `第 ${i + 1} 個文件的 MIME 類型不匹配：期望 "${expectedType}"，實際 "${actualType}"`
      };
    }
  }

  return { isValid: true };
};

/**
 * 創建測試用的 File 對象
 * @param name 文件名
 * @param type MIME 類型
 * @param content 文件內容
 * @returns File 對象
 */
export const createTestFile = (name: string, type: string, content: string = ''): File => {
  const blob = new Blob([content], { type });
  return new File([blob], name, { type });
};

/**
 * 多文件上傳測試用例
 */
export const testMultiFileUpload = () => {
  console.log('🧪 開始多文件上傳測試...');

  // 測試用例 1: 單文件上傳
  const singleFile = [createTestFile('document.pdf', 'application/pdf', 'PDF content')];
  const singleContentType = generateContentTypeString(singleFile);
  console.log('📄 單文件測試:', {
    files: singleFile.map(f => ({ name: f.name, type: f.type })),
    contentType: singleContentType,
    validation: validateContentType(singleFile, singleContentType)
  });

  // 測試用例 2: 多文件上傳
  const multipleFiles = [
    createTestFile('file1.pdf', 'application/pdf', 'PDF content'),
    createTestFile('file2.md', 'text/markdown', '# Markdown content'),
    createTestFile('file3.jpg', 'image/jpeg', 'JPEG content'),
    createTestFile('file4.txt', 'text/plain', 'Plain text content')
  ];
  const multipleContentType = generateContentTypeString(multipleFiles);
  console.log('📁 多文件測試:', {
    files: multipleFiles.map(f => ({ name: f.name, type: f.type })),
    contentType: multipleContentType,
    validation: validateContentType(multipleFiles, multipleContentType)
  });

  // 測試用例 3: 無 MIME 類型的文件
  const filesWithoutType = [
    createTestFile('unknown1', '', 'Unknown content 1'),
    createTestFile('unknown2', '', 'Unknown content 2')
  ];
  const unknownContentType = generateContentTypeString(filesWithoutType);
  console.log('❓ 無類型文件測試:', {
    files: filesWithoutType.map(f => ({ name: f.name, type: f.type || 'empty' })),
    contentType: unknownContentType,
    validation: validateContentType(filesWithoutType, unknownContentType)
  });

  // 測試用例 4: 錯誤情況 - content_type 數量不匹配
  const mismatchValidation = validateContentType(multipleFiles, 'application/pdf,text/markdown');
  console.log('❌ 數量不匹配測試:', mismatchValidation);

  console.log('✅ 多文件上傳測試完成');
};
