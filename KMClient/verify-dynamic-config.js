#!/usr/bin/env node

/**
 * 動態配置驗證腳本
 * 驗證所有 API 請求都使用動態配置，沒有硬編碼 URL
 */

const fs = require('fs');
const path = require('path');

const CONFIG_PATH = path.join(__dirname, 'public/config.json');
const BACKUP_PATH = path.join(__dirname, 'public/config.json.backup');

// 測試配置
const TEST_CONFIGS = [
  {
    name: '測試配置 1',
    baseUrl: 'http://test-server-1.example.com:8083',
    appName: '動態配置測試 1'
  },
  {
    name: '測試配置 2', 
    baseUrl: 'http://test-server-2.example.com:9090',
    appName: '動態配置測試 2'
  }
];

// 讀取配置
function readConfig() {
  return JSON.parse(fs.readFileSync(CONFIG_PATH, 'utf8'));
}

// 寫入配置
function writeConfig(config) {
  fs.writeFileSync(CONFIG_PATH, JSON.stringify(config, null, 2));
}

// 備份配置
function backupConfig() {
  fs.copyFileSync(CONFIG_PATH, BACKUP_PATH);
  console.log('✅ 配置文件已備份');
}

// 恢復配置
function restoreConfig() {
  if (fs.existsSync(BACKUP_PATH)) {
    fs.copyFileSync(BACKUP_PATH, CONFIG_PATH);
    fs.unlinkSync(BACKUP_PATH);
    console.log('✅ 配置文件已恢復');
  }
}

// 應用測試配置
function applyTestConfig(testConfig) {
  const config = readConfig();
  config.api.gateway.baseUrl = testConfig.baseUrl;
  config.app.name = testConfig.appName;
  writeConfig(config);
  console.log(`🔄 已應用${testConfig.name}:`);
  console.log(`   baseUrl: ${testConfig.baseUrl}`);
  console.log(`   appName: ${testConfig.appName}`);
}

// 主測試流程
async function runTest() {
  console.log('🧪 開始動態配置驗證測試...');
  console.log('');
  
  // 備份原配置
  backupConfig();
  
  try {
    for (let i = 0; i < TEST_CONFIGS.length; i++) {
      const testConfig = TEST_CONFIGS[i];
      
      console.log(`📋 第 ${i + 1} 輪測試：${testConfig.name}`);
      applyTestConfig(testConfig);
      
      console.log('');
      console.log('🔍 請在瀏覽器中驗證：');
      console.log(`   1. 頁面標題是否變為 "${testConfig.appName}"`);
      console.log('   2. 控制台是否顯示配置更新日誌');
      console.log(`   3. API 請求是否使用新的 baseUrl: ${testConfig.baseUrl}`);
      console.log('   4. 檢查網絡面板中的 API 請求 URL');
      console.log('');
      
      if (i < TEST_CONFIGS.length - 1) {
        console.log('⏰ 10 秒後切換到下一個配置...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        console.log('');
      }
    }
    
    console.log('⏰ 10 秒後恢復原配置...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
  } catch (error) {
    console.error('❌ 測試失敗:', error);
  } finally {
    restoreConfig();
    console.log('✅ 動態配置驗證測試完成');
  }
}

// 處理中斷信號
process.on('SIGINT', () => {
  console.log('\n⚠️  測試被中斷，正在恢復配置...');
  restoreConfig();
  process.exit(0);
});

// 檢查是否在正確的目錄
if (!fs.existsSync(CONFIG_PATH)) {
  console.error('❌ 找不到 config.json 文件，請在項目根目錄運行此腳本');
  process.exit(1);
}

// 開始測試
runTest();
