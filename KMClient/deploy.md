# KM Client Docker 打包與生產部署指南

本指南說明如何將 KM Client（React + TypeScript + Vite）封裝為 Docker 映像，並在生產環境以 Nginx 靜態服務對外提供，同時保留 `config.json` 熱重載能力與多種 API 連線模式。

（本文內容與 deply.md 相同，統一檔名為 deploy.md）

---

## 1. 前置環境
- 建置端：Docker 24+、（可選）Docker Compose v2
- 執行端：Linux 主機或任意支援容器的環境（Kubernetes 亦可依此轉換）
- 原始碼：KM Client 專案根目錄（包含 `public/config.json`）

> 若不想在本機安裝 Node，建議直接使用下方 Docker 多階段建置流程；若需本機開發，請使用 Node 18/20。

---

## 2. 重要配置（public/config.json）
- `app.name` / `app.description`：應用名稱與描述（已接入熱重載，會即時反映至頁面標題、導覽列與歡迎訊息）
- `api.gateway.baseUrl`：API Gateway 對外位址（若 UI 為 https，請使用 https 端點）
- `features.hotReload`：
  - `enabled`：true 時前端會輪詢 `/config.json`，自動套用變更
  - `interval`：輪詢毫秒數（建議 3000–10000）

> 生產部署時請確保 `/config.json` 不被快取（Nginx 設定示例已內建）。

---

## 3. Dockerfile（多階段建置）
> 將以下內容保存為 `Dockerfile`（與專案根同層，亦即 KM Client 專案根目錄）。

```Dockerfile
# ========= Build stage =========
FROM node:20-alpine AS builder
WORKDIR /app
COPY . .
# 使用 ci 確保乾淨可重現；如使用 yarn/pnpm 自行替換
RUN npm ci && npm run build

# ========= Runtime stage (Nginx) =========
FROM nginx:1.27-alpine
# 拷貝打包產物至 Nginx 網站根目錄
COPY --from=builder /app/dist /usr/share/nginx/html

# 站台設定：SPA 與 config.json 不快取（支援熱重載）
RUN rm -f /etc/nginx/conf.d/default.conf \
  && printf 'server {\n\
    listen 80;\n\
    server_name _;\n\
    root /usr/share/nginx/html;\n\
    index index.html;\n\
\n\
    location / {\n\
      try_files $uri $uri/ /index.html;\n\
    }\n\
\n\
    # 關閉 config.json 快取，確保熱重載即時生效\n\
    location = /config.json {\n\
      add_header Cache-Control "no-cache, no-store, must-revalidate" always;\n\
      add_header Pragma "no-cache" always;\n\
      add_header Expires "0" always;\n\
      try_files $uri =404;\n\
    }\n\
\n\
    # 可選：同網域反向代理 API，避免 CORS（需要時解除註解並改為實際後端位址）\n\
    # location /svc/ {\n\
    #   proxy_pass https://api.example.com:8083/svc/;\n\
    #   proxy_set_header Host $host;\n\
    #   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n\
    #   proxy_set_header X-Forwarded-Proto $scheme;\n\
    # }\n\
  }\n' > /etc/nginx/conf.d/km-client.conf

EXPOSE 80
```

### .dockerignore（建議）
> 將以下內容保存為 `.dockerignore`，降低建置上下文體積與時間。

```
node_modules
dist
.git
.gitignore
.vscode
*.log
coverage
```

---

## 4. 本機建置與驗證

### 4.1 建置映像
```
docker build -t yourrepo/km-client:1.0.0 .
```

### 4.2 啟動容器（本機測試）
```
docker run --rm -p 8080:80 yourrepo/km-client:1.0.0
```
- 瀏覽 http://localhost:8080
- 必要時在網址後加上 `?TenantID=your_tenant` 以通過參數驗證

### 4.3 測試熱重載
- 編輯容器內 `/usr/share/nginx/html/config.json` 並立即生效較不方便，建議以 Volume 掛載（見下方 Compose）

---

## 5. Docker Compose（生產建議）
> 以下 `docker-compose.yml` 允許以宿主機檔案覆蓋容器內 `config.json`，以便零停機更新配置並透過熱重載即時生效。

```yaml
services:
  km-client:
    image: yourrepo/km-client:1.0.0
    container_name: km-client
    ports:
      - "80:80"                 # 對外提供 80；若前面有反代，可改用內部埠
    volumes:
      - /etc/km-client/config.json:/usr/share/nginx/html/config.json:ro
    restart: unless-stopped
```

部署步驟
1. 準備宿主機配置 `/etc/km-client/config.json`（調整 `api.gateway.baseUrl`、`features.hotReload` 等）
2. 上傳映像或在生產機上 `docker pull yourrepo/km-client:1.0.0`
3. 在生產機放置 `docker-compose.yml`，執行：
   - `docker compose up -d`
4. 更新映像：
   - `docker compose pull && docker compose up -d`
5. 更新配置（熱重載，零停機）：
   - 直接編輯 `/etc/km-client/config.json`，等待 `features.hotReload.interval`（例如 5 秒）即可

> 若需 HTTPS 與網域，建議在前面加一層反向代理（Nginx/Caddy/Traefik）做 TLS 終結與流量治理。

---

## 6. 發佈到生產（CI/CD 參考）

### 6.1 推送映像到容器倉庫
```
docker login
# 建置
docker build -t yourrepo/km-client:1.0.0 .
# 推送
docker push yourrepo/km-client:1.0.0
```

### 6.2 生產機滾動發布（以 Compose 為例）
```
# 拉取新版本
docker compose pull
# 套用新版本（不中斷）
docker compose up -d
```

### 6.3 回滾
```
# 切換回上一個 tag
docker compose down
# 將 docker-compose.yml 或部署指令改回上一版 tag 後
docker compose up -d
```

---

## 7. 驗證清單（Production）
- 以 `https://your-domain/?TenantID=xxx` 開啟
- 首頁載入正常、Console 無重大錯誤
- 聊天/附件功能可用（視後端）
- 編輯 `/etc/km-client/config.json` → 修改 `app.name` 或 `description`
  - 約 5 秒（預設 interval）內：
    - 瀏覽器分頁標題更新
    - 導覽列應用名更新
    - 歡迎訊息更新
- 跨網域情境：API CORS 設定正確或使用同網域反向代理 `/svc`

---

## 8. 常見問題（FAQ）
- `config.json` 變更沒有即時生效
  - 確認 `features.hotReload.enabled = true`
  - 反向代理/前置 CDN 對 `/config.json` 不可長時間快取，務必加上 `Cache-Control: no-cache`
  - 確認您修改的是生產機上的實際檔案（若使用 Volume：`/etc/km-client/config.json`）

- HTTPS UI + HTTP API 導致混合內容被阻擋
  - 請改用 HTTPS API，或以同網域反向代理將 HTTPS 請求轉至後端

- CORS 被阻擋
  - 在 API 伺服器開啟 CORS 允許 UI 網域，或採同網域反向代理（啟用上方 `location /svc/`）

- 中斷最小化
  - 使用 `docker compose pull && docker compose up -d` 做滾動更新
  - 配置更新靠熱重載，無需重啟容器

---

## 9. 安全與維護建議
- 將 `config.json` 版本化管理，修改前後保留備份
- 倚賴 Compose 以便快速更新/回滾；Kubernetes 可用 Deployment + ConfigMap（以 Volume 覆蓋 `config.json`）
- 前置反向代理處理 HTTPS 與 WAF/Rate Limit，Nginx 容器僅做靜態檔服務
- 監控：`docker logs -f km-client`、反代層與 API 伺服器日誌

---

## 10. 單機 docker run 範例（無 Compose）
```
# 建議將 config.json 放在 /etc/km-client/config.json

docker run -d --name km-client \
  -p 80:80 \
  -v /etc/km-client/config.json:/usr/share/nginx/html/config.json:ro \
  yourrepo/km-client:1.0.0
```

> 若需同網域反向代理 API，參見 Dockerfile 內的 `location /svc/` 範例，並將 `proxy_pass` 指向實際後端。

