import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  timeout: 60_000, // 增加超時時間以適應移動端測試
  expect: { timeout: 10_000 }, // 增加期望超時時間
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }]
  ],
  use: {
    baseURL: 'http://localhost:3001',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    // 移動端測試的通用設置
    actionTimeout: 15_000,
    navigationTimeout: 30_000,
  },
  projects: [
    // 桌面端測試
    {
      name: 'Desktop Chrome',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1280, height: 800 },
      },
    },
    {
      name: 'Desktop Firefox',
      use: {
        ...devices['Desktop Firefox'],
        viewport: { width: 1280, height: 800 },
      },
    },
    {
      name: 'Desktop Safari',
      use: {
        ...devices['Desktop Safari'],
        viewport: { width: 1280, height: 800 },
      },
    },

    // 移動端測試 - 手機
    {
      name: 'Mobile Chrome',
      use: {
        ...devices['Pixel 5'],
        // 模擬移動端網絡條件
        contextOptions: {
          permissions: ['camera', 'microphone', 'geolocation'],
        },
      },
    },
    {
      name: 'Mobile Safari',
      use: {
        ...devices['iPhone 12'],
        contextOptions: {
          permissions: ['camera', 'microphone', 'geolocation'],
        },
      },
    },
    {
      name: 'Mobile Safari Landscape',
      use: {
        ...devices['iPhone 12 landscape'],
        contextOptions: {
          permissions: ['camera', 'microphone', 'geolocation'],
        },
      },
    },

    // 平板測試
    {
      name: 'Tablet Chrome',
      use: {
        ...devices['iPad Pro'],
        contextOptions: {
          permissions: ['camera', 'microphone', 'geolocation'],
        },
      },
    },
    {
      name: 'Tablet Safari',
      use: {
        ...devices['iPad Pro landscape'],
        contextOptions: {
          permissions: ['camera', 'microphone', 'geolocation'],
        },
      },
    },

    // 小屏幕設備測試
    {
      name: 'Small Mobile',
      use: {
        ...devices['iPhone SE'],
        contextOptions: {
          permissions: ['camera', 'microphone', 'geolocation'],
        },
      },
    },

    // 大屏幕設備測試
    {
      name: 'Large Mobile',
      use: {
        ...devices['Pixel 5'],
        viewport: { width: 414, height: 896 }, // iPhone 11 Pro Max size
        contextOptions: {
          permissions: ['camera', 'microphone', 'geolocation'],
        },
      },
    },
  ],

  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3001',
    reuseExistingServer: !process.env.CI,
    timeout: 120_000,
  },
});

