<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content-Type 字段測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .test-result {
            background: #e8f5e8;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #4caf50;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .test-error {
            background: #ffe8e8;
            border-left-color: #f44336;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .file-info {
            background: #f0f8ff;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }
        .api-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📤 多文件上傳 Content-Type 字段測試</h1>
        
        <div class="test-section">
            <div class="test-title">🧪 測試說明</div>
            <p>此測試驗證多文件上傳功能中 <code>content_type</code> 字段的生成邏輯：</p>
            <ul>
                <li><strong>單文件上傳</strong>：content_type 包含單一 MIME 類型</li>
                <li><strong>多文件上傳</strong>：content_type 包含逗號分隔的 MIME 類型列表</li>
                <li><strong>順序保持</strong>：MIME 類型順序與文件順序完全一致</li>
                <li><strong>默認處理</strong>：無 MIME 類型的文件使用 "application/octet-stream"</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 測試控制</div>
            <button onclick="runAllTests()">運行所有測試</button>
            <button onclick="runSingleFileTest()">單文件測試</button>
            <button onclick="runMultiFileTest()">多文件測試</button>
            <button onclick="runEdgeCaseTests()">邊界情況測試</button>
            <button onclick="clearResults()">清除結果</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        // 工具函數：生成 content_type 字符串
        function generateContentTypeString(files) {
            if (!files || files.length === 0) {
                return '';
            }
            const contentTypes = files.map(file => file.type || 'application/octet-stream');
            return contentTypes.join(',');
        }

        // 工具函數：驗證 content_type 格式
        function validateContentType(files, contentType) {
            if (!files || files.length === 0) {
                return { isValid: false, error: '文件數組不能為空' };
            }
            if (!contentType) {
                return { isValid: false, error: 'content_type 不能為空' };
            }

            const contentTypes = contentType.split(',');
            if (contentTypes.length !== files.length) {
                return { 
                    isValid: false, 
                    error: `content_type 中的 MIME 類型數量 (${contentTypes.length}) 與文件數量 (${files.length}) 不匹配` 
                };
            }

            for (let i = 0; i < files.length; i++) {
                const expectedType = files[i].type || 'application/octet-stream';
                const actualType = contentTypes[i].trim();
                if (expectedType !== actualType) {
                    return {
                        isValid: false,
                        error: `第 ${i + 1} 個文件的 MIME 類型不匹配：期望 "${expectedType}"，實際 "${actualType}"`
                    };
                }
            }
            return { isValid: true };
        }

        // 工具函數：創建測試文件
        function createTestFile(name, type, content = '') {
            const blob = new Blob([content], { type });
            return new File([blob], name, { type });
        }

        // 顯示測試結果
        function displayResult(title, result, isError = false) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-section';
            
            const resultClass = isError ? 'test-result test-error' : 'test-result';
            resultDiv.innerHTML = `
                <div class="test-title">${title}</div>
                <div class="${resultClass}">${JSON.stringify(result, null, 2)}</div>
            `;
            
            resultsDiv.appendChild(resultDiv);
        }

        // 單文件上傳測試
        function runSingleFileTest() {
            console.log('🧪 運行單文件上傳測試...');
            
            const singleFile = [createTestFile('document.pdf', 'application/pdf', 'PDF content')];
            const contentType = generateContentTypeString(singleFile);
            const validation = validateContentType(singleFile, contentType);
            
            const result = {
                testType: '單文件上傳',
                files: singleFile.map(f => ({ name: f.name, type: f.type, size: f.size })),
                contentType: contentType,
                validation: validation,
                apiRequest: {
                    file: singleFile.map(f => f.name),
                    content_type: contentType
                }
            };
            
            displayResult('📄 單文件上傳測試結果', result);
            return result;
        }

        // 多文件上傳測試
        function runMultiFileTest() {
            console.log('🧪 運行多文件上傳測試...');
            
            const multipleFiles = [
                createTestFile('file1.pdf', 'application/pdf', 'PDF content'),
                createTestFile('file2.md', 'text/markdown', '# Markdown content'),
                createTestFile('file3.jpg', 'image/jpeg', 'JPEG binary data'),
                createTestFile('file4.txt', 'text/plain', 'Plain text content')
            ];
            
            const contentType = generateContentTypeString(multipleFiles);
            const validation = validateContentType(multipleFiles, contentType);
            
            const result = {
                testType: '多文件上傳',
                fileCount: multipleFiles.length,
                files: multipleFiles.map(f => ({ name: f.name, type: f.type, size: f.size })),
                contentType: contentType,
                contentTypeArray: contentType.split(','),
                validation: validation,
                apiRequest: {
                    file: multipleFiles.map(f => f.name),
                    content_type: contentType
                }
            };
            
            displayResult('📁 多文件上傳測試結果', result);
            return result;
        }

        // 邊界情況測試
        function runEdgeCaseTests() {
            console.log('🧪 運行邊界情況測試...');
            
            // 測試 1: 無 MIME 類型的文件
            const filesWithoutType = [
                createTestFile('unknown1', '', 'Unknown content 1'),
                createTestFile('unknown2', '', 'Unknown content 2')
            ];
            const unknownContentType = generateContentTypeString(filesWithoutType);
            const unknownValidation = validateContentType(filesWithoutType, unknownContentType);
            
            displayResult('❓ 無 MIME 類型文件測試', {
                testType: '無 MIME 類型文件',
                files: filesWithoutType.map(f => ({ name: f.name, type: f.type || '(empty)', size: f.size })),
                contentType: unknownContentType,
                validation: unknownValidation
            });

            // 測試 2: content_type 數量不匹配
            const testFiles = [
                createTestFile('file1.pdf', 'application/pdf'),
                createTestFile('file2.txt', 'text/plain')
            ];
            const mismatchValidation = validateContentType(testFiles, 'application/pdf');
            
            displayResult('❌ 數量不匹配測試', {
                testType: 'content_type 數量不匹配',
                files: testFiles.map(f => ({ name: f.name, type: f.type })),
                providedContentType: 'application/pdf',
                expectedContentType: generateContentTypeString(testFiles),
                validation: mismatchValidation
            }, true);

            // 測試 3: 空文件數組
            const emptyValidation = validateContentType([], '');
            displayResult('🚫 空文件數組測試', {
                testType: '空文件數組',
                validation: emptyValidation
            }, true);
        }

        // 運行所有測試
        function runAllTests() {
            clearResults();
            console.log('🚀 開始運行所有測試...');
            
            runSingleFileTest();
            runMultiFileTest();
            runEdgeCaseTests();
            
            // 顯示 API 使用示例
            const apiExample = `
// 前端實現示例
const handleMultipleUpload = async (files) => {
  // 生成 content_type 字符串
  const contentTypes = files.map(file => file.type || 'application/octet-stream');
  const contentTypeString = contentTypes.join(',');

  // API 請求
  await uploadFiles({
    file: files,
    tenant_id: userInfo.tenant_id,
    service_id: userInfo.service_id,
    user_id: userInfo.user_id,
    content_type: contentTypeString, // 關鍵字段
  });
};

// 示例請求體
{
  "file": ["file1.pdf", "file2.md", "file3.jpg"],
  "tenant_id": "*********-0001",
  "service_id": "dbc2cd12-3d42-1bbe-6728-0b03b2c19440",
  "user_id": "00000000-0000-0000-1002-000000000001",
  "content_type": "application/pdf,text/markdown,image/jpeg"
}`;
            
            const resultsDiv = document.getElementById('results');
            const apiDiv = document.createElement('div');
            apiDiv.className = 'test-section';
            apiDiv.innerHTML = `
                <div class="test-title">💻 API 使用示例</div>
                <div class="api-example">${apiExample}</div>
            `;
            resultsDiv.appendChild(apiDiv);
            
            console.log('✅ 所有測試完成');
        }

        // 清除結果
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 頁面載入時自動運行測試
        window.onload = function() {
            console.log('📋 Content-Type 字段測試頁面已載入');
        };
    </script>
</body>
</html>
