# 📱 KM Client 移動應用開發規格文件

## 📋 文件資訊
- **專案名稱**: KM Client Mobile App
- **版本**: 1.0.0
- **建立日期**: 2025-01-19
- **最後更新**: 2025-01-19
- **作者**: <PERSON> Liu

---

## 1. 📖 專案概述與目標

### 1.1 專案背景
KM Client 是一個基於 React 的知識管理客戶端，目前以 Web 應用形式提供服務。為了擴展用戶覆蓋範圍並提供更好的移動端體驗，需要將其轉換為原生移動應用程式。

### 1.2 專案目標
- **完整功能移植**: 將現有 Web 端所有功能完整移植到移動端
- **跨平台支援**: 同時支援 iOS 和 Android 平台
- **原生體驗**: 採用符合移動端設計規範的 UI/UX
- **性能優化**: 針對移動設備進行性能優化
- **離線支援**: 後續階段添加離線功能和推送通知

### 1.3 預期成果
- 發布到 App Store 和 Google Play Store
- 提供與 Web 端一致的功能體驗
- 支援移動端特有的交互方式
- 建立可持續的移動端開發流程

---

## 2. 🏗️ 技術架構說明

### 2.1 前端技術棧

#### 核心框架
- **React 19**: 前端框架，保持與現有 Web 端一致
- **TypeScript**: 類型安全的 JavaScript 超集
- **Capacitor**: 跨平台移動應用框架，提供原生功能訪問

#### UI 組件庫
- **Ant Design Mobile**: 專為移動端設計的 React UI 組件庫
- **Tailwind CSS**: 實用優先的 CSS 框架，用於自定義樣式
- **Framer Motion**: 動畫庫，提供流暢的動畫效果

#### 狀態管理與工具
- **Zustand**: 輕量級狀態管理庫
- **Axios**: HTTP 客戶端，用於 API 請求
- **React Router**: 路由管理
- **Vite**: 構建工具

### 2.2 後端 API 架構

#### 微服務架構
現有後端採用微服務架構，包含以下服務：

1. **tenants.svc**: 租戶管理服務
   - 系統指令設置 (`/getSysInstruction`, `/setSysInstruction`)

2. **ams.svc**: 附件管理服務
   - 文件上傳/下載 (`/upload`, `/download`)
   - 網站管理 (`/setWebSite`, `/deleteWebSite`)
   - YouTube 管理 (`/setYoutubeLink`, `/deleteYoutubeLink`)
   - 純文本管理 (`/setPlainText`, `/deletePlainText`)

3. **brainHub.svc**: 智慧聊天服務
   - 聊天對話 (`/chat`)
   - 會話管理 (`/session/create`, `/session/history`, `/session/list`)

4. **channelHub.svc**: 通道服務
   - Line 聊天支援 (`/chat`, `/webhook`)

5. **quizto.svc**: 知識管理服務
   - 搜索和索引 (`/search`, `/index`)

6. **頭像服務**: 獨立的頭像管理服務
   - 頭像更新 (`POST /job/api/platform/servicenumber/update`)
   - 使用獨立的 API 配置 (baseUrl: `https://newaile.prod.aile.cloud`)
   - HMAC-SHA256 加密認證機制

#### API 網關
- **基礎 URL**: 可配置的 API 網關地址
- **統一格式**: `/svc/{service_name}/{path}/{endpoint}`
- **認證機制**: 基於 URL 參數的租戶驗證

### 2.3 資料流向與狀態管理

#### 全局狀態結構
```typescript
interface AppState {
  // URL 參數與驗證
  urlParams: URLParams;
  isParamsValid: boolean;
  
  // 應用狀態
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  
  // UI 狀態
  isDarkMode: boolean;
  sidebarCollapsed: boolean;
  
  // 業務數據
  attachments: Attachment[];
  currentSession: ChatSession | null;
  sessions: ChatSession[];
  userInfo: UserInfo;
}
```

#### 數據流向
1. **初始化**: URL 參數解析 → 用戶信息設置 → 配置載入
2. **附件管理**: 用戶操作 → API 請求 → 狀態更新 → UI 重新渲染
3. **聊天功能**: 消息發送 → API 請求 → 消息解析 → 會話更新

---

## 3. 🎯 功能模組規劃

### 3.1 核心功能模組

#### 3.1.1 系統指令設置模組
- **功能描述**: 管理全局和服務特定的系統指令
- **主要組件**: SystemInstructionsPage
- **API 依賴**: tenants.svc
- **移動端適配**: 
  - 使用 Ant Design Mobile 的 TextArea 組件
  - 優化觸控操作體驗
  - 支援語音輸入（後續功能）

#### 3.1.2 智慧聊天模組
- **功能描述**: AI 對話、會話管理、消息解析
- **主要組件**: ChatPanel, MessageRenderer
- **API 依賴**: brainHub.svc, channelHub.svc
- **移動端適配**:
  - 全屏聊天界面
  - 觸控友好的輸入框
  - 滑動手勢支援
  - 語音輸入支援（後續功能）

#### 3.1.3 服務管理模組
- **功能描述**: 整合服務號的完整管理功能，包含頭像設置和知識內容管理
- **主要組件**: AvatarSettingsPanel, AttachmentPanel, FileUploadTab
- **API 依賴**:
  - 頭像服務: 獨立頭像服務 (https://newaile.prod.aile.cloud)
  - 知識內容: ams.svc
- **子功能模組**:
  - **頭像設置**: 服務號頭像管理和更新
  - **知識內容管理**: 文件、網站、YouTube、純文本管理
- **移動端適配**:
  - 相機拍照上傳（頭像和文件）
  - 文件系統訪問
  - 拖拽上傳改為點擊上傳
  - 縮略圖預覽
  - 圖片裁剪和壓縮
  - 即時預覽功能
  - 支援多種圖片格式 (PNG, JPG, JPEG, GIF)

### 3.2 移動端特有功能

#### 3.2.1 相機整合
- **功能**: 直接拍照上傳文件
- **實現**: Capacitor Camera Plugin
- **用戶場景**: 快速捕捉文檔、圖片

#### 3.2.2 文件系統訪問
- **功能**: 訪問設備文件系統
- **實現**: Capacitor Filesystem Plugin
- **用戶場景**: 選擇和上傳本地文件

#### 3.2.3 推送通知（後續功能）
- **功能**: 接收聊天回覆通知
- **實現**: Capacitor Push Notifications Plugin
- **用戶場景**: 及時獲得 AI 回覆提醒

### 3.3 功能優先級排序

#### 第一階段（MVP）
1. ✅ 基礎聊天功能
2. ✅ 服務管理（頭像設置 + 知識內容管理）
3. ✅ 系統指令設置
4. ✅ 用戶認證與會話管理

#### 第二階段
1. 🔄 相機整合
2. 🔄 文件系統訪問
3. 🔄 離線數據緩存
4. 🔄 性能優化

#### 第三階段
1. ⏳ 推送通知
2. ⏳ 語音輸入
3. ⏳ 生物識別認證
4. ⏳ 多語言支援

---

## 4. 🎨 UI/UX 設計規範

### 4.1 移動端設計原則

#### 4.1.1 觸控優先設計
- **最小觸控目標**: 44px × 44px
- **手勢支援**: 滑動、長按、雙擊
- **反饋機制**: 觸覺反饋、視覺反饋

#### 4.1.2 內容優先
- **簡化導航**: 減少層級，直達核心功能
- **關鍵信息突出**: 重要內容優先顯示
- **漸進式披露**: 按需顯示詳細信息

#### 4.1.3 性能導向
- **快速載入**: 優化首屏載入時間
- **流暢動畫**: 60fps 動畫效果
- **節省電量**: 合理使用動畫和背景任務

### 4.2 組件庫使用規範

#### 4.2.1 Ant Design Mobile 組件對應

| Web 端組件 | 移動端組件 | 用途 |
|-----------|-----------|------|
| Button | Button | 按鈕操作 |
| Input | Input | 文本輸入 |
| TextArea | TextArea | 多行文本 |
| Card | Card | 內容容器 |
| List | List | 列表展示 |
| Modal | Modal | 彈窗對話 |
| Drawer | Drawer | 側邊欄 |
| Tabs | Tabs | 標籤切換 |

#### 4.2.2 自定義組件
- **SpotlightCard**: 保留科技感設計，適配移動端尺寸
- **MessageRenderer**: 優化消息顯示，支援觸控操作
- **AttachmentPreview**: 新增縮略圖預覽功能

### 4.3 響應式設計策略

#### 4.3.1 斷點設計
- **手機豎屏**: < 768px
- **手機橫屏**: 768px - 1024px
- **平板**: > 1024px

#### 4.3.2 佈局適配
- **單欄佈局**: 手機端採用單欄設計
- **標籤切換**: 附件管理和聊天功能通過底部標籤切換
- **全屏模式**: 聊天界面支援全屏模式

### 4.4 無障礙設計考量

#### 4.4.1 視覺輔助
- **高對比度**: 支援高對比度模式
- **字體縮放**: 支援系統字體大小設置
- **色彩無關**: 不僅依賴顏色傳達信息

#### 4.4.2 操作輔助
- **語音標籤**: 為所有交互元素添加 aria-label
- **鍵盤導航**: 支援外接鍵盤操作
- **屏幕閱讀器**: 兼容主流屏幕閱讀器

---

## 5. 🔌 API 接口規格

### 5.1 現有 API 接口分析

#### 5.1.1 認證機制
```typescript
interface URLParams {
  tenantId: string;    // 必要參數
  serviceId?: string;  // 可選參數
  userId?: string;     // 可選參數
}
```

#### 5.1.2 統一響應格式
```typescript
interface ApiResponse<T = any> {
  code: number;        // 0: 成功, 非0: 錯誤
  message: string;     // 響應消息
  data?: T;           // 響應數據
  timestamp?: number;  // 時間戳
}
```

### 5.2 移動端特殊需求 API

#### 5.2.1 文件上傳增強
```typescript
interface MobileUploadRequest extends UploadFileRequest {
  source: 'camera' | 'gallery' | 'files';  // 文件來源
  compression?: {
    quality: number;    // 圖片壓縮質量 0-1
    maxWidth: number;   // 最大寬度
    maxHeight: number;  // 最大高度
  };
}
```

#### 5.2.2 離線數據同步（後續功能）
```typescript
interface SyncRequest {
  lastSyncTime: string;
  deviceId: string;
  changes: {
    attachments: Attachment[];
    messages: ChatMessage[];
    sessions: ChatSession[];
  };
}
```

### 5.3 錯誤處理策略

#### 5.3.1 網絡錯誤處理
- **重試機制**: 自動重試 3 次
- **降級策略**: 網絡不佳時使用緩存數據
- **用戶提示**: 友好的錯誤提示信息

#### 5.3.2 業務錯誤處理
- **參數驗證**: 客戶端預驗證
- **權限檢查**: 統一權限驗證機制
- **數據完整性**: 關鍵操作的數據校驗

---

## 6. ⚙️ 開發環境配置

### 6.1 Capacitor 環境設置

#### 6.1.1 基礎安裝
```bash
# 安裝 Capacitor CLI
npm install -g @capacitor/cli

# 初始化 Capacitor
npx cap init "KM Client" "com.kmclient.app"

# 添加平台
npx cap add ios
npx cap add android
```

#### 6.1.2 必要插件
```bash
# 核心插件
npm install @capacitor/app @capacitor/haptics @capacitor/keyboard
npm install @capacitor/status-bar @capacitor/splash-screen

# 功能插件
npm install @capacitor/camera @capacitor/filesystem
npm install @capacitor/push-notifications @capacitor/local-notifications
```

### 6.2 開發工具鏈配置

#### 6.2.1 IDE 配置
- **VS Code**: 主要開發環境
- **必要擴展**: 
  - TypeScript and JavaScript Language Features
  - ES7+ React/Redux/React-Native snippets
  - Tailwind CSS IntelliSense
  - Capacitor

#### 6.2.2 調試工具
- **Chrome DevTools**: Web 端調試
- **Safari Web Inspector**: iOS 調試
- **Chrome Remote Debugging**: Android 調試
- **Flipper**: React Native 調試工具

### 6.3 構建與打包流程

#### 6.3.1 開發構建
```bash
# Web 端開發
npm run dev

# 移動端開發
npm run build
npx cap sync
npx cap run ios
npx cap run android
```

#### 6.3.2 生產構建
```bash
# 構建 Web 資源
npm run build

# 同步到原生平台
npx cap sync

# 生成原生應用
npx cap build ios
npx cap build android
```

---

## 7. 🚀 部署與發布流程

### 7.1 應用商店發布準備

#### 7.1.1 iOS App Store
- **開發者帳號**: Apple Developer Program
- **應用簽名**: Distribution Certificate
- **應用圖標**: 1024×1024 高解析度圖標
- **截圖**: 各種設備尺寸的應用截圖
- **隱私政策**: 符合 App Store 審核指南

#### 7.1.2 Google Play Store
- **開發者帳號**: Google Play Console
- **應用簽名**: Play App Signing
- **應用圖標**: 512×512 高解析度圖標
- **功能圖片**: 1024×500 特色圖片
- **隱私政策**: 符合 Google Play 政策

### 7.2 版本管理策略

#### 7.2.1 版本號規則
- **格式**: MAJOR.MINOR.PATCH
- **MAJOR**: 重大功能更新或架構變更
- **MINOR**: 新功能添加
- **PATCH**: 錯誤修復和小幅改進

#### 7.2.2 發布週期
- **Beta 版本**: 每週發布，內部測試
- **Release Candidate**: 每月發布，外部測試
- **正式版本**: 每季度發布，公開發布

### 7.3 持續集成/持續部署

#### 7.3.1 CI/CD 流程
```yaml
# GitHub Actions 示例
name: Build and Deploy
on:
  push:
    branches: [main]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Build
        run: npm run build
      - name: Sync Capacitor
        run: npx cap sync
```

### 7.4 更新機制設計

#### 7.4.1 熱更新
- **Web 資源**: 通過 Capacitor 實現熱更新
- **原生代碼**: 需要通過應用商店更新
- **配置文件**: 支援遠程配置更新

#### 7.4.2 版本檢查
- **自動檢查**: 應用啟動時檢查更新
- **強制更新**: 關鍵安全更新的強制升級
- **用戶選擇**: 一般更新允許用戶選擇時機

---

## 8. 🧪 測試策略

### 8.1 單元測試規劃

#### 8.1.1 測試框架
- **Vitest**: 單元測試框架
- **React Testing Library**: React 組件測試
- **Jest DOM**: DOM 測試工具

#### 8.1.2 測試覆蓋範圍
- **工具函數**: 100% 覆蓋率
- **服務層**: 90% 覆蓋率
- **組件**: 80% 覆蓋率
- **頁面**: 70% 覆蓋率

### 8.2 整合測試策略

#### 8.2.1 API 測試
- **Mock 服務**: 使用 MSW 模擬 API
- **端到端測試**: 真實 API 環境測試
- **錯誤場景**: 網絡錯誤、服務錯誤測試

#### 8.2.2 跨平台測試
- **iOS 測試**: iPhone、iPad 各種尺寸
- **Android 測試**: 不同廠商、不同版本
- **性能測試**: 低端設備性能測試

### 8.3 移動端特殊測試需求

#### 8.3.1 設備功能測試
- **相機功能**: 拍照、選擇圖片
- **文件系統**: 文件讀寫權限
- **網絡狀態**: 離線、弱網環境

#### 8.3.2 用戶體驗測試
- **觸控操作**: 點擊、滑動、長按
- **屏幕旋轉**: 橫豎屏切換
- **多任務**: 應用切換、後台運行

### 8.4 性能測試與優化

#### 8.4.1 性能指標
- **首屏載入時間**: < 3 秒
- **頁面切換時間**: < 500ms
- **內存使用**: < 100MB
- **電池消耗**: 合理範圍內

#### 8.4.2 優化策略
- **代碼分割**: 按需載入組件
- **圖片優化**: WebP 格式、懶載入
- **緩存策略**: 合理的緩存機制
- **動畫優化**: GPU 加速動畫

---

## 9. 📅 開發時程與里程碑

### 9.1 開發階段劃分

#### 第一階段：基礎架構搭建（2週）
- **Week 1**: Capacitor 環境搭建、基礎配置
- **Week 2**: UI 組件庫整合、路由配置

#### 第二階段：核心功能開發（6週）
- **Week 3-4**: 聊天功能移植與優化
- **Week 5-6**: 附件管理功能移植
- **Week 7-8**: 系統指令設置功能移植

#### 第三階段：移動端特有功能（4週）
- **Week 9-10**: 相機整合、文件系統訪問
- **Week 11-12**: UI/UX 優化、性能調優

#### 第四階段：測試與發布（4週）
- **Week 13-14**: 全面測試、錯誤修復
- **Week 15-16**: 應用商店提交、發布準備

### 9.2 關鍵里程碑設定

#### 里程碑 1：技術驗證（Week 2）
- ✅ Capacitor 環境搭建完成
- ✅ 基礎 UI 組件正常運行
- ✅ API 服務正常連接

#### 里程碑 2：功能完整性（Week 8）
- ✅ 所有 Web 端功能移植完成
- ✅ 基礎移動端適配完成
- ✅ 核心用戶流程可用

#### 里程碑 3：移動端優化（Week 12）
- ✅ 移動端特有功能完成
- ✅ 性能優化達標
- ✅ 用戶體驗優化完成

#### 里程碑 4：發布就緒（Week 16）
- ✅ 全面測試通過
- ✅ 應用商店審核通過
- ✅ 正式發布上線

### 9.3 資源配置計劃

#### 9.3.1 人力資源
- **前端開發**: 2 人
- **UI/UX 設計**: 1 人
- **測試工程師**: 1 人
- **專案經理**: 1 人

#### 9.3.2 技術資源
- **開發設備**: iOS 和 Android 測試設備
- **開發者帳號**: Apple Developer、Google Play Console
- **CI/CD 服務**: GitHub Actions 或類似服務
- **測試服務**: 真機測試雲服務

### 9.4 風險評估與應對

#### 9.4.1 技術風險
- **風險**: Capacitor 插件兼容性問題
- **應對**: 提前進行技術驗證，準備替代方案

#### 9.4.2 時程風險
- **風險**: 開發進度延遲
- **應對**: 設置緩衝時間，優先級管理

#### 9.4.3 質量風險
- **風險**: 移動端性能不達標
- **應對**: 持續性能監控，及時優化

---

## 10. 📚 附錄

### 10.1 技術選型對比

#### 10.1.1 跨平台框架對比

| 框架 | 優勢 | 劣勢 | 選擇原因 |
|------|------|------|----------|
| Capacitor | Web 技術棧、易於遷移 | 性能略低於原生 | ✅ 與現有技術棧匹配 |
| React Native | 接近原生性能 | 需要重寫大量代碼 | ❌ 遷移成本高 |
| Flutter | 高性能、統一 UI | 全新技術棧 | ❌ 學習成本高 |

#### 10.1.2 UI 組件庫對比

| 組件庫 | 優勢 | 劣勢 | 選擇原因 |
|--------|------|------|----------|
| Ant Design Mobile | 成熟穩定、中文友好 | 定制化程度有限 | ✅ 符合項目需求 |
| React Native Elements | 高度可定制 | 需要更多開發工作 | ❌ 開發成本高 |
| NativeBase | 跨平台一致性好 | 社區相對較小 | ❌ 生態不夠成熟 |

### 10.2 第三方依賴清單

#### 10.2.1 核心依賴
```json
{
  "@capacitor/core": "^5.0.0",
  "@capacitor/ios": "^5.0.0",
  "@capacitor/android": "^5.0.0",
  "antd-mobile": "^5.0.0",
  "react": "^19.1.0",
  "typescript": "^5.8.3"
}
```

#### 10.2.2 功能插件
```json
{
  "@capacitor/camera": "^5.0.0",
  "@capacitor/filesystem": "^5.0.0",
  "@capacitor/push-notifications": "^5.0.0",
  "@capacitor/local-notifications": "^5.0.0"
}
```

### 10.3 開發規範與最佳實踐

#### 10.3.1 代碼規範
- **TypeScript**: 嚴格模式，完整類型定義
- **ESLint**: 統一代碼風格檢查
- **Prettier**: 自動代碼格式化
- **Husky**: Git hooks 自動化檢查

#### 10.3.2 移動端最佳實踐
- **性能優化**: 避免不必要的重新渲染
- **內存管理**: 及時清理事件監聽器
- **電池優化**: 合理使用背景任務
- **網絡優化**: 實現請求去重和緩存

#### 10.3.3 安全考量
- **數據加密**: 敏感數據本地加密存儲
- **網絡安全**: HTTPS 通信，證書驗證
- **權限管理**: 最小權限原則
- **代碼混淆**: 生產版本代碼混淆

---

## 📝 總結

本規格文件詳細規劃了 KM Client 從 Web 應用到移動應用的完整轉換方案。通過採用 React + Capacitor + Ant Design Mobile 的技術棧，我們能夠：

1. **最大化代碼復用**: 保持與現有 Web 端的技術一致性
2. **快速上市**: 利用成熟的跨平台解決方案
3. **原生體驗**: 提供符合移動端設計規範的用戶體驗
4. **可持續發展**: 建立完整的移動端開發和發布流程

該方案在保證功能完整性的同時，充分考慮了移動端的特殊需求和用戶體驗，為 KM Client 的移動化轉型奠定了堅實的基礎。

---

**文件版本**: 1.0.0  
**最後更新**: 2025-01-19  
**下次審查**: 2025-02-19
