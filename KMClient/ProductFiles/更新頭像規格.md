# 更新服務號基本資料(名稱/頭圖)

**POST** `/job/api/platform/servicenumber/update`

## 說明

使用系統應用授權方式，更新指定服務號的頭圖和名稱，必須使用 `formdata` 格式。

## Request

### 參數

| 參數名 | 必填 | 類型 | 說明 |
|--------|------|------|------|
| `serviceNumberId` | 是 | String | 服務號ID |
| `name` | 否 | String | 服務號名稱 |
| `file` | 否 | file | 服務號頭圖文件 |

## Response

若發送成功，`data` 會返回更新後的相關資料。

## Request 範例

### HTTP Header

HTTP Header 必須包含以下屬性：

```
aileMode = develop
Authorization = [非文件數據參數組成json加密後的字符串]
nonce = [隨機數]
```

### HTTP Body

HTTP Body 為 `formdata` 格式數據。

## Authorization 加密腳本參考 (Apifox)

```javascript
// 獲取所有 formdata 參數
let data = formdata.toObject();
formdata.each((item) => {
    console.log("處理表單項:", item);
    // 處理禁用項和文件類型項
    if (item.disabled || item.type === 'file') {
        delete data[item.key];
    }
});

function generateHmacAuthorization(appId, appSecret, nonce, data) {
    console.log("start generateHmacAuthorization");
    console.log("appId = " + appId);
    console.log("appSecret = " + appSecret);
    console.log("nonce = " + nonce);
    console.log("data = " + JSON.stringify(data));
    let source = "";
    if (typeof data === 'string') {
        console.log("data is string");
        source = data;
    } else if (typeof data === 'object') {
        source = canonicalJson(data);
    }

    // 這裡是將原始數據組合
    const fullData = appId + nonce + source;

    // HMAC-SHA256，key 直接用字符串
    const encrypted = CryptoJS.HmacSHA256(fullData, appSecret);

    const signature = CryptoJS.enc.Base64.stringify(encrypted);
    console.log("signature = " + signature);
    return "AILE " + appId + ":" + signature;
}

// 為保證請求方傳遞的原始數據參數順序與接收方一致，請求方做了一次參數排序
function canonicalJson(obj) {
    const sortedKeys = Object.keys(obj).sort();
    const result = {};
    for (const key of sortedKeys) {
        result[key] = obj[key];
    }
    return JSON.stringify(result);
}