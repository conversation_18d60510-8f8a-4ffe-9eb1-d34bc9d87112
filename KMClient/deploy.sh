#!/bin/bash

# KM Client 部署腳本
# 用於自動化構建和部署流程

set -e

echo "🚀 開始部署 KM Client..."

# 檢查 Node.js 版本
echo "📋 檢查環境..."
node_version=$(node -v)
echo "Node.js 版本: $node_version"

# 安裝依賴
echo "📦 安裝依賴..."
npm ci

# 運行測試（如果有）
if [ -f "package.json" ] && grep -q "\"test\"" package.json; then
    echo "🧪 運行測試..."
    npm test
fi

# 構建專案
echo "🔨 構建專案..."
npm run build

# 檢查構建結果
if [ -d "dist" ]; then
    echo "✅ 構建成功！"
    echo "📊 構建統計:"
    du -sh dist
    ls -la dist
else
    echo "❌ 構建失敗！"
    exit 1
fi

# 可選：部署到服務器
if [ "$1" = "--deploy" ]; then
    echo "🌐 部署到服務器..."
    # 這裡可以添加實際的部署邏輯
    # 例如：rsync, scp, 或其他部署工具
    echo "部署邏輯需要根據實際環境配置"
fi

echo "🎉 部署完成！"
echo "📝 訪問地址: http://your-domain.com"
echo "📋 記得配置正確的 URL 參數: ?TenantID=xxx&ServiceID=xxx"
