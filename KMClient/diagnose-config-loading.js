#!/usr/bin/env node

/**
 * 配置載入診斷腳本
 * 檢查配置載入順序和 API 服務初始化
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 配置載入診斷開始...');
console.log('');

// 檢查配置文件
const configPath = path.join(__dirname, 'public/config.json');
const distConfigPath = path.join(__dirname, 'dist/config.json');

console.log('📁 檢查配置文件：');
console.log(`   public/config.json: ${fs.existsSync(configPath) ? '✅ 存在' : '❌ 不存在'}`);
console.log(`   dist/config.json: ${fs.existsSync(distConfigPath) ? '✅ 存在' : '❌ 不存在'}`);
console.log('');

// 讀取並比較配置文件
if (fs.existsSync(configPath)) {
  try {
    const publicConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    console.log('📋 public/config.json 內容：');
    console.log(`   baseUrl: ${publicConfig.api.gateway.baseUrl}`);
    console.log(`   allowedOrigins: ${JSON.stringify(publicConfig.security.allowedOrigins)}`);
  } catch (error) {
    console.error('❌ 讀取 public/config.json 失敗:', error.message);
  }
}

if (fs.existsSync(distConfigPath)) {
  try {
    const distConfig = JSON.parse(fs.readFileSync(distConfigPath, 'utf8'));
    console.log('📋 dist/config.json 內容：');
    console.log(`   baseUrl: ${distConfig.api.gateway.baseUrl}`);
    console.log(`   allowedOrigins: ${JSON.stringify(distConfig.security.allowedOrigins)}`);
  } catch (error) {
    console.error('❌ 讀取 dist/config.json 失敗:', error.message);
  }
}

console.log('');

// 檢查環境變量
console.log('🌍 環境變量檢查：');
console.log(`   VITE_API_BASE_URL: ${process.env.VITE_API_BASE_URL || '未設置'}`);
console.log(`   VITE_APP_URL: ${process.env.VITE_APP_URL || '未設置'}`);
console.log(`   NODE_ENV: ${process.env.NODE_ENV || '未設置'}`);
console.log('');

// 檢查 package.json 腳本
const packagePath = path.join(__dirname, 'package.json');
if (fs.existsSync(packagePath)) {
  try {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    console.log('📦 package.json 腳本：');
    console.log(`   dev: ${packageJson.scripts?.dev || '未定義'}`);
    console.log(`   build: ${packageJson.scripts?.build || '未定義'}`);
    console.log(`   preview: ${packageJson.scripts?.preview || '未定義'}`);
  } catch (error) {
    console.error('❌ 讀取 package.json 失敗:', error.message);
  }
}

console.log('');
console.log('🔧 建議的修復步驟：');
console.log('1. 確保 public/config.json 中的 baseUrl 設置正確');
console.log('2. 重新啟動開發服務器：npm run dev');
console.log('3. 檢查瀏覽器控制台中的配置載入日誌');
console.log('4. 確認 API 請求使用正確的 baseURL');
console.log('');
console.log('🌐 測試 URL:');
console.log('   http://localhost:3001/?TenantID=R20230704-0001&ServiceID=dbc2cd12-3d42-1bbe-6728-0b03b2c19440&UserID=00000000-0000-0000-1002-000000000001');
console.log('');
console.log('✅ 診斷完成！');
