import { test, expect } from '@playwright/test';

// 依據 App 初始化要求，URL 需要帶上 TenantID 與 ServiceID
const qs = '?TenantID=test-tenant&ServiceID=test-service&UserID=test-user';

test('home loads and shows layout', async ({ page }) => {
  await page.goto('/' + qs);
  await expect(page).toHaveURL(new RegExp(`/\?TenantID=`));
  // 主畫面應該包含附件管理和聊天區域的一些關鍵元素
  await expect(page.getByRole('heading', { name: /附件管理/i })).toBeVisible();
});

test('system instructions page renders', async ({ page }) => {
  await page.goto('/system-instructions' + qs);
  await expect(page.getByRole('heading', { name: /系統指令設置/i })).toBeVisible();
});

test('theme demo renders', async ({ page }) => {
  await page.goto('/theme-demo' + qs);
  await expect(page.getByText('React Bits + 主題切換演示')).toBeVisible();
});

test('motion demo renders', async ({ page }) => {
  await page.goto('/motion-demo' + qs);
  await expect(page.getByText('Framer Motion 動效演示')).toBeVisible();
});

