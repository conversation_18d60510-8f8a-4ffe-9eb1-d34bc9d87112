# KM Client 環境變量示例文件
# 複製此文件為 .env 並根據需要修改配置

# API 基礎 URL（可選，會覆蓋 config.json 中的設置）
# 開發環境示例
VITE_API_BASE_URL=http://localhost:8083

# 測試環境示例
# VITE_API_BASE_URL=http://test-api.example.com

# 生產環境示例
# VITE_API_BASE_URL=https://api.example.com

# 應用 URL（用於 CORS 配置）
VITE_APP_URL=http://localhost:3001

# 應用環境
VITE_APP_ENV=development

# 注意：
# 1. 主要配置應該在 public/config.json 中設置
# 2. 環境變量主要用於不同環境的快速切換
# 3. config.json 的配置優先級高於環境變量
