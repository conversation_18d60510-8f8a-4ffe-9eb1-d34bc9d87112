#!/bin/bash

# 部署前端到 ngrok 的腳本
# 這樣前端和後端就在同一個域下，避免 CORS 問題

echo "🚀 開始部署前端到 ngrok..."

# 1. 構建前端
echo "📦 構建前端..."
npm run build

if [ ! -d "dist" ]; then
    echo "❌ 構建失敗，dist 目錄不存在"
    exit 1
fi

# 2. 啟動本地服務器
echo "🌐 啟動本地服務器..."
cd dist

# 使用 Python 啟動簡單的 HTTP 服務器
if command -v python3 &> /dev/null; then
    echo "使用 Python3 啟動服務器..."
    python3 -m http.server 8080 &
    SERVER_PID=$!
elif command -v python &> /dev/null; then
    echo "使用 Python 啟動服務器..."
    python -m SimpleHTTPServer 8080 &
    SERVER_PID=$!
else
    echo "❌ 未找到 Python，請安裝 Python 或使用其他 HTTP 服務器"
    exit 1
fi

echo "✅ 本地服務器已啟動，PID: $SERVER_PID"
echo "📍 本地地址: http://localhost:8080"

# 3. 使用 ngrok 暴露服務
echo "🌍 使用 ngrok 暴露服務..."
if command -v ngrok &> /dev/null; then
    echo "啟動 ngrok..."
    ngrok http 8080 &
    NGROK_PID=$!
    
    echo "✅ ngrok 已啟動，PID: $NGROK_PID"
    echo "🔗 請查看 ngrok 控制台獲取公網地址"
    echo "📋 通常地址格式為: https://xxxxx.ngrok-free.app"
    
    # 等待用戶按鍵停止服務
    echo ""
    echo "按任意鍵停止服務..."
    read -n 1
    
    # 停止服務
    echo "🛑 停止服務..."
    kill $SERVER_PID 2>/dev/null
    kill $NGROK_PID 2>/dev/null
    
    echo "✅ 服務已停止"
else
    echo "❌ 未找到 ngrok，請先安裝 ngrok"
    echo "安裝方法: https://ngrok.com/download"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi
