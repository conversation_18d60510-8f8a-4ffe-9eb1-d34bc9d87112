<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多文件上傳修復測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 多文件上傳修復測試</h1>
    
    <div class="test-section info">
        <h2>📋 測試目標</h2>
        <ul>
            <li>✅ 修復 <code>validateUploadRequest is not a function</code> 錯誤</li>
            <li>✅ 驗證 content_type 參數格式正確</li>
            <li>✅ 測試單文件和多文件上傳功能</li>
            <li>✅ 確保 FormData 構建正確</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🧪 功能測試</h2>
        
        <h3>1. 單文件上傳測試</h3>
        <input type="file" id="singleFile" accept="*/*">
        <button onclick="testSingleUpload()">測試單文件上傳</button>
        
        <h3>2. 多文件上傳測試</h3>
        <input type="file" id="multipleFiles" multiple accept="*/*">
        <button onclick="testMultipleUpload()">測試多文件上傳</button>
        
        <h3>3. content_type 格式驗證</h3>
        <button onclick="testContentTypeGeneration()">測試 content_type 生成</button>
    </div>

    <div class="test-section">
        <h2>📊 測試結果</h2>
        <div id="testResults" class="log">等待測試執行...</div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            resultsDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testResults').textContent = '';
        }

        // 模擬 content_type 生成邏輯
        function generateContentType(files) {
            return files.map(file => file.type || 'application/octet-stream').join(',');
        }

        // 模擬 validateUploadRequest 函數
        function validateUploadRequest(request) {
            if (!request.file || request.file.length === 0) {
                throw new Error('文件數組不能為空');
            }

            if (!request.content_type) {
                throw new Error('content_type 不能為空');
            }

            // 驗證 content_type 格式
            const contentTypes = request.content_type.split(',');
            if (contentTypes.length !== request.file.length) {
                throw new Error(`content_type 中的 MIME 類型數量 (${contentTypes.length}) 與文件數量 (${request.file.length}) 不匹配`);
            }

            // 驗證每個文件對象
            request.file.forEach((file, index) => {
                if (!(file instanceof File)) {
                    throw new Error(`第 ${index + 1} 個文件不是有效的 File 對象`);
                }

                if (!file.name) {
                    throw new Error(`第 ${index + 1} 個文件沒有名稱`);
                }
            });
        }

        function testSingleUpload() {
            clearLog();
            log('開始單文件上傳測試...');
            
            const fileInput = document.getElementById('singleFile');
            const files = Array.from(fileInput.files);
            
            if (files.length === 0) {
                log('請先選擇一個文件', 'error');
                return;
            }

            try {
                const contentType = generateContentType(files);
                log(`生成的 content_type: ${contentType}`);
                
                const request = {
                    file: files,
                    tenant_id: 'test-tenant',
                    service_id: 'test-service',
                    user_id: 'test-user',
                    content_type: contentType
                };

                validateUploadRequest(request);
                log('✅ 單文件上傳驗證通過', 'success');
                log(`文件信息: ${files[0].name} (${files[0].type || 'application/octet-stream'})`);
                
            } catch (error) {
                log(`單文件上傳驗證失敗: ${error.message}`, 'error');
            }
        }

        function testMultipleUpload() {
            clearLog();
            log('開始多文件上傳測試...');
            
            const fileInput = document.getElementById('multipleFiles');
            const files = Array.from(fileInput.files);
            
            if (files.length === 0) {
                log('請先選擇多個文件', 'error');
                return;
            }

            try {
                const contentType = generateContentType(files);
                log(`生成的 content_type: ${contentType}`);
                log(`文件數量: ${files.length}`);
                
                files.forEach((file, index) => {
                    log(`文件 ${index + 1}: ${file.name} (${file.type || 'application/octet-stream'})`);
                });
                
                const request = {
                    file: files,
                    tenant_id: 'test-tenant',
                    service_id: 'test-service',
                    user_id: 'test-user',
                    content_type: contentType
                };

                validateUploadRequest(request);
                log('✅ 多文件上傳驗證通過', 'success');
                
            } catch (error) {
                log(`多文件上傳驗證失敗: ${error.message}`, 'error');
            }
        }

        function testContentTypeGeneration() {
            clearLog();
            log('開始 content_type 格式驗證測試...');
            
            // 測試案例
            const testCases = [
                {
                    name: '單個 PDF 文件',
                    files: [{ name: 'test.pdf', type: 'application/pdf' }],
                    expected: 'application/pdf'
                },
                {
                    name: '多個不同類型文件',
                    files: [
                        { name: 'doc.pdf', type: 'application/pdf' },
                        { name: 'image.jpg', type: 'image/jpeg' },
                        { name: 'text.txt', type: 'text/plain' }
                    ],
                    expected: 'application/pdf,image/jpeg,text/plain'
                },
                {
                    name: '包含無類型文件',
                    files: [
                        { name: 'doc.pdf', type: 'application/pdf' },
                        { name: 'unknown', type: '' }
                    ],
                    expected: 'application/pdf,application/octet-stream'
                }
            ];

            testCases.forEach((testCase, index) => {
                log(`測試案例 ${index + 1}: ${testCase.name}`);
                
                const result = testCase.files.map(file => file.type || 'application/octet-stream').join(',');
                
                if (result === testCase.expected) {
                    log(`  ✅ 通過: ${result}`, 'success');
                } else {
                    log(`  ❌ 失敗: 期望 "${testCase.expected}", 實際 "${result}"`, 'error');
                }
            });
            
            log('content_type 格式驗證測試完成');
        }

        // 頁面載入時顯示歡迎信息
        window.onload = function() {
            log('🚀 多文件上傳修復測試頁面已載入');
            log('請選擇文件並點擊相應的測試按鈕');
        };
    </script>
</body>
</html>
