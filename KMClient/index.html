<!DOCTYPE html>
<html lang="zh-TW">
  <head>
    <meta charset="UTF-8" />

    <!-- Favicon 配置 - 支援多種格式和尺寸 -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="shortcut icon" href="/favicon.ico" />

    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />

    <!-- Android Chrome -->
    <link rel="manifest" href="/site.webmanifest" />
    <meta name="theme-color" content="#00d4ff" />

    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>KM Client - 知識管理客戶端</title>
    <meta name="description" content="專注於系統指令設置和智慧聊天功能的知識管理客戶端" />
    
    <!-- 預載入字體 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
      /* 載入畫面樣式 - 科技感設計 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(0, 212, 255, 0.2);
        border-top: 3px solid #00d4ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: #00d4ff;
        font-family: 'Inter', sans-serif;
        font-size: 18px;
        font-weight: 500;
        text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        animation: glow 2s ease-in-out infinite alternate;
      }
      
      @keyframes glow {
        0% { text-shadow: 0 0 10px rgba(0, 212, 255, 0.5); }
        100% { text-shadow: 0 0 20px rgba(0, 212, 255, 0.8); }
      }
    </style>
  </head>
  <body>
    <!-- React 應用掛載點 -->
    <div id="root">
      <div id="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">🧠 KM Client 載入中...</div>
      </div>
    </div>
    
    <script type="module" src="/src/main.tsx"></script>
    
    <script>
      console.log('KM Client HTML script loaded');

      // 檢查 React 是否載入成功
      setTimeout(function() {
        const loading = document.getElementById('loading');
        if (loading && loading.style.display !== 'none') {
          console.error('React 應用可能載入失敗');
        }
      }, 5000);
    </script>
  </body>
</html>
