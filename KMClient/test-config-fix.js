#!/usr/bin/env node

/**
 * 測試配置修復效果
 * 驗證配置載入順序和 API 請求是否使用正確的 baseURL
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 開始測試配置修復效果...');
console.log('');

// 讀取當前配置
const configPath = path.join(__dirname, 'public/config.json');
if (fs.existsSync(configPath)) {
  try {
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    console.log('📋 當前配置文件內容：');
    console.log(`   baseUrl: ${config.api.gateway.baseUrl}`);
    console.log(`   app.name: ${config.app.name}`);
    console.log('');
  } catch (error) {
    console.error('❌ 讀取配置文件失敗:', error.message);
  }
}

console.log('🔧 修復內容總結：');
console.log('');

console.log('1. ✅ 修復了 configService 初始化順序問題');
console.log('   - 添加了 ensureConfigLoaded() 方法');
console.log('   - 確保配置在 apiService 創建前載入');
console.log('');

console.log('2. ✅ 修復了 apiService 配置更新邏輯');
console.log('   - 在構造函數中等待配置載入完成');
console.log('   - 正確處理配置熱重載');
console.log('');

console.log('3. ✅ 移除了所有硬編碼 URL');
console.log('   - DEFAULT_CONFIG 使用環境變量');
console.log('   - config.json 使用動態配置');
console.log('');

console.log('🚀 測試步驟：');
console.log('1. 重新啟動開發服務器：npm run dev');
console.log('2. 打開瀏覽器開發者工具');
console.log('3. 訪問測試 URL');
console.log('4. 檢查控制台日誌：');
console.log('   - 應該看到 "🔄 ApiService 使用載入的配置更新"');
console.log('   - API 請求應該使用 https://aile-ai-product.web.app/');
console.log('5. 檢查網絡面板：');
console.log('   - 所有 API 請求的 URL 應該以正確的 baseURL 開始');
console.log('');

console.log('🌐 測試 URL:');
console.log('http://localhost:3001/?TenantID=R20230704-0001&ServiceID=dbc2cd12-3d42-1bbe-6728-0b03b2c19440&UserID=00000000-0000-0000-1002-000000000001');
console.log('');

console.log('🔍 預期結果：');
console.log('- 控制台顯示配置載入成功');
console.log('- API 請求使用 https://aile-ai-product.web.app/ 而不是 localhost');
console.log('- 配置熱重載正常工作');
console.log('- 頁面標題顯示配置中的 app.name');
console.log('');

console.log('✅ 測試腳本完成！請按照上述步驟進行驗證。');
